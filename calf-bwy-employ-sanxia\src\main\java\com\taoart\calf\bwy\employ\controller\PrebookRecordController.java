package com.taoart.calf.bwy.employ.controller;

import com.opensymphony.oscache.util.StringUtil;
import com.taoart.calf.bwy.domain.ImportFileLog;
import com.taoart.calf.bwy.domain.TicketEmploy;
import com.taoart.calf.bwy.employ.Constant;
import com.taoart.calf.bwy.employ.IgnoreAuth;
import com.taoart.calf.bwy.employ.SessionContextUtils;
import com.taoart.calf.bwy.service.EmployEnterpriseAuthService;
import com.taoart.calf.bwy.service.ImportFileLogService;
import com.taoart.calf.bwy.service.TicketUserService;
import com.taoart.calf.wypw.domain.*;
import com.taoart.common.bean.ExcelBean;
import com.taoart.common.bean.JsonResponse;
import com.taoart.common.bean.PageBean;
import com.taoart.common.utils.*;
import com.taoart.common.web.RequestThreadLocal;
import com.taoart.domain.user.User;
import com.taoart.wypw.sale.api.*;
import org.apache.commons.io.FileUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jxls.common.Context;
import org.jxls.util.JxlsHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName: PrebookRecordController
 * @Description: 预约记录
 * <AUTHOR>
 * @Date 2020年03月20日 11:21:21
 * @Modify
 * @CopyRight 杭州淘艺数据技术有限公司
 */
@Controller
@RequestMapping("prebook/record")
public class PrebookRecordController extends DefaultController {

    @Autowired
    private PrebookInfoConfigSaleServiceApi prebookInfoConfigService;
    @Autowired
    private VisitorSaleServiceApi visitorSaleServiceApi;
    @Autowired
    private PrebookRecordSaleServiceApi prebookRecordSaleServiceApi;
    @Autowired
    private TicketUserService ticketUserService;
    @Autowired
    private EnterpriseSaleServiceApi enterpriseSaleServiceApi;
    @Autowired
    private PrebookRecordExportSaleServiceApi prebookRecordExportSaleServiceApi;
    @Autowired
    private PrebookCertificateServiceApi prebookCertificateService;
    @Autowired
    private TicketOrderSaleServiceApi ticketOrderSaleServiceApi;
    @Autowired
    private TicketOrderRelationSaleServiceApi ticketOrderRelationSaleServiceApi;
    @Autowired
    private EmployEnterpriseAuthService employEnterpriseAuthService;
    @Autowired
    private ImportTeamAppointmentServiceApi importTeamAppointmentServiceApi;
    @Autowired
    private ImportFileLogService importFileLogService;

    private static final int[] factorArray = {3, 5, 7, 2, 9, 8, 6, 1, 1, 4, 3, 2, 7, 5, 8, 6, 2, 1, 2, 3};

    private static final Log log = LogFactory.getLog(PrebookRecordController.class);


    /**
     * 此方法用于解密预约
     *
     * @param code
     * @return
     */
    @RequestMapping("decrypt")
    @ResponseBody
    @IgnoreAuth
    public String decrypt(@RequestParam(defaultValue = "") String code) {
        return AesEncodeUtil.decrypt(code);
    }


    /**
     * 预约记录列表
     *
     * @Title: prebookRecordList
     * @Description: 预约记录列表
     * @Param @return
     * @Return ModelAndView
     * @Throws
     */
    @RequestMapping("list")
    public ModelAndView prebookRecordList(@RequestParam(defaultValue = "0") int srcType, String startDate, String endDate, @RequestParam(defaultValue = "0") int status, String identification,
                                          String periodStart, String periodEnd, String createTime, @RequestParam(defaultValue = "1") int pageNum, String visitName, @RequestParam(defaultValue = "0") int slaveEnterpriseId, Integer enterpriseId) {
        ModelAndView mav = new ModelAndView("admin/prebook_record_list");
        if (pageNum == 0) {
            pageNum = 1;
        }
        TicketEmploy employee = (TicketEmploy) SessionContextUtils.getLoginObject();
        List<Enterprise> enterpriseList = employEnterpriseAuthService.enterpriseListByEmployId(employee.getId());
        mav.addObject("srcType", srcType);
        //数据量过大导致列表加载很慢,给个预约时间默认值
        if (org.apache.commons.lang.StringUtils.isBlank(startDate) || org.apache.commons.lang.StringUtils.isBlank(endDate)) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = CalfDateUtil.localDateToStr(LocalDate.now());
        }
        mav.addObject("startDate", startDate);
        mav.addObject("endDate", endDate);
        mav.addObject("status", status);
        mav.addObject("identification", identification);
        mav.addObject("periodStart", periodStart);
        mav.addObject("periodEnd", periodEnd);
        mav.addObject("createTime", createTime);
        Map<String, Object> params = new HashMap<String, Object>();
//        Enterprise enterprise = enterpriseSaleServiceApi.selectById(employee.getEnterpriseId());
        if (!CollectionUtils.isEmpty(enterpriseList)) {
            if (slaveEnterpriseId == 0) {
                slaveEnterpriseId = employee.getEnterpriseId();
            }
            if(slaveEnterpriseId == -1){
                List<Integer> enterpriseIds = enterpriseList.stream().map(Enterprise::getId).collect(Collectors.toList());
                params.put("enterpriseIds", enterpriseIds);
            }else {
                params.put("enterpriseId", slaveEnterpriseId);
            }
            mav.addObject("slaveEnterpriseId", slaveEnterpriseId);
            fillEnterpriseList(enterpriseList);
            mav.addObject("enterpriseList", enterpriseList);
        } else {
            slaveEnterpriseId = employee.getEnterpriseId();
        }
        List<Enterprise> enterprises = enterpriseSaleServiceApi.listAllEnterprise();
        mav.addObject("enterprises", enterprises);

        params.put("srcType", srcType);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("status", status);
        params.put("identification", identification);
        params.put("periodStart", periodStart);
        params.put("periodEnd", periodEnd);
        params.put("createTime", createTime);
        if (!StringUtils.isBlank(createTime)) {
            params.put("createTimeStart", createTime + " 00:00:00");
            params.put("createTimeEnd", createTime + " 23:59:59");
        }
        PageBean<Visitor> list = visitorSaleServiceApi.listByEnterpriseIdParams(params, pageNum, Constant.PAGE_SIZE);
        if (list.getResult().size() > 0) {
            list.setResult(visitorListEncryption(list.getResult()));
        }
        mav.addObject("pageBean", list);
//        mav.addObject("enterpriseId", enterpriseId);
        return mav;
    }

    private void fillEnterpriseList(List<Enterprise> enterpriseList) {
        Enterprise enterprise = new Enterprise();
        enterprise.setId(-1);
        enterprise.setName("全部");
        enterpriseList.add(0,enterprise);
    }


    /**
     * 进馆记录列表
     *
     * @Title: signRecordList
     * @Description: 进馆记录列表
     * @Param @return
     * @Return ModelAndView
     * @Throws
     */
    @RequestMapping("sign-list")
    public ModelAndView signRecordList(String startDate, String endDate, String identification, @RequestParam(defaultValue = "1") int pageNum, String visitName,
                                       @RequestParam(defaultValue = "0") int signinMode, @RequestParam(defaultValue = "0") int slaveEnterpriseId, Integer enterpriseId) {
        ModelAndView mav = new ModelAndView("admin/sign_record_list");
        if (pageNum == 0) {
            pageNum = 1;
        }
        TicketEmploy employee = (TicketEmploy) SessionContextUtils.getLoginObject();
        List<Enterprise> enterpriseList = employEnterpriseAuthService.enterpriseListByEmployId(employee.getId());
        //数据量过大导致列表加载很慢,给个预约时间默认值
        if (org.apache.commons.lang.StringUtils.isBlank(startDate) || org.apache.commons.lang.StringUtils.isBlank(endDate)) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = CalfDateUtil.localDateToStr(LocalDate.now());
        }
        mav.addObject("startDate", startDate);
        mav.addObject("endDate", endDate);
        mav.addObject("identification", identification);
        mav.addObject("visitName", visitName);
        mav.addObject("signinMode", signinMode);
        Map<String, Object> params = new HashMap<String, Object>();
        Enterprise enterprise = enterpriseSaleServiceApi.selectById(employee.getEnterpriseId());
        if (!CollectionUtils.isEmpty(enterpriseList)) {
            mav.addObject("enterpriseList", enterpriseList);
            if (slaveEnterpriseId == 0) {
                slaveEnterpriseId = employee.getEnterpriseId();
            }
        } else {
            slaveEnterpriseId = employee.getEnterpriseId();
        }


        mav.addObject("slaveEnterpriseId", slaveEnterpriseId);
        if (org.apache.commons.lang.StringUtils.isNotBlank(startDate)) {
            startDate = startDate + " 00:00:00";
        }
        if (org.apache.commons.lang.StringUtils.isNotBlank(endDate)) {
            endDate = endDate + " 23:59:59";
        }
        List<Enterprise> enterprises = enterpriseSaleServiceApi.listAllEnterprise();
        mav.addObject("enterprises", enterprises);
        params.put("enterpriseId", slaveEnterpriseId);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("identification", identification);
        params.put("visitName", visitName);
        params.put("signinMode", signinMode);
        PageBean<Visitor> list = visitorSaleServiceApi.listSignByParams(params, pageNum, Constant.PAGE_SIZE);
        if (list.getResult().size() > 0) {
            list.setResult(visitorListEncryption(list.getResult()));
        }
        mav.addObject("pageBean", list);
        mav.addObject("enterpriseId", enterpriseId);
        return mav;
    }

    /**
     * 预约记录详情
     *
     * @Title: editPrebookPeriod
     * @Description: 预约记录详情
     * @Param @return
     * @Return ModelAndView
     * @Throws
     */
    @RequestMapping("detail")
    public ModelAndView prebookRecordDetail(@RequestParam(defaultValue = "0") int id) {
        ModelAndView mav = new ModelAndView("admin/prebook_record_detail");
        Visitor visitor = visitorSaleServiceApi.selectById(id);
        if (visitor == null) {
            return new ModelAndView("redirect:/auth/no-auth.htm");
        }
        TicketEmploy employee = (TicketEmploy) SessionContextUtils.getLoginObject();
        PrebookInfoConfig p = prebookInfoConfigService.selectByEnterpriseId(employee.getEnterpriseId());
        int prebookRecordId = visitor.getPrebookRecordId();
        if (prebookRecordId > 0) {
            PrebookRecord prebookRecord = prebookRecordSaleServiceApi.selectById(prebookRecordId);
            mav.addObject("prebookRecord", prebookRecord);
            if (prebookRecord != null) {
                int userId = prebookRecord.getUserId();
                User user = ticketUserService.selectById(userId);
                if (user != null) {
                    //矫正数据
                    //游客联系方式
                    visitor.setVisitorMobile(visitor.getMobile());
                    //游客预约手机
                    visitor.setMobile(user.getMobile());
                }
                if (prebookRecord.getType() == 2) {
                    PrebookCertificate prebookCertificate = prebookCertificateService.selectByUserIdAndEnterpriseId(user.getId(), prebookRecord.getEnterpriseId());
                    mav.addObject("prebookCertificate", prebookCertificate);
                }
            }
        }
        mav.addObject("dto", visitorEncryption(visitor));
        // 二维码生成
        String code = "{\"type\": 4,\"info\":\"" + id + "\"}";
        code = "TYS" + AesEncodeUtil.encrypt(code);
        mav.addObject("code", code);
        return mav;
    }

    /**
     * 进馆记录详情
     *
     * @Title: signRecordDetail
     * @Description: 进馆记录详情
     * @Param @return
     * @Return ModelAndView
     * @Throws
     */
    @RequestMapping("sign-detail")
    public ModelAndView signRecordDetail(@RequestParam(defaultValue = "0") int id) {
        ModelAndView mav = new ModelAndView("admin/sign_record_detail");
        Visitor visitor = visitorSaleServiceApi.selectById(id);
        if (visitor == null) {
            return new ModelAndView("redirect:/auth/no-auth.htm");
        }
        TicketEmploy employee = (TicketEmploy) SessionContextUtils.getLoginObject();
        mav.addObject("dto", visitorEncryption(visitor));
        return mav;
    }

    @RequestMapping("do-cancel")
    @ResponseBody
    public JsonResponse doCancelPrebookRecord(@RequestParam(defaultValue = "0") int id) {
        JsonResponse json = this.success();
        Visitor visitor = visitorSaleServiceApi.selectById(id);
        if (visitor == null) {
            return this.fail("记录不存在!");
        }
        if (visitor.getStatus() != 1) {
            return this.fail("该记录不处于待使用状态,不能取消!");
        }
        TicketEmploy loginEmployee = (TicketEmploy) SessionContextUtils.getLoginObject();
        visitorSaleServiceApi.cancelPrebook(id);
        return json;
    }

    /**
     * 后台添加入馆人数
     *
     * @param addCount
     * @param addDate
     * @return
     */
    @RequestMapping("do-signIn")
    @ResponseBody
    public JsonResponse doSignIn(@RequestParam(defaultValue = "0") int addCount, @RequestParam(defaultValue = "") String addDate) {
        Map<String, String> map = new HashMap<>();
        TicketEmploy loginEmployee = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (StringUtils.isBlank(addDate)) {
            return this.fail("请选择进馆时间");
        }
        map.put("codeScope", "1");
        map.put("srcType", "100");
        map.put("enterpriseId", loginEmployee.getEnterpriseId() + "");
        String code = "{\"type\": 7,\"info\":\"" + addCount + "\"}";
        map.put("userCode", AesEncodeUtil.encrypt(code));
        map.put("addDate", DateUtils.dateToStr(new Date(), "yyyy-MM-dd") + " " + addDate);

        JsonResponse json = visitorSaleServiceApi.visitorSignin(map);
        return json;
    }

    @RequestMapping("visitorsCancel")
    @ResponseBody
    public JsonResponse visitorsCancel(@RequestParam(defaultValue = "0") String ids) {
        TicketEmploy loginEmployee = (TicketEmploy) SessionContextUtils.getLoginObject();
        try {
            List<Visitor> list = new ArrayList<>();
            String[] split = ids.split(",");
            for (String id : split) {
                Visitor visitor = visitorSaleServiceApi.selectById(Integer.parseInt(id));
                if (visitor != null && visitor.getStatus() == 1) {
                    if (visitor.getEnterpriseId() != loginEmployee.getEnterpriseId()) {
                        return this.fail("无权限！");
                    }
                    list.add(visitor);
                }
            }
            visitorSaleServiceApi.batchCancelPrebook(list);
        } catch (Exception e) {
            log.error(e.getMessage());
            return this.fail(e.getMessage());
        }
        return this.success();
    }

    @RequestMapping("visitorsSignIn")
    @ResponseBody
    public JsonResponse visitorsSignIn(@RequestParam(defaultValue = "0") String ids) {
        TicketEmploy loginEmployee = (TicketEmploy) SessionContextUtils.getLoginObject();
        try {
            List<Visitor> list = new ArrayList<>();
            String[] split = ids.split(",");
            for (String id : split) {
                Visitor visitor = visitorSaleServiceApi.selectById(Integer.parseInt(id));
                if (visitor != null && visitor.getStatus() == 1 && DateUtils.dateToStr(new Date(), "yyyy-MM-dd")
                        .equals(DateUtils.dateToStr(visitor.getPreviewDate(), "yyyy-MM-dd"))) {
                    if (visitor.getEnterpriseId() != loginEmployee.getEnterpriseId()) {
                        return this.fail("无权限！");
                    }
                    list.add(visitor);
                }
            }
            if(list.size()>0){
                visitorSaleServiceApi.batchSignIn(list);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return this.fail(e.getMessage());
        }
        return this.success();
    }

    @RequestMapping("record-list-excel")
    @IgnoreAuth(requireLogin = true)
    public ResponseEntity<byte[]> excelPrebookRecord(HttpServletResponse response, HttpServletRequest request, String startDate, String endDate,
                                                     @RequestParam(defaultValue = "0") int slaveEnterpriseId) throws IOException {
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (!DateUtil.isWithinThreeMonths(startDate)) {
            response.setContentType("text/html;charset=utf-8");
            PrintWriter errorInfo = response.getWriter();
            errorInfo.print("<script type='text/javascript'> alert('最多近3个月数据')</script>");
            errorInfo.flush();
            errorInfo.close();
            return null;
        }
        ClassLoader classLoader = getClass().getClassLoader();
        /**
         * getResource()方法会去classpath下找这个文件，获取到url resource,
         * 得到这个资源后，调用url.getFile获取到 文件 的绝对路径
         */
        URL url = classLoader.getResource("excel/excel-template/prebook_record_list_template.xls");
        URL url2 = classLoader.getResource("excel/prebook_record_list_temp.xls");
        String num = RandomUtils.generateRandomNumber(5);
        new File(url2.getPath().replace(".xls", num + ".xls"));
        InputStream in = new FileInputStream(url.getPath());
        FileOutputStream out = new FileOutputStream(url2.getPath().replace(".xls", num + ".xls"));
        ExcelBean excelBean = new ExcelBean();
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("enterpriseId", slaveEnterpriseId);
        String dateStr = startDate + "至" + endDate;

        params.put("startDate", startDate);
        params.put("endDate", endDate);
        Integer count = visitorSaleServiceApi.countForRecordExport(params);
        if (count > 65000) {
            // 导出失败!导出数据条数超规格(单次最多65000条),请缩小导出范围
            log.error("预约进馆数据超规格导出失败,导出数据条数:" + count);
            response.setContentType("text/html;charset=utf-8");
            PrintWriter errorInfo = response.getWriter();
            errorInfo.print("<script type='text/javascript'> alert('导出条数超出限制(单次最多65000条)，请缩小导出范围')</script>");
            errorInfo.flush();
            errorInfo.close();
            return null;
        }
        List<Visitor> retList = visitorSaleServiceApi.listForRecordExport(params);
        for (Visitor visitor : retList) {
            visitor.setCreateTimeStr(DateUtils.dateToStr(visitor.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
            visitor.setPreviewDateStr(DateUtils.dateToStr(visitor.getPreviewDate(), "yyyy-MM-dd"));
            if (visitor.getSigninTime() != null) {
                visitor.setSigninTimeStr(DateUtils.dateToStr(visitor.getSigninTime(), "yyyy-MM-dd HH:mm:ss"));
            }
            if (visitor.getStatus() == 1) {
                visitor.setStatusStr("待使用");
            } else if (visitor.getStatus() == 2) {
                visitor.setStatusStr("已使用");
            } else if (visitor.getStatus() == 3) {
                visitor.setStatusStr("已取消");
                visitor.setCancelTimeStr(DateUtils.dateToStr(visitor.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
            }

            if (visitor.getSrcType() == 1) {
                visitor.setSrcTypeStr("身份证");
            } else if (visitor.getSrcType() == 5) {
                visitor.setSrcTypeStr("护照");
            } else if (visitor.getSrcType() == 6) {
                visitor.setSrcTypeStr("台胞证");
            } else if (visitor.getSrcType() == 7) {
                visitor.setSrcTypeStr("港澳通行证");
            } else if (visitor.getSrcType() == 9) {
                visitor.setSrcTypeStr("市民卡");
            } else if (visitor.getSrcType() == 10) {
                visitor.setSrcTypeStr("其他");
            }

            if (visitor.getRecordType() == 1) {
                visitor.setRecordTypeStr("个人");
                visitor.setTeamName("无");
            } else if (visitor.getRecordType() == 2) {
                visitor.setRecordTypeStr("团队");
            }
        }
        visitorListEncryption(retList);
        try {
            Context context = new Context();
            excelBean.setListData(retList);
            context.putVar("excelBean", excelBean);
            context.putVar("dateStr", "预约数据(" + dateStr + ")");
            JxlsHelper.getInstance().processTemplate(in, out, context);
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        File f = null;
        f = new File(url2.getPath().replace(".xls", num + ".xls")); // 将服务器上的文件下载下来
        HttpHeaders headers = new HttpHeaders();
        String fileName = new String(("预约数据(" + dateStr + ").xls").getBytes("UTF-8"), "iso-8859-1");
        headers.setContentType(MediaType.valueOf("application/x-excel;charset=UTF-8"));
        headers.add("Content-Disposition", "attachment; filename=" + fileName);
        headers.add("Content-Length", String.valueOf(f.length()));
        byte[] b = FileUtils.readFileToByteArray(f);
        if (f.exists())
            f.delete();
        PrebookRecordExport prebookRecordExport = new PrebookRecordExport();
        prebookRecordExport.setEnterpriseId(employ.getEnterpriseId());
        prebookRecordExport.setOperId(employ.getId());
        prebookRecordExport.setTimeRange(dateStr);
        prebookRecordExport.setCreateTime(new Date());
        prebookRecordExport.setUpdateTime(new Date());
        prebookRecordExportSaleServiceApi.insert(prebookRecordExport);
        return new ResponseEntity<byte[]>(b, headers, HttpStatus.OK);
    }

    @RequestMapping("sign-list-excel")
    @IgnoreAuth(requireLogin = true)
    public ResponseEntity<byte[]> excelVisit(HttpServletResponse response, HttpServletRequest request, String startDate, String endDate,
                                             @RequestParam(defaultValue = "0") int slaveEnterpriseId) throws IOException {
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        ClassLoader classLoader = getClass().getClassLoader();
        /**
         * getResource()方法会去classpath下找这个文件，获取到url resource,
         * 得到这个资源后，调用url.getFile获取到 文件 的绝对路径
         */
        URL url = classLoader.getResource("excel/excel-template/visit_list_template.xls");
        URL url2 = classLoader.getResource("excel/visit_list_temp.xls");
        String num = RandomUtils.generateRandomNumber(5);
        new File(url2.getPath().replace(".xls", num + ".xls"));
        InputStream in = new FileInputStream(url.getPath());
        FileOutputStream out = new FileOutputStream(url2.getPath().replace(".xls", num + ".xls"));
        ExcelBean excelBean = new ExcelBean();
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("enterpriseId", slaveEnterpriseId);
        String dateStr = startDate + "至" + endDate;
        if (org.apache.commons.lang.StringUtils.isNotBlank(startDate)) {
            startDate = startDate + " 00:00:00";
        }
        if (org.apache.commons.lang.StringUtils.isNotBlank(endDate)) {
            endDate = endDate + " 23:59:59";
        }
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        Integer count = visitorSaleServiceApi.countForVisitorSignExport(params);
        if (count > 65000) {
            // 导出失败!导出数据条数超规格(单次最多65000条),请缩小导出范围
            log.error("实名进馆数据超规格导出失败,导出数据条数:" + count);
            response.setContentType("text/html;charset=utf-8");
            PrintWriter errorInfo = response.getWriter();
            errorInfo.print("<script type='text/javascript'> alert('导出条数超出限制(单次最多65000条)，请缩小导出范围')</script>");
            errorInfo.flush();
            errorInfo.close();
            return null;
        }
        List<Visitor> retList = visitorSaleServiceApi.listForVisitorSignExport(params);
        for (Visitor visitor : retList) {
            if (visitor.getSigninTime() != null) {
                visitor.setSigninTimeStr(DateUtils.dateToStr(visitor.getSigninTime(), "yyyy-MM-dd HH:mm:ss"));
            }
            if (visitor.getSrcType() == 1) {
                visitor.setSrcTypeStr("身份证");
            } else if (visitor.getSrcType() == 5) {
                visitor.setSrcTypeStr("护照");
            } else if (visitor.getSrcType() == 6) {
                visitor.setSrcTypeStr("台胞证");
            } else if (visitor.getSrcType() == 7) {
                visitor.setSrcTypeStr("港澳通行证");
            } else if (visitor.getSrcType() == 9) {
                visitor.setSrcTypeStr("社保卡");
            } else {
                visitor.setSrcTypeStr("其他");
            }
            if (visitor.getSrcType() != 1 && visitor.getSrcType() != 5 && visitor.getSrcType() != 6 && visitor.getSrcType() != 7 && visitor.getSrcType() != 9) {
                visitor.setIdentification("");
            }
            if (visitor.getSigninType() == 1) {
                if (visitor.getSrcType() == 9) {
                    visitor.setSigninTypeStr("社保卡");
                } else {
                    visitor.setSigninTypeStr("身份证");
                }
            } else if (visitor.getSigninType() == 3) {
                visitor.setSigninTypeStr("人脸");
            } else {
                visitor.setSigninTypeStr("电子码");
            }
            if (visitor.getSrcType() == 1 || visitor.getSrcType() == 9) {
                if (org.apache.commons.lang.StringUtils.isNotBlank(visitor.getIdentification()) && visitor.getIdentification().length() == 18) {
                    visitor.setUserArea(StringUtils.getProvinceByIdCard(visitor.getIdentification()));
                    visitor.setAge(StringUtils.IdNOToAge(visitor.getIdentification()));
                } else {
                    visitor.setUserArea("-");
                    visitor.setAge(0);
                }
            } else {
                visitor.setUserArea("-");
                visitor.setAge(0);
            }
            if (!StringUtils.isBlank(visitor.getIdentification())) {
                int subIndex = (visitor.getIdentification().length() >> 1) + 1;
                String idNumber = DesensitizationUtils.idMask(visitor.getIdentification(), subIndex, 0);
                if (idNumber != null) {
                    visitor.setIdentification(idNumber);
                }
            }
        }
        try {
            Context context = new Context();
            excelBean.setListData(retList);
            context.putVar("excelBean", excelBean);
            context.putVar("dateStr", "进馆数据(" + dateStr + ")");
            JxlsHelper.getInstance().processTemplate(in, out, context);
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        File f = null;
        f = new File(url2.getPath().replace(".xls", num + ".xls")); // 将服务器上的文件下载下来
        HttpHeaders headers = new HttpHeaders();
        String fileName = new String(("进馆数据(" + dateStr + ").xls").getBytes("UTF-8"), "iso-8859-1");
        headers.setContentType(MediaType.valueOf("application/x-excel;charset=UTF-8"));
        headers.add("Content-Disposition", "attachment; filename=" + fileName);
        headers.add("Content-Length", String.valueOf(f.length()));
        byte[] b = FileUtils.readFileToByteArray(f);
        if (f.exists())
            f.delete();
        PrebookRecordExport prebookRecordExport = new PrebookRecordExport();
        prebookRecordExport.setEnterpriseId(employ.getEnterpriseId());
        prebookRecordExport.setOperId(employ.getId());
        prebookRecordExport.setTimeRange(dateStr);
        prebookRecordExport.setCreateTime(new Date());
        prebookRecordExport.setUpdateTime(new Date());
        prebookRecordExportSaleServiceApi.insert(prebookRecordExport);
        return new ResponseEntity<byte[]>(b, headers, HttpStatus.OK);
    }


    //列表脱敏
    public List<Visitor> visitorListEncryption(List<Visitor> list) {
        //这里对身份证 姓名 进行敏感字加密
        for (Visitor visitor : list) {
            if (visitor.getSrcType() == 1 || visitor.getSrcType() == 9) {
                visitor.setIdentification(idMask(visitor.getIdentification(), 6, 5));
            } else {
                visitor.setIdentification(idMask(visitor.getIdentification(), 2, 2));
            }
            visitor.setName(protectedName(visitor.getName()));

        }
        return list;
    }

    //详情页脱敏
    public Visitor visitorEncryption(Visitor visitor) {
        //这里对身份证 姓名 进行敏感字加密
        if (visitor != null) {
            if (visitor.getSrcType() == 1 || visitor.getSrcType() == 9) {
                visitor.setIdentification(idMask(visitor.getIdentification(), 6, 5));
            } else {
                visitor.setIdentification(idMask(visitor.getIdentification(), 2, 2));
            }
            visitor.setName(protectedName(visitor.getName()));
            //visitor.setVisitorMobile(desensitizedPhoneNumber(visitor.getVisitorMobile()));
            //visitor.setMobile(desensitizedPhoneNumber(visitor.getMobile()));
        }
        return visitor;
    }


    /**
     * 用户身份证号码的打码隐藏加星号加* 18位和非18位身份证处理均可成功处理 参数异常返回null
     *
     * @param idCardNum 身份证号码
     * @param front     需要显示前几位
     * @param end       需要显示末几位
     * @return 处理完成的身份证
     */
    public static String idMask(String idCardNum, int front, int end) {
        // 身份证不能为空
        if (StringUtil.isEmpty(idCardNum)) {
            return null;
        }
        // 需要截取的长度不能大于身份证号长度
        if ((front + end) > idCardNum.length()) {
            return null;
        }
        // 需要截取的不能小于0

        if (front < 0 || end < 0) {
            return null;
        }
        // 计算*的数量
        int asteriskCount = idCardNum.length() - (front + end);
        StringBuffer asteriskStr = new StringBuffer();
        for (int i = 0; i < asteriskCount; i++) {
            asteriskStr.append("*");
        }
        String regex = "(\\w{" + String.valueOf(front) + "})(\\w+)(\\w{" + String.valueOf(end) + "})";
        return idCardNum.replaceAll(regex, "$1" + asteriskStr + "$3");
    }

    //名字脱敏
    private String protectedName(String userName) {
        if (StringUtil.isEmpty(userName)) {
            return null;
        }
        userName = userName.trim();
        char[] r = userName.toCharArray();
        String resultName = "";
        if (r.length == 2) {
            resultName = r[0] + "*";
        }
        if (r.length > 2) {
            String star = "";
            for (int i = 0; i < r.length - 2; i++) {
                star = star + "*";
            }
            resultName = r[0] + star + r[r.length - 1];
        }
        return resultName;
    }

    private static String desensitizedPhoneNumber(String phoneNumber) {
        if (StringUtil.isEmpty(phoneNumber)) {
            return "";
        }
        return phoneNumber.replaceAll("(\\w{3})\\w*(\\w{4})", "$1****$2");
    }

    @RequestMapping("importExcel")
    @ResponseBody
    @IgnoreAuth(requireLogin = false)
    public JsonResponse exportExcel(@RequestParam(value = "file") MultipartFile multipartFile, HttpServletResponse response) {
        try {
            String sessionCode = (String) RequestThreadLocal.get().getSession().getAttribute("user_record_visitor_excel_import");
            if (StringUtils.isBlank(sessionCode)) {
                return this.batchInsertUserVisit(multipartFile.getOriginalFilename(),multipartFile.getInputStream(), response);
            } else {
                return this.fail("正在导入中,请勿重复操作!");
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return this.fail(e.getMessage());
        }
    }

    public JsonResponse batchInsertUserVisit(String fileName,InputStream inputStream, HttpServletResponse response) {
        JsonResponse result = new JsonResponse();
        ImportFileLog oldImportFileLog = importFileLogService.selectByFileName(fileName);
        if(oldImportFileLog != null){
            if("数据导入成功".equals(oldImportFileLog.getStatusName())){
                result.setErrorMessage("当前"+fileName+"文件已成功导入不可再次导入!");
                result.setSuccess(true);
                return result;
            }
        }
        Date startTime = new Date();
        String exeKey=UUID.randomUUID().toString();
        log.error(exeKey+"导入开始时间--- " + startTime);
        ImportFileLog importFileLog = new ImportFileLog();
        importFileLog.setFileName(fileName);
        importFileLog.setStartTime(startTime);
        importFileLogService.insert(importFileLog);

        int errorNum = 0;
        try {
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            XSSFSheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                result.setErrorMessage("excel内容为空!");
                result.setSuccess(false);
                return result;
            }
            int rowNums = sheet.getLastRowNum();
            if (rowNums < 2) {
                result.setErrorMessage("excel内容行数据为空!");
                result.setSuccess(false);
                return result;
            }


            RequestThreadLocal.get().getSession().setAttribute("user_record_visitor_excel_import", System.currentTimeMillis());

//            ArrayList<User> users = new ArrayList<>();
//            ArrayList<User> ticketUsers = new ArrayList<>();
//            ArrayList<PrebookRecord> prebookRecords = new ArrayList<>();
//            ArrayList<TicketOrder> ticketOrders = new ArrayList<>();
//            Map<String, ImportTeamAppointment> teamNumMap = getAllTeamAppointment();

            for (int rowNum = 1; rowNum <= rowNums; rowNum++) {
                errorNum = rowNum;
                XSSFRow xssfRow = sheet.getRow(rowNum);
                if (xssfRow != null) {
                    // todo 过滤掉有删除时间的数据
                    XSSFCell delTime = xssfRow.getCell(26);
                    delTime.setCellType(CellType.STRING);
                    if (!StringUtils.isBlank(delTime.getStringCellValue())) {
                        continue;
                    }
                    // 场馆名称
                    XSSFCell resAddress = xssfRow.getCell(25);
                    if (StringUtils.isBlank(resAddress.getStringCellValue())) {
                        continue;
                    }
                    String resAddressValue = resAddress.getStringCellValue();
                    Integer enterpriseId = getEnterpriseId(resAddressValue);
                    // todo 当前场馆信息为空
                    if (enterpriseId == null) {
                        continue;
                    }
                    // todo 区分票务 和预约的场馆
                    if (enterpriseId == 2 || enterpriseId == 4) {
                        XSSFCell payStateCell = xssfRow.getCell(18);
                        if (payStateCell.getCellType() == CellType.NUMERIC) {
                            // todo 未核销的支付数据跳过
                            if(payStateCell.getNumericCellValue() == 0d) {
                                continue;
                            }
                        }
                        addTicketOrder(xssfRow, enterpriseId);
                    } else {
                        // 判断是否已发布 0-未核销 1-已核销
                        XSSFCell isWrite = xssfRow.getCell(9);
                        if (isWrite.getCellType() == CellType.NUMERIC) {
                            // todo 未核销的预约数据跳过
                            if(isWrite.getNumericCellValue() == 0d) {
                                continue;
                            }
                        }
                        addTeamPrebookRecord(xssfRow, enterpriseId);
                    }

                }
            }

            result.setSuccess(true);
            result.setMessage("预约数据导入成功!");
            RequestThreadLocal.get().getSession().removeAttribute("user_record_visitor_excel_import");
            Date endTime = new Date();
            log.error(exeKey+"导入结束时间--- " + endTime);
            importFileLog.setEndTime(endTime);
            importFileLog.setStatusName("数据导入成功");
            importFileLogService.update(importFileLog);

            return result;
        } catch (Exception e) {
//            e.printStackTrace();
            log.error(exeKey+"导入报错: " + e);
            log.error("错误行数：" + errorNum);
            importFileLog.setEndTime(new Date());
            importFileLog.setStatusName("第" +errorNum+"行导入错误");
            importFileLogService.update(importFileLog);

            result.setSuccess(false);
            result.setErrorMessage("用户预约记录第" + errorNum + "行数据导入失败");
            RequestThreadLocal.get().getSession().removeAttribute("user_record_visitor_excel_import");
            return result;
        }
    }

    /**
     * 新增团队预约单
     *
     * @param xssfRow      当前行
     * @param enterpriseId 企业id
     * @return 保存成功行数
     */
    private void addTeamPrebookRecord(XSSFRow xssfRow, Integer enterpriseId) {
        XSSFCell teamno = xssfRow.getCell(27);
        ImportTeamAppointment importTeam = null;
        if (teamno != null) {
            if (!StringUtils.isBlank(teamno.getStringCellValue())) {
                importTeam = importTeamAppointmentServiceApi.selectByRecNo(teamno.getStringCellValue());
                // 无数据 不处理
                if (importTeam == null) {
                    return;
                }
            }
        }
        //处理用户信息
        User user = null;
        if (importTeam == null) {
            // 保存领队信息
            User commonUser = new User(); // 要保存的用户(新手机号)
            // 手机号
            XSSFCell visitorPhone = xssfRow.getCell(1);
            if(visitorPhone == null){
                return;
            }
            // 如果识别成了科学计数法的 转为字符串处理
            if(visitorPhone.getCellType() == CellType.NUMERIC){
                visitorPhone.setCellType(CellType.STRING);
            }
            if (StringUtils.isBlank(visitorPhone.getStringCellValue())) {
                return;
            }
            if(visitorPhone.getStringCellValue().length() > 11){
                return;
            }
            if(isNotStartWithOne(visitorPhone.getStringCellValue())){
                return;
            }
            commonUser.setMobile(visitorPhone.getStringCellValue());//手机号
            XSSFCell visitorName = xssfRow.getCell(0);// 真实姓名
            if(!StringUtils.isBlank(visitorName.getStringCellValue())) {
                commonUser.setRealName(truncateString(visitorName.getStringCellValue()));//领队名称
            }
            // 判断当前手机号用户是否已存在
            user = ticketUserService.selectByMobileNoPassword(commonUser.getMobile());
            if (user == null) {
                commonUser.setUserNick("微信用户");
                commonUser.setCreateTime(new Date());
                commonUser.setUpdateTime(new Date());
                commonUser.setGrade(1);
                commonUser.setStatus(1);
                commonUser.setUseType(1);
                commonUser.setPassword(MD5.encode("123456"));
                // todo 暂定 迁移中文
                commonUser.setRegisterSrc("qianyi");
                try{
                    ticketUserService.insert(commonUser);
                }catch (Exception e){
                    commonUser = ticketUserService.selectByMobileNoPassword(commonUser.getMobile());
                }

                user = commonUser;
            }
        } else if (importTeam.getPrebookRecordId() == 0) {
            // 保存领队信息
            User leaderUser = new User();
            if(!StringUtils.isBlank(importTeam.getLeaderName())) {
                leaderUser.setRealName(truncateString(importTeam.getLeaderName()));
            }
            leaderUser.setMobile(importTeam.getLeaderPhone());
            // 判断当前手机号用户是否已存在
            user = ticketUserService.selectByMobileNoPassword(leaderUser.getMobile());
            if (user == null) {
                leaderUser.setUserNick("微信用户");
                leaderUser.setCreateTime(new Date());
                leaderUser.setUpdateTime(new Date());
                leaderUser.setGrade(1);
                leaderUser.setStatus(1);
                leaderUser.setUseType(1);
                leaderUser.setPassword(MD5.encode("123456"));
                // todo 暂定 迁移中文
                leaderUser.setRegisterSrc("qianyi");
                try{
                    ticketUserService.insert(leaderUser);
                }catch (Exception e){
                    leaderUser = ticketUserService.selectByMobileNoPassword(leaderUser.getMobile());
                }

                user = leaderUser;
            }
        }

        //处理预约信息
        if (importTeam == null || importTeam.getPrebookRecordId() == 0) {
            // 预约记录表
            PrebookRecord prebookRecord = new PrebookRecord();
            prebookRecord.setUserId(user.getId());
            prebookRecord.setEnterpriseId(enterpriseId);
            prebookRecord.setQuantity(1);
            if (importTeam != null) {
                prebookRecord.setQuantity(importTeam.getReservationNumber());
                prebookRecord.setPreviewDate(importTeam.getAppointmentDate());
                prebookRecord.setPeriodId(getPeriodIdByEnterpriseId(enterpriseId, importTeam.getArriveTime()));
                prebookRecord.setUseStatus(importTeam.getIsWrite());
            } else {
                // 预览日期  =  核销日期
                XSSFCell appointmentDate = xssfRow.getCell(4);
                if (appointmentDate != null && null != appointmentDate.getDateCellValue()) {
                    prebookRecord.setPreviewDate(appointmentDate.getDateCellValue());
                }
                // 时段 0-上午 1-下午
                XSSFCell arriveTime = xssfRow.getCell(7);
                if (arriveTime != null) {
                    arriveTime.setCellType(CellType.STRING);
                    if (null != arriveTime.getStringCellValue()) {
                        prebookRecord.setPeriodId(getPeriodIdByEnterpriseId(enterpriseId, Integer.parseInt(arriveTime.getStringCellValue())));
                    }
                }
                // 判断是否已发布 0-未核销 1-已核销
                XSSFCell isWrite = xssfRow.getCell(9);
                if (isWrite != null) {
                    isWrite.setCellType(CellType.STRING);
                    prebookRecord.setUseStatus(Integer.parseInt(isWrite.getStringCellValue()));
                } else {
                    prebookRecord.setUseStatus(0);
                }
            }
            // 区分团队或者个人
            if (importTeam == null) {
                prebookRecord.setType(1);
            } else {
                prebookRecord.setType(2);
            }

            prebookRecord.setChannel(1);
            prebookRecord.setMode(1);

            // 下单时间
            XSSFCell createTime = xssfRow.getCell(6);
            prebookRecord.setCreateTime(createTime.getDateCellValue());

            int prebookRecordId = prebookRecordSaleServiceApi.insert(prebookRecord);

            prebookRecord.setId(prebookRecordId);
            if (importTeam != null) {
                importTeam.setPrebookRecordId(prebookRecordId);
                importTeamAppointmentServiceApi.update(importTeam);
            }
            addVisitor(prebookRecord, xssfRow);
        } else {
            PrebookRecord oldPrebookRecord = prebookRecordSaleServiceApi.selectById(importTeam.getPrebookRecordId());
            addVisitor(oldPrebookRecord, xssfRow);
        }
    }

    /**
     * 判断是否1开头的手机号
     * @param str 手机号
     * @return 是否1开头
     */
    public static boolean isNotStartWithOne(String str) {
        return str.charAt(0) != '1';
    }

    private void addVisitor(PrebookRecord prebookRecord, XSSFRow xssfRow) {
        int enterpriseId = prebookRecord.getEnterpriseId();
        // 明细表
        Visitor visitor = new Visitor();
        visitor.setEnterpriseId(enterpriseId);
        visitor.setInformationId(0);

        XSSFCell visitorCardid = xssfRow.getCell(2);// 证件编号
        if (!StringUtils.isBlank(visitorCardid.getStringCellValue())) {
            visitor.setIdentification(visitorCardid.getStringCellValue());
        }

        // 籍贯
        XSSFCell nativeplace = xssfRow.getCell(11);
        if (!StringUtils.isBlank(nativeplace.getStringCellValue())) {
            String nativePlaceStr = nativeplace.getStringCellValue();
            visitor.setUserArea(nativePlaceStr);
        }
        XSSFCell visitorName = xssfRow.getCell(0); // 真实姓名
        if (!StringUtils.isBlank(visitorName.getStringCellValue())) {
            visitor.setName(visitorName.getStringCellValue());
        }

        // 性别
        XSSFCell sexTypeName = xssfRow.getCell(12);
        if (!StringUtils.isBlank(sexTypeName.getStringCellValue())) {
            String sexType = sexTypeName.getStringCellValue();
            visitor.setSex(sexType.equals("男") ? 1 : 2);
        }
        // 年龄
        XSSFCell ageStr = xssfRow.getCell(13);
        ageStr.setCellType(CellType.STRING);
        if (StringUtils.isBlank(ageStr.getStringCellValue())) {
            visitor.setAge(0);
        } else {
            String age = ageStr.getStringCellValue();
            visitor.setAge(Integer.parseInt(age));
        }
        // 民族
        XSSFCell nation = xssfRow.getCell(14);
        nation.setCellType(CellType.STRING);
        if (StringUtils.isBlank(nation.getStringCellValue())) {
            visitor.setNation("汉族");
        } else {
            visitor.setNation(nation.getStringCellValue());
        }
        // 证件类型
        XSSFCell visitorCardType = xssfRow.getCell(3);
        if (!StringUtils.isBlank(visitorCardType.getStringCellValue())) {
            visitor.setSrcType(getSrcType(visitorCardType.getStringCellValue()));
        } else {
            visitor.setSrcType(2);
        }
        visitor.setPreviewDate(prebookRecord.getPreviewDate());
        visitor.setSigninType(2);
        visitor.setChannel(1);
        visitor.setSigninQuantity(1);
        visitor.setMode(1);
        visitor.setUserRelationId(0);
        visitor.setPreviewDate(prebookRecord.getPreviewDate());

        // 判断是否已发布 0-未核销 1-已核销
        XSSFCell isWrite = xssfRow.getCell(9);
        isWrite.setCellType(CellType.STRING);
        // 核销时间
        XSSFCell writeOfftime = xssfRow.getCell(10);
        writeOfftime.setCellType(CellType.NUMERIC);
        // 如果是否核销为空 但 核销时间不为空
        if (!StringUtils.isBlank(isWrite.getStringCellValue()) && writeOfftime.getDateCellValue() != null) {
            visitor.setSigninTime(writeOfftime.getDateCellValue());
        }

        if (writeOfftime.getDateCellValue() != null) {
            visitor.setSigninTime(writeOfftime.getDateCellValue());
            visitor.setIsTake(1);
            visitor.setStatus(2);
        }
        visitor.setPeriodId(prebookRecord.getPeriodId());
        visitor.setPrebookRecordId(prebookRecord.getId());

        // 创建时间
        XSSFCell createTime = xssfRow.getCell(6);
        visitor.setCreateTime(createTime.getDateCellValue());

        visitorSaleServiceApi.insert(visitor);
    }

    private void addTicketOrder(XSSFRow xssfRow, int enterpriseId) {
        // 已经保存过 ticketOrder 的数据
        ImportTeamAppointment importTeam = null;
        XSSFCell teamNoCell = xssfRow.getCell(27);
        teamNoCell.setCellType(CellType.STRING);
        if (!StringUtils.isBlank(teamNoCell.getStringCellValue())) {
            importTeam = importTeamAppointmentServiceApi.selectByRecNo(teamNoCell.getStringCellValue());
            if (importTeam == null) {
                return;
            }
        }

        if (importTeam == null || importTeam.getPrebookRecordId() == 0) {
            User user;
            if (importTeam == null) {//散客
                User commonUser = new User(); // 要保存的用户(新手机号)
                // 手机号
                XSSFCell visitorPhone = xssfRow.getCell(1);
                if(visitorPhone == null){
                    return;
                }
                // 如果识别成了科学计数法的 转为字符串处理
                if(visitorPhone.getCellType() == CellType.NUMERIC){
                    visitorPhone.setCellType(CellType.STRING);
                }
                if (StringUtils.isBlank(visitorPhone.getStringCellValue())) {
                    return;
                }
                if(visitorPhone.getStringCellValue().length() > 11){
                    return;
                }
                if(isNotStartWithOne(visitorPhone.getStringCellValue())){
                    return;
                }
                commonUser.setMobile(visitorPhone.getStringCellValue());//手机号
                XSSFCell visitorName = xssfRow.getCell(0);// 真实姓名
                if(!StringUtils.isBlank(visitorName.getStringCellValue())) {
                    commonUser.setRealName(truncateString(visitorName.getStringCellValue()));//领队名称
                }
                // 判断当前手机号用户是否已存在
                user = ticketUserService.selectByMobileNoPassword(commonUser.getMobile());
                if (user == null) {
                    commonUser.setUserNick("微信用户");
                    commonUser.setCreateTime(new Date());
                    commonUser.setUpdateTime(new Date());
                    commonUser.setGrade(1);
                    commonUser.setStatus(1);
                    commonUser.setUseType(1);
                    commonUser.setPassword(MD5.encode("123456"));
                    // todo 暂定 迁移中文
                    commonUser.setRegisterSrc("qianyi");
                    ticketUserService.insert(commonUser);
                    user = commonUser;
                }
            } else {//团队
                User leaderUser = new User(); // 要保存的用户(新手机号)
                leaderUser.setMobile(importTeam.getLeaderPhone());//手机号
                if(!StringUtils.isBlank(importTeam.getLeaderName())) {
                    leaderUser.setRealName(truncateString(importTeam.getLeaderName()));
                }
                // 判断当前手机号用户是否已存在
                user = ticketUserService.selectByMobileNoPassword(leaderUser.getMobile());
                if (user == null) {
                    leaderUser.setUserNick("微信用户");
                    leaderUser.setCreateTime(new Date());
                    leaderUser.setUpdateTime(new Date());
                    leaderUser.setGrade(1);
                    leaderUser.setStatus(1);
                    leaderUser.setUseType(1);
                    leaderUser.setPassword(MD5.encode("123456"));
                    // todo 暂定 迁移中文
                    leaderUser.setRegisterSrc("qianyi");
                    ticketUserService.insert(leaderUser);
                    user = leaderUser;
                }
            }

            TicketOrder ticketOrder = new TicketOrder();//订单主表

            ticketOrder.setUserId(user.getId());
            ticketOrder.setInformationId(getInformationId(enterpriseId));
            ticketOrder.setEnterpriseId(enterpriseId);
            if (importTeam == null) {
                ticketOrder.setQuantity(1);
            } else {
                ticketOrder.setQuantity(importTeam.getReservationNumber());
            }

            // 默认现金支付 1：wechat 2：alipaly 3：cash
            ticketOrder.setPayWay(3);
            // 默认线下购买 1：网络 2：线下 3：机器 4：app
            ticketOrder.setBuyWay(2);

            if (importTeam == null) {
                XSSFCell visitorPhone = xssfRow.getCell(1);
                if(visitorPhone == null){
                    return;
                }
                // 如果识别成了科学计数法的 转为字符串处理
                if(visitorPhone.getCellType() == CellType.NUMERIC){
                    visitorPhone.setCellType(CellType.STRING);
                }
                if (StringUtils.isBlank(visitorPhone.getStringCellValue())) {
                    return;
                }
                if(visitorPhone.getStringCellValue().length() > 11){
                    return;
                }
                if(isNotStartWithOne(visitorPhone.getStringCellValue())){
                    return;
                }
                ticketOrder.setMobile(visitorPhone.getStringCellValue());
            } else {
                ticketOrder.setMobile(importTeam.getLeaderPhone());
            }

            // 预览日期  =  核销日期
            XSSFCell appointmentDate = xssfRow.getCell(4);
            if (appointmentDate != null && null != appointmentDate.getDateCellValue()) {
                ticketOrder.setViewDate(appointmentDate.getDateCellValue());
            }

            XSSFCell payStateCell = xssfRow.getCell(18);
            payStateCell.setCellType(CellType.STRING);
//          payState = 0:待支付/1：支付成功/2：退款处理中/3：退款成功/4：退款失败 / 空
            if (StringUtils.isBlank(payStateCell.getStringCellValue())) {
                ticketOrder.setStatus(3);
                ticketOrder.setTotalPrice(BigDecimal.ZERO);
                ticketOrder.setPayPrice(BigDecimal.ZERO);
                ticketOrder.setCloseReson("没有支付状态的数据");
            }

            if (!StringUtils.isBlank(payStateCell.getStringCellValue())) {
                //   status =  1：未付款(待付款)2：已付款（未取票）3、交易关闭
                String payState = payStateCell.getStringCellValue();
//                XSSFCell orderPrice = xssfRow.getCell(20); // 应付金额 全票价格
//                XSSFCell payAmount = xssfRow.getCell(21); // 支付金额

                // 待支付的数据  没有金额 设置为0
                if (payState.equals("0")) {
                    ticketOrder.setStatus(1);
                    ticketOrder.setTotalPrice(BigDecimal.ZERO);
                    ticketOrder.setPayPrice(BigDecimal.ZERO);
                }

                // 已支付的数据
                if (payState.equals("1")) {
                    BigDecimal ticketPrice = BigDecimal.ZERO;
                    XSSFCell payAmount = xssfRow.getCell(21); // 支付金额
                    if (payAmount != null) {
                        payAmount.setCellType(CellType.STRING);
                        if (!StringUtils.isBlank(payAmount.getStringCellValue())) {
                            ticketPrice = new BigDecimal(payAmount.getStringCellValue());
                        }
                    }
                    ticketOrder.setStatus(2);
                    if (importTeam == null) {//散客
                        ticketOrder.setPayPrice(ticketPrice);
                    } else {
                        ticketOrder.setPayPrice(ticketPrice.multiply(new BigDecimal(importTeam.getReservationNumber())));
                    }

                    ticketOrder.setTotalPrice(ticketOrder.getPayPrice());
                }

                // 退票
                if (payState.equals("3")) {
                    // 3-退款 并关闭
                    ticketOrder.setStatus(3);
                    ticketOrder.setCloseReson("退款成功");
                    BigDecimal ticketPrice = BigDecimal.ZERO;
                    XSSFCell payAmount = xssfRow.getCell(21); // 支付金额
                    if (payAmount != null) {
                        payAmount.setCellType(CellType.STRING);
                        if (!StringUtils.isBlank(payAmount.getStringCellValue())) {
                            ticketPrice = new BigDecimal(payAmount.getStringCellValue());
                        }
                    }
                    if (importTeam == null) {//散客
                        ticketOrder.setPayPrice(ticketPrice);
                    } else {
                        ticketOrder.setPayPrice(ticketPrice.multiply(new BigDecimal(importTeam.getReservationNumber())));
                    }
                    ticketOrder.setTotalPrice(ticketOrder.getPayPrice());
                }
            }

            // 创建时间
            XSSFCell createTime = xssfRow.getCell(6);
            ticketOrder.setBuyTime(createTime.getDateCellValue());
            if (importTeam == null) {
                // 时段 0-上午 1-下午
                XSSFCell arriveTime = xssfRow.getCell(7);
                if (arriveTime != null) {
                    arriveTime.setCellType(CellType.STRING);
                    if (null != arriveTime.getStringCellValue()) {
                        ticketOrder.setTimeId(getTimeIdByEnterpriseId(enterpriseId, Integer.parseInt(arriveTime.getStringCellValue())));
                    }
                }
            } else {
                ticketOrder.setTimeId(getPeriodIdByEnterpriseId(enterpriseId, importTeam.getArriveTime()));
            }
            // 是否已取票 0-未 1-已
            ticketOrder.setIsTakeTicket(1);
            // 是否电子核销 1-是 0-否
            ticketOrder.setIsEVerification(1);
//            if (isWrite == 0) {
//                // 页面显示逻辑  是否电子核销(电子核销数量/总数量)，取票
////                ticketOrder.setTakeTicketQuantity(0);
//                ticketOrder.seteVerificationQuantity(0);
//            }
//            if (isWrite == 1) {
////                ticketOrder.setTakeTicketQuantity(1);
//                ticketOrder.seteVerificationQuantity(1);
//            }
            // 核销时间
            XSSFCell writeOfftime = xssfRow.getCell(10);

            if (writeOfftime.getCellType() == CellType.NUMERIC) {
                ticketOrder.setPaidTime(writeOfftime.getDateCellValue());
                ticketOrder.setIsTakeTicket(1);
                ticketOrder.seteVerificationQuantity(0);
            }

//            if (writeOfftime.getDateCellValue() != null) {
//                    ticketOrder.setPaidTime(writeOfftime.getDateCellValue());
//                    ticketOrder.setIsTakeTicket(1);
////                    ticketOrder.setTakeTicketQuantity(1);
//                    ticketOrder.seteVerificationQuantity(0);
//
//            }

            // 支付成功时间
            XSSFCell payTime = xssfRow.getCell(19);
            if(payTime.getCellType() == CellType.STRING){
                String stringCellValue = payTime.getStringCellValue();
                if(!StringUtils.isBlank(stringCellValue)) {
                    payTime.setCellType(CellType.NUMERIC);
                }
            }
            if(payTime.getCellType() == CellType.NUMERIC) {
                if (payTime.getDateCellValue() != null) {
                    ticketOrder.setPaidTime(payTime.getDateCellValue());
                }
            }

            ticketOrder.setCreateTime(createTime.getDateCellValue());
            ticketOrder.setUpdateTime(createTime.getDateCellValue());
            int orderId = ticketOrderSaleServiceApi.insert(ticketOrder);
            ticketOrder.setId(orderId);
            if (importTeam != null) {
                importTeam.setPrebookRecordId(orderId);
                importTeamAppointmentServiceApi.update(importTeam);

            }

            //ticketOrderRelation
            createOrderRealation(ticketOrder, xssfRow);
            return;
        }

        if (importTeam.getPrebookRecordId() != 0) {
            int orderId = importTeam.getPrebookRecordId();
            TicketOrder oldTicketOrder = ticketOrderSaleServiceApi.selectById(orderId);
            createOrderRealation(oldTicketOrder, xssfRow);
        }
    }

    private void createOrderRealation(TicketOrder ticketOrder, XSSFRow xssfRow) {
        //ticketOrderRelation
        TicketOrderRelation ticketOrderRelation = new TicketOrderRelation();// 关联关系表（一对多）
        BigDecimal ticketPrice = BigDecimal.ZERO;
        XSSFCell payAmount = xssfRow.getCell(21); // 支付金额
        if (payAmount != null) {
            payAmount.setCellType(CellType.STRING);
            if (!StringUtils.isBlank(payAmount.getStringCellValue())) {
                ticketPrice = new BigDecimal(payAmount.getStringCellValue());
            }
        }
        ticketOrderRelation.setTicketId(getTicketIdByEnterpriseId(ticketOrder.getEnterpriseId(), ticketPrice));

        // 0-未付款 1-已取票 2-未取票
        ticketOrderRelation.setIsTakeTicket(0);
        ticketOrderRelation.setEnterpriseId(ticketOrder.getEnterpriseId());
        ticketOrderRelation.setInformationId(getInformationId(ticketOrder.getEnterpriseId()));

        // 身份证号
        XSSFCell visitorCardid = xssfRow.getCell(2);
        ticketOrderRelation.setIdcard("");
        if (visitorCardid != null && !StringUtils.isBlank(visitorCardid.getStringCellValue())) {
            ticketOrderRelation.setIdcard(visitorCardid.getStringCellValue());
        }

        XSSFCell nativePlace = xssfRow.getCell(11); // 籍贯
        if (nativePlace != null && !StringUtils.isBlank(nativePlace.getStringCellValue())) {
            ticketOrderRelation.setUserArea(nativePlace.getStringCellValue());
        }

        // 场次id
        ticketOrderRelation.setTimeId(ticketOrder.getTimeId());

        XSSFCell visitorName = xssfRow.getCell(0);
        ticketOrderRelation.setName("");
        if (visitorName != null && !StringUtils.isBlank(visitorName.getStringCellValue())) {
            ticketOrderRelation.setName(visitorName.getStringCellValue());
        }

        XSSFCell createTime = xssfRow.getCell(6);
        ticketOrderRelation.setCreateTime(createTime.getDateCellValue());
        ticketOrderRelation.setUpdateTime(createTime.getDateCellValue());

        // 是否核销 1-核销 2-未核销
        ticketOrderRelation.setIsVerification(2);

        XSSFCell visitorCardType = xssfRow.getCell(3);
        if (visitorCardType != null && !StringUtils.isBlank(visitorCardType.getStringCellValue())) {
            ticketOrderRelation.setCardType(getSrcType(visitorCardType.getStringCellValue()));
        }

        // 手机号
        XSSFCell visitorPhone = xssfRow.getCell(1);
        visitorPhone.setCellType(CellType.STRING);
        if (!StringUtils.isBlank(visitorPhone.getStringCellValue())) {
            ticketOrderRelation.setMobile(visitorPhone.getStringCellValue());
        }

        ticketOrderRelation.setTicketType(compareBigDecimal(ticketPrice));
        ticketOrderRelation.setTakeTicketOperId(0);
        ticketOrderRelation.setTicketPrice(ticketPrice);
        ticketOrderRelation.setTerminalId(0);
        // 1-已退款 2-可退款 3-退款中
        ticketOrderRelation.setRefundStatus(2);
        // 是否进馆  1-是0-否
        ticketOrderRelation.setIsSignin(0);
        // 待支付的数据  没有金额 设置为0
        if (ticketOrder.getPayPrice().intValue() == 0) {
            ticketOrderRelation.setIsTakeTicket(0);
        }
        // 退票
        if ("退款成功".equals(ticketOrder.getCloseReson())) {
            // 1-已退款 2-可退款 3-退款中
            ticketOrderRelation.setRefundStatus(1);
            ticketOrderRelation.setRefundPrice(getTicketPriceByEnterpriseId(ticketOrder.getEnterpriseId()));
        }

        // 核销时间
        XSSFCell verificationTime = xssfRow.getCell(10);
        if (verificationTime.getCellType() == CellType.NUMERIC) {
            ticketOrderRelation.setVerificationTime(verificationTime.getDateCellValue());
        }
        ticketOrderRelation.setIsTakeTicket(0);
        // 判断是否已发布 0-未核销 1-已核销
        XSSFCell isWrite = xssfRow.getCell(9);
        isWrite.setCellType(CellType.STRING);
        if (!StringUtils.isBlank(isWrite.getStringCellValue())) {
            String stringCellValue = isWrite.getStringCellValue();
            if ("1".equals(stringCellValue)) {
                // 是否核销 1-核销 2-未核销
                ticketOrderRelation.setIsVerification(1);
                // 核销方式 1-电子核销 2-票核销
                ticketOrderRelation.setVerificationType(1);
                ticketOrderRelation.setIsGet(1);
            }
        } else {
            // 是否核销 1-核销 2-未核销
            ticketOrderRelation.setIsVerification(2);
            ticketOrderRelation.setIsGet(0);
        }
        ticketOrderRelation.setOrderId(ticketOrder.getId());
        // 设置电子码核销数量
        ticketOrder.seteVerificationQuantity(ticketOrder.geteVerificationQuantity() + 1);
        ticketOrder.setIsEVerification(1);
        ticketOrderSaleServiceApi.update(ticketOrder);
        ///////////////////////
        ticketOrderRelationSaleServiceApi.insertByImport(ticketOrderRelation);
    }

    /**
     * 企业id 获取价格
     *
     * @param enterpriseId 企业id
     * @return 票价
     */
    private BigDecimal getTicketPriceByEnterpriseId(int enterpriseId) {
        if (enterpriseId == 4) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(2);
    }

    /**
     * 填充门票id
     *
     * @param enterpriseId 企业id
     * @return 门票id
     */
    private int getTicketIdByEnterpriseId(int enterpriseId, BigDecimal payPrice) {
        int priceNum = compareBigDecimal(payPrice);
        if (enterpriseId == 4) {
            if (priceNum == 0) {
                return 41;
            }
            if (priceNum == 1) {
                return 40;
            }
            return 39;
        }
        if (priceNum == 0) {
            return 38;
        }
        if (priceNum == 1) {
            return 37;
        }
        return 36;
    }

    public static int compareBigDecimal(BigDecimal input) {
        BigDecimal zero = BigDecimal.ZERO;
        BigDecimal one = BigDecimal.ONE;

        if (input.compareTo(zero) <= 0) {
            return 0;
        } else if (input.compareTo(one) <= 0) {
            return 2;
        } else {
            return 1;
        }
    }

    /**
     * 填充场次id
     *
     * @param enterpriseId 企业id
     * @param arriveTime   0-上午1-下午  6-白鹤梁 7-宋庆龄
     * @return 各场馆的场次id
     */
    private int getTimeIdByEnterpriseId(int enterpriseId, int arriveTime) {
        if (enterpriseId == 4) {
            return arriveTime == 0 ? 5 : 6;
        }
        return arriveTime == 0 ? 7 : 8;
    }

    /**
     * 通过企业id和上下午填充时段id
     *
     * @param enterpriseId 企业id
     * @param arriveTime   0-上午 1-下午
     * @return 企业id
     */
    private int getPeriodIdByEnterpriseId(int enterpriseId, int arriveTime) {
        if (enterpriseId == 1) {
            return arriveTime == 0 ? 1 : 2;
        }
        return arriveTime == 0 ? 5 : 6;
    }

    /**
     * 填充展览id
     * todo 展览id切换
     *
     * @param enterpriseId 企业id
     * @return 展览id
     */
    private int getInformationId(int enterpriseId) {
        if (enterpriseId == 4) {
            return 6;
        }
        return 7;
    }

    private int getSrcType(String visitorCardTypeStr) {
//		用户来源1身份证2手机号注册扫码3人脸识别4民防馆人数5表示护照6台胞7港澳8团队非实名9社保卡10军官证11外国人永久居住证12票务核销码13-19预留20employ人脸21visitor人脸
        // 二代身份证 、护照、军官证
        if (visitorCardTypeStr.contains("身份证")) {
            return 1;
        }
        if (visitorCardTypeStr.equals("护照")) {
            return 5;
        }
        if (visitorCardTypeStr.equals("台胞")) {
            return 6;
        }
        if (visitorCardTypeStr.equals("港澳")) {
            return 7;
        }
        if (visitorCardTypeStr.equals("军官证")) {
            return 10;
        }
        return 2;
    }

    public static String truncateString(String input) {
        if (input.length() > 35) {
            return input.substring(0, 35);
        } else {
            return input;
        }
    }

    private String createTakeTicketCode(Integer orderId) {

        Date takeCodeDate = new Date();
        String dateFormat = "yyMMdd";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);
        String dateStr = simpleDateFormat.format(takeCodeDate);

        int modTemp = Math.floorMod(orderId, 1000);
        int modLength = (modTemp + "").length();

        int takeCodeHours = takeCodeDate.getHours();
        int weight = takeCodeHours * 3 + modLength;
        int randowLength = 6;
        if (weight < 10) {
            randowLength = 7;
        }
        String random = getRandom(randowLength - modLength);

        return dateStr + modTemp + random + weight;

    }

    private static String createVerificationCode(int orderRelationId) {

        int modTemp = Math.floorMod(orderRelationId, 1000);

        String orderRelationIdStr = buZero(modTemp + "", 3);
        String code = new SimpleDateFormat("yy").format(new Date()) + orderRelationIdStr
                + CalfDateUtil.localDateTimeToStr(LocalDateTime.now(), "MMddHHmmss") + getRandom(5);
        int factorLength = factorArray.length;
        int total = 0;
        for (int i = 0; i < factorLength; i++) {
            total += Character.digit(code.charAt(i), 10) * factorArray[i];
        }
        return code + total % 10;
    }

    /**
     * 判断企业id
     *
     * @param enterpriseName 场地名
     * @return 场地id
     */
    public Integer getEnterpriseId(String enterpriseName) {
        switch (enterpriseName) {
            case "重庆中国三峡博物馆":
                return 1;
            case "重庆宋庆龄纪念馆":
            case "重庆宋庆龄旧居陈列馆":
                return 2;
            case "三峡文物科技保护基地":
                return 3;
            case "重庆白鹤梁水下博物馆":
                return 4;
        }
        return null;
    }

    /**
     * 企业id+年月日时分秒+4位随机
     *
     * @Title: buildOrdersSerial
     * @Description: TODO
     * @Param @return
     * @Return String
     * @Throws
     */
    private synchronized String buildOrdersSerial(Date date, int enterpriseId) {
        String idStr = buZero(enterpriseId + "", 2);
        LocalDateTime localDateTime = convertDateToLocalDateTime(date);
        String dateStr = CalfDateUtil.localDateTimeToStr(localDateTime, "yyMMddHHmmss");
        String random = getRandom(4);
        return "T" + dateStr + idStr + random;

    }

    public static LocalDateTime convertDateToLocalDateTime(Date date) {
        // 将 Date 转换为 Instant
        Instant instant = new Date().toInstant();
        if (date != null) {
            instant = date.toInstant();
        }
        // 获取系统默认时区
        ZoneId zoneId = ZoneId.systemDefault();
        // 将 Instant 和 ZoneId 组合成 ZonedDateTime
        return instant.atZone(zoneId).toLocalDateTime();
    }

    private static String getRandom(int size) {
        StringBuilder sb = new StringBuilder();
        Random ran = new Random();
        for (int i = 0; i < size; i++) {
            int r = ran.nextInt(10);// 10以内的数
            sb.append(r);//
        }
        return sb.toString();
    }

    private static String buZero(String str, int count) {
        StringBuilder strBuilder = new StringBuilder(str);
        while (strBuilder.length() < count) {
            strBuilder.insert(0, "0");
        }
        str = strBuilder.toString();
        return str;
    }
}