package com.taoart.calf.bwy.domain.fullview;

import com.taoart.common.domain.BaseDomain;

public class CqManagerInformation extends BaseDomain{

	private static final long serialVersionUID = 3394652596257900L;
	
	private int enterpriseId;
			/**名称*/
	private String name;

	private String url;

	private String coverPath;

	private Integer hotCount;

	private String resourcePath;

	public String getResourcePath() {
		return resourcePath;
	}

	public void setResourcePath(String resourcePath) {
		this.resourcePath = resourcePath;
	}

	public void setEnterpriseId(int enterpriseId){
		this.enterpriseId = enterpriseId;
	}
	
	public int getEnterpriseId(){
		return enterpriseId;
	}
	public void setName(String name){
		this.name = name;
	}
	
	public String getName(){
		return name;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getCoverPath() {
		return coverPath;
	}

	public void setCoverPath(String coverPath) {
		this.coverPath = coverPath;
	}

	public Integer getHotCount() {
		return hotCount;
	}

	public void setHotCount(Integer hotCount) {
		this.hotCount = hotCount;
	}
}