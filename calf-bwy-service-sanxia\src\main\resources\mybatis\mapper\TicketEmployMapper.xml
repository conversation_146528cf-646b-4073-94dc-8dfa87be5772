<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.taoart.calf.bwy.mapper.TicketEmployMapper">
    <resultMap type="ticketEmploy" id="result-map-ticketEmploy">
        <result column="id" javaType="int" jdbcType="INTEGER" property="id"/>
        <result column="login_name" javaType="String" jdbcType="VARCHAR" property="loginName"/>
        <result column="password" javaType="String" jdbcType="CHAR" property="password"/>
        <result column="name" javaType="String" jdbcType="VARCHAR" property="name"/>
        <result column="photo" javaType="String" jdbcType="VARCHAR" property="photo"/>
        <result column="role_id" javaType="int" jdbcType="INTEGER" property="roleId"/>
        <result column="is_has_message" javaType="int" jdbcType="INTEGER" property="isHasMessage"/>
        <result column="create_time" javaType="java.util.Date" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" javaType="java.util.Date" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="photo_update_time" javaType="java.util.Date" jdbcType="TIMESTAMP" property="photoUpdateTime"/>
        <result column="enterprise_id" property="enterpriseId"/>
        <result column="status" javaType="int" jdbcType="INTEGER" property="status"/>
        <result column="check_ticket_status" javaType="int" jdbcType="INTEGER" property="checkTicketStatus"/>
        <result column="mobile" javaType="String" jdbcType="VARCHAR" property="mobile"/>
        <result column="state" javaType="int" jdbcType="INTEGER" property="state"/>

    </resultMap>

    <!-- ========插入一条记录 ======== -->
    <insert id="insert" parameterType="ticketEmploy" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ticket_employ(
        enterprise_id,
        login_name,
        password,
        name,
        photo,
        role_id,
        create_time,
        update_time,
        photo_update_time,
        status,
        check_ticket_status,
        mobile
        )VALUES(
        #{enterpriseId} ,
        #{loginName,jdbcType=VARCHAR},
        #{password,jdbcType=CHAR},
        #{name,jdbcType=VARCHAR},
        #{photo,jdbcType=VARCHAR},
        #{roleId,jdbcType=INTEGER},
        now(),
        now(),
        <if test="photo != null">
            photo_update_time=#{photoUpdateTime},
        </if>
        <if test="photo == null">
            photo_update_time=null,
        </if>
        #{status,jdbcType=INTEGER},
        #{checkTicketStatus,jdbcType=INTEGER},
        #{mobile,jdbcType=VARCHAR}
        )
    </insert>

    <!-- ====================== 按主键查询============================= -->
    <select id="selectById" parameterType="int" resultMap="result-map-ticketEmploy">
        SELECT *
        FROM ticket_employ
        WHERE ID = #{id, jdbcType=INTEGER}
    </select>

    <select id="listByEnterpriseId" parameterType="int" resultMap="result-map-ticketEmploy">
        SELECT *
        FROM ticket_employ
        WHERE enterprise_id = #{enterpriseId, jdbcType=INTEGER}
    </select>

    <!-- =====================删除================================== -->
    <delete id="delete" parameterType="int">
        DELETE
        FROM ticket_employ
        WHERE ID = #{id, jdbcType=INTEGER}
    </delete>

    <!-- ==================更新 ================================== -->
    <update id="update" parameterType="ticketEmploy">
        UPDATE ticket_employ SET
        login_name=#{loginName,jdbcType=VARCHAR},
        password=#{password,jdbcType=CHAR},
        name=#{name,jdbcType=VARCHAR},
        photo=#{photo,jdbcType=VARCHAR},
        mobile=#{mobile,jdbcType=VARCHAR},
        role_id=#{roleId,jdbcType=INTEGER},
        is_has_message=#{isHasMessage,jdbcType=INTEGER},
        update_time=now(),
        <if test="photoUpdateTime != null">
            photo_update_time=#{photoUpdateTime},
        </if>
        <if test="enterpriseId != null">
            enterprise_id = #{enterpriseId},
        </if>
        status=#{status,jdbcType=INTEGER},
        state=#{state,jdbcType=INTEGER},
        check_ticket_status=#{checkTicketStatus,jdbcType=INTEGER}
        WHERE ID=#{id, jdbcType=INTEGER}
    </update>


    <update id="updateMessage" parameterType="int">
  <![CDATA[
        UPDATE ticket_employ
        SET is_has_message=0,
            update_time=now()
        WHERE ID = #{id, jdbcType=INTEGER}
        ]]>
  </update>

    <!-- 通过条件查询 -->
    <select id="listAll" parameterType="ticketEmploy" resultMap="result-map-ticketEmploy">
        select employ.* , role.role_name roleName from ticket_employ employ left join ticket_role role on employ.role_id
        = role.id where 1
        <if test="null != loginName">
            and employ.login_name = #{loginName}
        </if>
        <if test="null != password">
            and employ.password = #{password}
        </if>
        <if test="enterpriseId != 0">
            and employ.enterprise_id=#{enterpriseId} and employ.role_id != 0
        </if>
        <if test="roleId > 0">
            and employ.role_id = #{roleId}
        </if>
        <if test="status > 0">
            and employ.status = #{status}
        </if>
        <if test="null != mobile">
            and employ.mobile = #{mobile}
        </if>
    </select>

    <!-- ==================更新 ================================== -->
    <update id="updatePic" parameterType="ticketEmploy">
  <![CDATA[
        UPDATE ticket_employ
        SET pic=#{pic},
            update_time=now()
        WHERE ID = #{id, jdbcType=INTEGER}
        ]]>
  </update>

    <select id="listByPage" parameterType="pageBean" resultMap="result-map-ticketEmploy">
        select employ.* , role.role_name roleName from ticket_employ employ left join ticket_role role on employ.role_id
        = role.id
        <include refid="selParams"/>
        order by employ.create_time desc LIMIT #{pageSize} OFFSET #{startItem}
    </select>

    <select id="findByPageCount" parameterType="pageBean" resultType="int">
        select count(employ.id) from ticket_employ employ
        <include refid="selParams"/>
    </select>

    <sql id="selParams">
        <where>
            <if test="params.enterpriseIds != null and params.enterpriseIds != ''">
                and employ.enterprise_id = #{params.enterpriseIds}
            </if>
            <if test="params.enterpriseId != null and params.enterpriseId != ''">
                and employ.enterprise_id = #{params.enterpriseId} and employ.role_id != 0
            </if>
            <if test="params.roleId != null and params.roleId != ''">
                and employ.role_id = #{params.roleId}
            </if>
            <if test="params.status > 0">
                and employ.status = 1
            </if>
            <if test="params.state > 0">
                and employ.state = 1
            </if>
            <if test="params.state == 0">
                and employ.state = 0
            </if>
            <if test="null != params.name and params.name != ''">
                and (employ.name like CONCAT('%',#{params.name},'%') or employ.login_name like
                CONCAT('%',#{params.name},'%'))
            </if>
            and employ.role_id != 0
        </where>
    </sql>
</mapper> 