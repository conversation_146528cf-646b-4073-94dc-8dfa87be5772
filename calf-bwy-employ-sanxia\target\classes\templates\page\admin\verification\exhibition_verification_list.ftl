<#include "layout.ftl"/>
<#macro content>
    <#include "/head.ftl" />
    <div class="main">
        <#include "left_menu.ftl" />
        <!--弹出层-->
        <div id="mask"></div>
        <div class="maskCnt">
            <div class="mk_box">
                <a href="javascript:void(0)" class="mk_del"><i class="iconList"></i></a>
                <div class="mk_bx_cnt">
                </div>
            </div>
        </div>
        <!--#弹出层#-->
        <div class="right">

            <form action="${rootPath}/analysis/exhibition-verification-list.htm" id="thisForm" method="post">

                <!--搜索-->
                <div class="searchBox">
                    <div class="searchBg">
                        <span class="searchTle">搜索</span>

                        <table cellpadding="0" cellspacing="0" border="0" width="100%" class="search_tbl">
                            <tr>
                                <td>
                                    <label class="inputTle">场馆：</label>
                                    <select class="select_300_33" name="enterpriseId" id="enterpriseId" onChange="getInfoList()">
                                        <#list enterpriseList as item>
                                            <option value="${item.id}"
                                                    <#if item.id==enterpriseId>selected</#if>>${item.name}</option>
                                        </#list>
                                    </select>
                                </td>
                                <td width="500"><label class="inputTle">展览名称：</label>
                                    <select class="select_300_33" name="informationId" id="informationId">
                                        <option value="0" <#if 0==informationId>selected</#if>>全部</option>
                                        <#list infoList as item>
                                            <option value="${item.id}" <#if item.id==informationId>selected</#if>>${item.name}</option>
                                        </#list>
                                    </select>
                                </td>
                                <td>
                                    <label class="inputTle">订单编号：</label>
                                    <input class="input_240_33" name="orderSerial" value="${orderSerial}"/>
                                </td>
                                <td ><label class="inputTle">证件号码：</label><input  class="input_240_33" name="identification" value="${identification}" placeholder="支持输入证件号码、手机号、姓名"/></td>
                            </tr>

                            <tr>
                                <td><label class="inputTle">时
                                        <ins class="space25"></ins>
                                        间：</label>
                                    <input onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endDate\');}'})"
                                           class="input_120_33 Wdate" id="startDate"
                                           name="startDate" <#if startDate??> value="${startDate?string("yyyy-MM-dd")}" </#if>/>&nbsp;&nbsp;-&nbsp;&nbsp;
                                    <input onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startDate\');}',maxDate:'%y-%M-%d'})"
                                           class="input_120_33 Wdate" id="endDate"
                                           name="endDate" <#if endDate??> value="${endDate?string("yyyy-MM-dd")}" </#if>/>
                                </td>
                                <td>
                                    <input type="submit" class="btnBg" value="搜索"/>&nbsp;&nbsp;&nbsp;&nbsp;
                                    <input type="button" class="btnBg" value="导出" id="btn-excel"/>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                <!--#搜索#-->

                <div class="blank_10"></div>
                <table cellpadding="0" cellspacing="0" border="0" width="100%" class="listTbl">
                    <tr>
                        <th width="150">订单号</th>
                        <th>展览名称</th>
                        <th>核销时间</th>
                        <th>操作员</th>
                        <th>核销方式</th>
                        <th>核销码</th>
                        <th>核销状态</th>
                    </tr>
                    <#list pageBean.result as item>
                        <tr>
                            <td align="center">${item.orderSerial}</td>
                            <td align="center">${item.informationName}</td>
                            <td align="center"><#if item.verificationTime??>${item.verificationTime?string('yyyy-MM-dd HH:mm:ss')}</#if></td>
                            <td align="center">${item.userName}</td>
                            <td align="center"><#if item.verificationType == 1>电子核销<#elseif item.verificationType == 2>门票核销</#if></td>
                            <td align="center">${item.verificationCode}</td>
                            <td align="center"><#if item.isVerification == 1>核销成功</#if></td>
                        </tr>
                    </#list>
                </table>
                <div class="pageList ">
                    <input type="hidden" name="pageNum" id="pageNum" value="${pageNum}"/>
                    <@c.pagebar 'javascript:doPage({page})'/>
                </div>
            </form>
        </div>
    </div>


</#macro>
<#macro script>
    <script type="text/javascript">
        function doPage(pageNum) {
            $("#pageNum").val(pageNum);
            $("#thisForm").submit();
        }

        $(function () {
            $(".value").click(function () {
                selectVal(this);
            });
            $("#btn-excel").click(function () {
                var sdate = $("#startDate").val();
                var edate = $("#endDate").val();
                if (sdate == '' || edate == '') {
                    myDialog(1, "请筛选日期");
                    return;
                }
                if (GetNumberOfDays(sdate, edate) > 31) {
                    myDialog(1, "单次选择日期的最长跨度为31天");
                    return;
                }

                var searchParam = $("#thisForm").serialize();
                window.location.href = "${rootPath}/analysis/ticket-verification-excel.htm?" + searchParam;
            });
        })

        function GetNumberOfDays(date1, date2) {//获得天数
            //date1：开始日期，date2结束日期
            var a1 = Date.parse(new Date(date1));
            var a2 = Date.parse(new Date(date2));
            var day = parseInt((a2 - a1) / (1000 * 60 * 60 * 24));//核心：时间戳相减，然后除以天数
            return day
        }

        function getInfoList(){
            var html ="<option value=\"0\" selected=\"selected\">全部</option>";
            $("#informationId").empty();
            $("#informationId").append(html);
            var enterpriseId = $("#enterpriseId").val();
            $.ajax({
                type:"post",
                url : "${rootPath}/analysis/informationList.htm?informationType=1&enterpriseId="+ enterpriseId,
                success:function(retData) {
                    if(retData.success) {
                        var jsonList = retData.result;
                        if (jsonList.length > 0) {
                            for (var i = 0; i < jsonList.length; i++) {
                                var e = jsonList[i];
                                $("#informationId").append("<option value=" + e.id + ">" + e.name + "</option>");
                            }
                        }
                    }
                }
            })
        }



    </script>
</#macro>