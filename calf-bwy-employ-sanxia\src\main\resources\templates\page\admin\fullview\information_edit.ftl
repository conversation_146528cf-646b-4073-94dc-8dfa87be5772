<#include "layout.ftl"/>
<#macro content>
    <#include "/head.ftl" />
    <div class="main">
        <!--弹出层-->
        <div id="mask"></div>
        <div class="maskCnt">
            <div class="mk_box">
                <a href="javascript:void(0)" class="mk_del"><i class="iconList"></i></a>
                <div class="mk_bx_cnt">
                </div>
            </div>
        </div>
        <!--#弹出层#-->
        <#include "left_menu.ftl" />
        <div class="right">
            <div class="pisitionDiv">
                <#if cqManagerInformation??&&cqManagerInformation.id gt 0>
                    <span class="pn_tle">修改</span>
                <#else>
                    <span class="pn_tle">新建</span>
                </#if>
                <a href="${rootPath}/cqManagerInformation/loadListPage.htm" class="return pull-right"><i class="smallIconList op_return_icon"></i>返回</a>
            </div>

            <form method="post" enctype="multipart/form-data" id="thisForm">
                <table cellpadding="0" cellspacing="0" border="0" width="100%" class="edit_tbl">
                    <tr>
                        <td height="70" width="120"><label class="inputTle"><span class="required">*</span>名称：</label></td>
                        <td>
                            <input id="name" name="name" class="input_240_33 " value="${cqManagerInformation.name}"/>
                        </td>
                    </tr>
                    <tr>
                        <td height="70" width="70"><label class="inputTle"><span class="required">*</span>网站：</label></td>
                        <td>
                            <input id="url" name="url" class="input_240_33 " value="${cqManagerInformation.url}"/>
                        </td>
                    </tr>
                    <!-- 封面上传 -->
                    <tr>
                        <td height="120" valign="top"><label class="inputTle lh18"><span class="required">*</span>上传封面：</label></td>
                        <td valign="top">
                            <ul class="upload_img ruku_certificate" id="upload_cover">
                                <#if cqManagerInformation.coverPath?? && cqManagerInformation.coverPath != "">
                                    <li><a href="javascript:void(0)">
                                            <div class="imgbox"><img id="coverPicture"
                                                                     src="${picPath}${cqManagerInformation.coverPath}"
                                                                     data-src='${cqManagerInformation.coverPath}'
                                                                     class="smallimg uploadCover"/></div>
                                            <i class="icon delIcon" onclick="removeImage(this, 'cover')"></i></a>
                                    </li>
                                <#else>
                                    <li class="upload_add" id="upload_cover_add">
                                        <a href="javascript:void(0)" onclick="openFileInput('fileCover');">+</a>
                                        <input type="file" class="hidden" id="fileCover"
                                               onchange="uploadImage('cover')"/>
                                    </li>
                                </#if>
                            </ul>
                        </td>
                    </tr>
                    <tr>
                        <td height="120" valign="top"><label class="inputTle lh18">上传资源包：</label></td>
                        <td valign="top">
                            <ul class="upload_img ruku_certificate" id="upload_resource">
                                <#if cqManagerInformation.resourcePath?? && cqManagerInformation.resourcePath != "">
                                    <li>
                                        <a href="javascript:void(0)" class="upload_yxdl uploadResource" data-src='${cqManagerInformation.resourcePath}'>已上传<i class="icon delIcon" onclick="removeVideo(this)"></i></a>
                                        <input type="hidden" id="resourcePath" value="${cqManagerInformation.resourcePath}"/>
                                    </li>
                                <#else>
                                    <li class="upload_add" id="upload_resource_add">
                                        <a href="javascript:void(0)" onclick="openFileInput('resource');">+</a>
                                        <input type="file" class="hidden" id="resource"
                                               onchange="uploadSource()"/>
                                    </li>
                                </#if>
                            </ul>
                        </td>
                    </tr>
                    <tr>
                        <td></td>
                        <td height="70">
                            <#if cqManagerInformation??&&cqManagerInformation.id gt 0>
                                <input type="hidden" id="id" value="${cqManagerInformation.id}"/>
                            </#if>
                            <#if cqManagerInformation??&&cqManagerInformation.id gt 0>
                                <input type="button" class="btnBg btnBg200" value="更新" onclick="doUpdate()"/>
                            <#else>
                                <input type="button" class="btnBg btnBg200" value="新建" onclick="doSave()"/>
                            </#if>
                        </td>
                    </tr>
                </table>
            </form>
        </div>
    </div>


</#macro>
<#macro script>
    <style>
        .required, .textarea_erro_tip {
            color: red;
        }
        .upload_yxdl {
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            width: 100%;
            height: 60px;
            border: 1px dashed #ccc;
            background-color: #f9f9f9;
            position: relative;
        }
        .upload_yxdl .delIcon {
            position: absolute;
            right: 5px;
            top: 5px;
        }
    </style>
    <script type="text/javascript">

        function doSave() {
            var flag = $("#thisForm").valid();
            if (!flag) {
                return
            }
            var data = {};
            if ($("#id").val()) {
                data.id = $("#id").val();
            }
            data.name = $("#name").val();
            data.url = $("#url").val();
            data.coverPath = $(".uploadCover").attr("data-src");
            data.resourcePath = $(".uploadResource").attr("data-src");
            $.ajax({
                dataType: 'json',
                type: 'post',
                data: data,
                url: "${rootPath}/cqManagerInformation/doSave.htm",
                success: function (retData) {
                    if (retData.success) {
                        window.location.href = "${rootPath}/cqManagerInformation/loadListPage.htm";
                    } else {
                        myDialog(1, retData.errorMessage);
                    }
                }
            })
        }
        function doUpdate() {
            var flag = $("#thisForm").valid();
            if (!flag) {
                return
            }
            var data = {};
            data.id = $("#id").val();
            data.name = $("#name").val();
            data.url = $("#url").val();
            data.coverPath = $(".uploadCover").attr("data-src");
            data.resourcePath = $(".uploadResource").attr("data-src");
            $.ajax({
                dataType: 'json',
                type: 'post',
                data: data,
                url: "${rootPath}/cqManagerInformation/doUpdate.htm",
                success: function (retData) {
                    if (retData.success) {
                        window.location.href = "${rootPath}/cqManagerInformation/loadListPage.htm";
                    } else {
                        myDialog(1, retData.errorMessage);
                    }
                }
            })
        }
        function openFileInput(fileId) {
            document.getElementById(fileId).click();
        }

        function uploadSource() {
            var file = document.getElementById("resource").files[0];  //file文件
            var fileName = document.getElementById("resource").value;  //file的文件名
            // var AllImgExt = ",bin|";
            // var extName = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();//（把路径中的所有字母全部转换为小写）
            // if (AllImgExt.indexOf(extName + "|") == -1) {
            //     ErrMsg = "该文件类型不允许上传。请上传 " + AllImgExt + " 类型的文件，当前文件类型为" + extName;
            //     alert(ErrMsg);
            //     return false;
            // }

            var formData = new FormData();
            formData.append("file", file);
            $.ajax({
                url: swfUploadPath+"?bwy="+getCookie("sxsession") + '&dir=media',
                type: "POST",
                data: formData,
                /**
                 *必须false才会自动加上正确的Content-Type
                 */
                contentType: false,
                /**
                 * 必须false才会避开jQuery对 formdata 的默认处理
                 * XMLHttpRequest会对 formdata 进行正确的处理
                 */
                processData: false,
                success: function (data) {
                    var img_obj = new Function("return" + data)();//转换后的JSON对象
                    console.log(img_obj.url)
                    alert("上传成功！");
                    // 模仿 virtu_edit.ftl 中的显示效果
                    var html = '<li>' +
                        '<a href="javascript:void(0)" class="upload_yxdl uploadResource" data-src="' + img_obj.relative_path + '">已上传<i class="icon delIcon" onclick="removeVideo(this)"></i></a>' +
                        '<input type="hidden" id="resourcePath" value="' + img_obj.relative_path + '"/>' +
                        '</li>';
                    $("#upload_resource").html(html);
                },
                error: function () {
                    alert("上传失败！");
                }
            });
        }


        function uploadImage(type) {
            var fileInput = document.getElementById('file' + type.charAt(0).toUpperCase() + type.slice(1));
            var file = fileInput.files[0];
            var fileName = fileInput.value;
            var AllImgExt = ".jpg|.png|.gif|.bmp|.jpeg|";
            var extName = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();

            if (AllImgExt.indexOf(extName + "|") == -1) {
                alert("该文件类型不允许上传。请上传 " + AllImgExt + " 类型的文件，当前文件类型为" + extName);
                return false;
            }

            var currentCount = $("#upload_" + type + " li:not(.upload_add)").length;
            if (type === 'cover' && currentCount >= 1) {
                alert("封面只能上传1张图片");
                return false;
            } else if (type !== 'cover' && currentCount >= 5) {
                alert("最多只能上传5张图片");
                return false;
            }

            var formData = new FormData();
            formData.append("file", file);

            $.ajax({
                url: swfUploadPath+"?bwy="+getCookie("sxsession"),
                type: "POST",
                data: formData,
                contentType: false,
                processData: false,
                success: function (data) {
                    var img_obj = new Function("return" + data)();
                    var html = '<li><a href="javascript:void(0)"><div class="imgbox"><img src="${picPath}/' + img_obj.path + '" data-src="' + img_obj.path + '" class="smallimg upload' + type.charAt(0).toUpperCase() + type.slice(1) + '"/></div><i class="icon delIcon" onclick="removeImage(this, \'' + type + '\')"></i></a></li>';
                    $("#upload_" + type).prepend(html);
                    updateImagePaths(type);

                    // 更新当前图片数量
                    currentCount = $("#upload_" + type + " li:not(.upload_add)").length;

                    // 如果是cover类型，或者其他类型达到5张图片时，隐藏上传按钮
                    if (type === 'cover' || currentCount >= 5) {
                        $("#upload_" + type + "_add").hide();
                    } else {
                        $("#upload_" + type + "_add").show();
                    }

                    // 清空文件输入，以便可以重复上传同一文件
                    fileInput.value = '';
                },
                error: function () {
                    alert("上传失败！");
                }
            });
        }
        function updateImagePaths(type) {
            var paths = [];
            $('#upload_' + type + ' img').each(function () {
                paths.push($(this).attr('data-src'));
            });
            $('#' + type + 'Paths').val(paths.join(','));
        }
        function removeImage(obj, type) {
            var $li = $(obj).closest('li');
            var imageSrc = $li.find('img').attr('data-src');

            if (type === 'cover') {
                // 重新显示上传按钮
                var uploadButton = '<li class="upload_add" id="upload_cover_add">' +
                    '<a href="javascript:void(0)" onclick="openFileInput(\'fileCover\');">+</a>' +
                    '<input type="file" class="hidden" id="fileCover" onchange="uploadImage(\'cover\')"/>' +
                    '</li>';
                $("#upload_cover").html(uploadButton);
            }

            $.ajax({
                url: '${rootPath}/bwy/fileOperate/delFile.htm',
                type: 'POST',
                data: {path: imageSrc},
                dataType: 'json',
                success: function (response) {
                    if (response.success) {
                        $li.remove();
                        updateImagePaths(type);

                        // 更新当前图片数量
                        var currentCount = $("#upload_" + type + " li:not(.upload_add)").length;

                        // 如果图片数量小于5，显示上传按钮
                        if (currentCount < 5) {
                            $("#upload_" + type + "_add").show();
                        }
                    } else {
                        alert('删除图片失败: ' + (response.message || '未知错误'));
                    }
                },
                error: function () {
                    alert('删除图片时发生错误,请稍后再试');
                }
            });
        }


    </script>
</#macro>