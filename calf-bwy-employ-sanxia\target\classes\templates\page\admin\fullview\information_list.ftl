<#include "layout.ftl"/>
<#macro content>
    <#include "/head.ftl" />
    <div class="main">
        <!--弹出层-->
        <div id="mask"></div>
        <div class="maskCnt">
            <div class="mk_box">
                <a href="javascript:void(0)" class="mk_del"><i class="iconList"></i></a>
                <div class="mk_bx_cnt">
                </div>
            </div>
        </div>
        <!--#弹出层#-->
        <#include "left_menu.ftl" />
        <!--right-->
        <form action="${rootPath}/cqManagerInformation/loadListPage.htm" method="post" id="thisForm">

            <div class="right">
                <div class="pisitionDiv">
                    <div class="pn_tle">展览管理</div>
                    <ul class="operaList">
                        <li><a class="btnBg3" href="${rootPath}/cqManagerInformation/loadEditPage.htm">+ 添加</a></li>
                    </ul>
                </div>

                <!--搜索-->
                <div class="searchBox">
                    <div class="searchBg">
                        <span class="searchTle">搜索</span>
                        <table cellpadding="0" cellspacing="0" border="0" width="100%" class="search_tbl">
                            <tr>
                                <#if enterpriseList??>
                                    <td><label class="inputTle">场馆：</label>
                                        <select class="select_300_33" name="slaveEnterpriseId">
                                            <#list enterpriseList as item>
                                                <option value="${item.id}"
                                                        <#if item.id==slaveEnterpriseId>selected</#if>>${item.name}</option>
                                            </#list>
                                        </select>
                                    </td>
                                </#if>
                                <td><label class="inputTle">展览名称：</label><input class="input_240_33" name="name"
                                                                                value="${cqManagerInformation.name}"
                                                                                placeholder="请输入名称"/></td>
                                <td>
                                    <input type="submit" class="btnBg" value="查询"/>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!--list-->
                <table cellpadding="0" cellspacing="0" border="0" width="100%" class="listTbl">
                    <tr>

                        <th>名称</th>
                        <th>展览缩略图</th>
                        <th>热点数量</th>
                        <th>日期</th>
                        <th>资源包</th>
                        <th>操作</th>
                    </tr>
                    <#list pageBean.result as item>
                        <tr>

                            <td align="center">${item.name}</td>
                            <td align="center">
                                <#if item.coverPath??>
                                    <#assign paths = item.coverPath?split(",")>
                                    <#if paths?size gt 0>
                                        <a href="javascript:void(0)"><img class="img_td"
                                                                          data-src="${picPath}/${paths[0]}"
                                                                          src="${picPath}/${paths[0]}"
                                                                          width="60"/></a>
                                    </#if>
                                </#if>
                            </td>
                            <td align="center">${item.hotCount}</td>
                            <td align="center">${item.createTime?string("yyyy-MM-dd HH:mm:ss")}</td>
                            <td align="center">
                                <#if item.resourcePath?? && item.resourcePath != "">
                                    <a href="javascript:void(0)" onclick="downloadFile('${picPath}${item.resourcePath}')" class="file-download">
                                        <i class="op_download"></i>下载
                                    </a>
                                </#if>
                            </td>
                            <td align="center">
                                <a href="${rootPath}/cqManagerInformation/loadEditPage.htm?id=${item.id}"
                                   class="opera_icon_btn"><i class="smallIconList op_edit_icon"></i>编辑</a>
                                &nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:void(0)" class="opera_icon_btn"
                                   onclick="deleteItem(${item.id})"><i class="smallIconList op_stop_icon"></i>删除</a>
                            </td>
                        </tr>
                    </#list>
                </table>
                <div class="pageList">
                    <input type="hidden" name="pageNum" id="pageId"/>
                    <@c.pagebar 'javascript:doPage({page})'/>
                </div>
            </div>
        </form>
        <!--#right#-->
    </div>


</#macro>
<#macro script>
    <script type="text/javascript">
        $(function () {
            $(".value").click(function () {
                selectVal(this);
            });
        });

        // 翻页
        function doPage(page) {
            $("#pageId").val(page);
            $("#thisForm").submit();
        }

        // 删除操作
        function deleteItem(id) {
            myDialog(1, "确定要删除吗？", function () {
                $.ajax({
                    type: "post",
                    url: "${rootPath}/cqManagerInformation/doDelete.htm",
                    data: {id: id},
                    success: function (response) {
                        if (response.success) {
                            myDialog(1, "删除成功");
                            window.location.reload();
                        } else {
                            myDialog(0, "删除失败");
                        }
                    }
                });
            })
        }
        function downloadFile(url) {
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.target = '_blank';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        }

    </script>
</#macro>
