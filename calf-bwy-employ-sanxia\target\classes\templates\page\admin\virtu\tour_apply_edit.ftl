<#include "layout.ftl"/>
<#macro content>
    <#include "/head.ftl" />
    <div class="main">
        <!--弹出层-->
        <div id="mask"></div>
        <div class="maskCnt">
            <div class="mk_box">
                <a href="javascript:void(0)" class="mk_del"><i class="iconList"></i></a>
                <div class="mk_bx_cnt">
                </div>
            </div>
        </div>
        <!--#弹出层#-->
        <#include "left_menu.ftl" />
        <div class="right">
            <div class="pisitionDiv">
                <a href="${rootPath}/tourApply/loadListPage.htm" class="return pull-right"><i
                            class="smallIconList op_return_icon"></i>返回</a>
            </div>
            <form method="post" enctype="multipart/form-data" id="thisForm">
                <table cellpadding="0" cellspacing="0" border="0" width="100%" class="edit_tbl">
                    <tr>
                        <td height="70" width="10%"><label class="inputTle">巡展名称：</label></td>
                        <td>${tourApply.tourName}</td>
                    </tr>
                    <tr>
                        <td height="70" width="100"><label class="inputTle">单位名称：</label></td>
                        <td>${tourApply.unitName}</td>
                    </tr>
                    <tr>
                        <td height="70" width="100"><label class="inputTle">展览时间：</label></td>
                        <td>${tourApply.startDate?string("yyyy-MM-dd")} ~ ${tourApply.endDate?string("yyyy-MM-dd")}</td>
                    </tr>
                    <tr>
                        <td height="70" width="100"><label class="inputTle">联系人：</label></td>
                        <td>${tourApply.contactName}</td>
                    </tr>
                    <tr>
                        <td height="70" width="100"><label class="inputTle">联系电话：</label></td>
                        <td>${tourApply.contactPhone}</td>
                    </tr>
                    <tr>
                        <td height="70" width="100"><label class="inputTle">是否需要配套教育：</label></td>
                        <td>
                            <#if tourApply.needActivity == 1>
                                需要
                            <#else>
                                不需要
                            </#if>
                        </td>
                    </tr>
                    <tr>
                        <td height="70" width="100"><label class="inputTle">现场图片：</label></td>
                        <td valign="top">
                            <ul class="upload_img ruku_certificate" id="upload_size">
                                <#if tourApply.scenePicPath?? && tourApply.scenePicPath != "">
                                    <#list tourApply.scenePicPath?split(",") as imagePath>
                                        <#if imagePath_index < 5>
                                            <li>
                                                <a href="javascript:void(0)" onclick="showImageModal(this)">
                                                    <div class="imgbox"><img src="<#if imagePath?trim?starts_with('http')>${imagePath?trim}<#else>${picPath}${imagePath?trim}</#if>"
                                                                             data-src='${imagePath?trim}'
                                                                             class="smallimg uploadSize"/></div>
                                                </a>
                                            </li>
                                        </#if>
                                    </#list>
                                </#if>
                            </ul>
                        </td>
                    </tr>
                </table>
            </form>
        </div>
    </div>


</#macro>
<#macro script>
    <style>
        /* 图片放大模态框样式 */
        .image-modal {
            display: none;
            position: fixed;
            z-index: 9999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            cursor: pointer;
            overflow: auto;
            padding: 20px;
            box-sizing: border-box;
        }

        .image-modal-content {
            position: relative !important;
            display: inline-block !important;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
            cursor: default;
            width: auto !important;
            height: auto !important;
            max-width: none !important;
            max-height: none !important;
        }

        .image-modal img {
            display: block !important;
            border-radius: 8px;
            /* 显示图片的真实原始尺寸，1:1像素显示 */
            width: auto !important;
            height: auto !important;
            max-width: none !important;
            max-height: none !important;
            min-width: auto !important;
            min-height: auto !important;
            object-fit: none !important;
            image-rendering: -webkit-optimize-contrast;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
        }

        .image-modal-close {
            position: absolute;
            top: 15px;
            right: 25px;
            color: #fff;
            font-size: 35px;
            font-weight: bold;
            cursor: pointer;
            z-index: 10000;
        }

        .image-modal-close:hover {
            color: #ccc;
        }

        /* 小图片悬停效果 */
        .smallimg:hover {
            opacity: 0.8;
            cursor: pointer;
        }
    </style>

    <!-- 图片放大模态框HTML -->
    <div id="imageModal" class="image-modal" onclick="closeImageModal()">
        <span class="image-modal-close" onclick="closeImageModal()">&times;</span>
        <div class="image-modal-content">
            <img id="modalImage" src="" alt="放大图片">
        </div>
    </div>

    <script type="text/javascript">
        // 显示图片放大模态框
        function showImageModal(element) {
            var img = element.querySelector('img');
            var imgSrc = img.src;

            var modalImage = document.getElementById('modalImage');
            var modal = document.getElementById('imageModal');

            modalImage.src = imgSrc;
            modal.style.display = 'block';

            // 阻止页面滚动
            document.body.style.overflow = 'hidden';
        }

        // 关闭图片放大模态框
        function closeImageModal() {
            document.getElementById('imageModal').style.display = 'none';

            // 恢复页面滚动
            document.body.style.overflow = 'auto';
        }

        // ESC键关闭模态框
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeImageModal();
            }
        });

        // 阻止模态框内容区域的点击事件冒泡
        document.addEventListener('DOMContentLoaded', function() {
            var modalContent = document.querySelector('.image-modal-content');
            if (modalContent) {
                modalContent.addEventListener('click', function(event) {
                    event.stopPropagation();
                });
            }
        });
    </script>
</#macro>