package com.taoart.calf.bwy.employ.controller.fullview;

import com.taoart.calf.bwy.domain.TicketEmploy;
import com.taoart.calf.bwy.domain.fullview.CqManagerInformation;
import com.taoart.calf.bwy.employ.IgnoreAuth;
import com.taoart.calf.bwy.employ.SessionContextUtils;
import com.taoart.calf.bwy.employ.controller.DefaultController;
import com.taoart.calf.bwy.service.EmployEnterpriseAuthService;
import com.taoart.calf.bwy.service.EmployOperLogService;
import com.taoart.calf.bwy.service.fullview.CqManagerInformationService;
import com.taoart.calf.wypw.domain.Enterprise;
import com.taoart.common.bean.JsonResponse;
import com.taoart.common.bean.PageBean;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 展览管理
 */
@Controller
@RequestMapping("cqManagerInformation")
public class CqManagerInformationController extends DefaultController {

    private static final Log log = LogFactory.getLog(CqManagerInformationController.class);

    @Autowired
    private CqManagerInformationService cqManagerInformationService;

    @Autowired
    private EmployEnterpriseAuthService employEnterpriseAuthService;

    @Autowired
    private EmployOperLogService employOperLogService;

    /**
     * 加载列表页面
     */
    @RequestMapping("loadListPage")
    public ModelAndView loadListPage(@RequestParam(defaultValue = "1") int pageNum, CqManagerInformation cqManagerInformation, @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        ModelAndView mav = new ModelAndView("fullview/information_list");
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        List<Enterprise> enterpriseList = employEnterpriseAuthService.enterpriseListByEmployId(employ.getId());
        Map<String, Object> map = new HashMap<String, Object>();
        if (slaveEnterpriseId == 0) {
            map.put("enterpriseId", employ.getEnterpriseId());
        } else {
            map.put("enterpriseId", slaveEnterpriseId);
        }

        map.put("name", cqManagerInformation.getName());
        PageBean<CqManagerInformation> pageBean = cqManagerInformationService.listByPage(map, pageNum, 10);
        mav.addObject("pageBean", pageBean);
        mav.addObject("cqManagerInformation", cqManagerInformation);
        mav.addObject("enterpriseList", enterpriseList);
        mav.addObject("slaveEnterpriseId", slaveEnterpriseId);
        employOperLogService.insertLog(employ.getEnterpriseId(), employ.getSystemType(), employ.getId(), 4, "【展览管理】查看展览列表");
        return mav;
    }


    /**
     * 新增
     */
    @RequestMapping("doSave")
    @ResponseBody
    public JsonResponse doInsert(CqManagerInformation cqManagerInformation) {
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        cqManagerInformation.setEnterpriseId(employ.getEnterpriseId());
        cqManagerInformation.setCreateTime(new Date());
        cqManagerInformation.setUpdateTime(new Date());
        try {
            this.cqManagerInformationService.insert(cqManagerInformation);
            employOperLogService.insertLog(employ.getEnterpriseId(), employ.getSystemType(), employ.getId(), 1, "【展览管理】新增展览:"+cqManagerInformation.getName());
        } catch (Exception e) {
            log.error(e.getMessage());
            return this.fail(e.getMessage());
        }
        return this.success();
    }

    /**
     * 通过主键删除
     */
    @RequestMapping("doDelete")
    @ResponseBody
    public JsonResponse doDelete(int id) {
        try {
            TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
            CqManagerInformation cqManagerInformation = cqManagerInformationService.selectById(id);
            this.cqManagerInformationService.delete(id);
            employOperLogService.insertLog(employ.getEnterpriseId(), employ.getSystemType(), employ.getId(), 2, "【展览管理】删除展览:" + cqManagerInformation.getName());
        } catch (Exception e) {
            log.error(e.getMessage());
            return this.fail(e.getMessage());
        }
        return this.success();
    }

    /**
     * 更新
     */
    @RequestMapping("doUpdate")
    @ResponseBody
    public JsonResponse doUpdate(CqManagerInformation cqManagerInformation) {
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        cqManagerInformation.setEnterpriseId(employ.getEnterpriseId());
        cqManagerInformation.setUpdateTime(new Date());
        try {
            this.cqManagerInformationService.update(cqManagerInformation);
            employOperLogService.insertLog(employ.getEnterpriseId(), employ.getSystemType(), employ.getId(), 3, "【展览管理】修改展览:" + cqManagerInformation.getName());
        } catch (Exception e) {
            log.error(e.getMessage());
            return this.fail(e.getMessage());
        }
        return this.success();
    }


    /**
     * 加载新增,修改页面
     */
    @RequestMapping("loadEditPage")
    public ModelAndView loadEditPage(@RequestParam(defaultValue = "0") int id) {
        ModelAndView mav = new ModelAndView("fullview/information_edit");
        if (id > 0) {
            CqManagerInformation cqManagerInformation = this.cqManagerInformationService.selectById(id);
            mav.addObject("cqManagerInformation", cqManagerInformation);
        }
        return mav;
    }


    @RequestMapping("loadListPageAjax")
    @ResponseBody
    @IgnoreAuth
    public JsonResponse loadListPageAjax(@RequestParam(defaultValue = "1") int pageNum, CqManagerInformation cqManagerInformation) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("enterpriseId", cqManagerInformation.getEnterpriseId());
        map.put("name", cqManagerInformation.getName());
        PageBean<CqManagerInformation> pageBean = cqManagerInformationService.listByPage(map, pageNum, 10);
        return this.success(pageBean);
    }


    @RequestMapping("loadEditPageAjax")
    @ResponseBody
    @IgnoreAuth
    public JsonResponse loadEditPageAjax(@RequestParam(defaultValue = "0") int id) {
        CqManagerInformation cqManagerInformation = this.cqManagerInformationService.selectById(id);
        return this.success(cqManagerInformation);
    }
}