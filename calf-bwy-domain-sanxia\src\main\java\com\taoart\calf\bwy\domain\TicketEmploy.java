package com.taoart.calf.bwy.domain;

import com.taoart.common.domain.BaseDomain;
import org.apache.commons.collections.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * 
 * @ClassName: TicketEmploy
 * @Description: TODO
 * <AUTHOR>
 * @Date 2017年05月12日 11:21:21
 * @Modify
 * @CopyRight 杭州淘艺数据技术有限公司
 */
public class TicketEmploy extends BaseDomain {

	private static final long serialVersionUID = 9161829852839L;

	/** 登录名 */
	private String loginName;
	/** 登录密码 */
	private String password;
	/** 姓名 */
	private String name;
	/** 角色id */
	private int roleId;
	private String roleName;
	private int enterpriseId;
	private String enterpriseName;
	private String storeName;
	private int storeId;
	private String pic;
	private String photo;
	private String postion;
	private int isHasMessage;
	private String path;
	private String logoPic;
	private String authStrs;
	private String mobile;
	private int status;
	private Date photoUpdateTime;
	
	private String systemType;
	private List<TicketAuth> authList;

	private int checkTicketStatus;


	//是否软删除
	private int state;

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public String getEnterpriseName() {
		return enterpriseName;
	}

	public void setEnterpriseName(String enterpriseName) {
		this.enterpriseName = enterpriseName;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public List<TicketAuth> getAuthList() {
		return authList;
	}

	public void setAuthList(List<TicketAuth> authList) {
		this.authList = authList;
	}

	public String getRoleName() {
		return roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}

	public int getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(int enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}

	public String getLoginName() {
		return loginName;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getPassword() {
		return password;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getName() {
		return name;
	}

	public void setRoleId(int roleId) {
		this.roleId = roleId;
	}

	public int getRoleId() {
		return roleId;
	}

	public String getStoreName() {
		return storeName;
	}

	public void setStoreName(String storeName) {
		this.storeName = storeName;
	}

	public int getStoreId() {
		return storeId;
	}

	public void setStoreId(int storeId) {
		this.storeId = storeId;
	}

	public String getPic() {
		return pic;
	}

	public void setPic(String pic) {
		this.pic = pic;
	}

	public String getPostion() {
		return postion;
	}

	public void setPostion(String postion) {
		this.postion = postion;
	}

	public int getIsHasMessage() {
		return isHasMessage;
	}

	public void setIsHasMessage(int isHasMessage) {
		this.isHasMessage = isHasMessage;
	}
	
	
	public void setAuthStrs(String authStrs) {
		this.authStrs = authStrs;
	}

	public String getAuthStrs() {
		String str = "";
		if (CollectionUtils.isNotEmpty(authList)) {
			for (TicketAuth auth : authList) {
				str += auth.getAuthUrl();
			}
		}
		return str;
	}

	public String getLogoPic() {
		return logoPic;
	}

	public void setLogoPic(String logoPic) {
		this.logoPic = logoPic;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getPhoto() {
		return photo;
	}

	public void setPhoto(String photo) {
		this.photo = photo;
	}

	public Date getPhotoUpdateTime() {
		return photoUpdateTime;
	}

	public void setPhotoUpdateTime(Date photoUpdateTime) {
		this.photoUpdateTime = photoUpdateTime;
	}

	public String getSystemType() {
		return systemType;
	}

	public void setSystemType(String systemType) {
		this.systemType = systemType;
	}

	public int getCheckTicketStatus() {
		return checkTicketStatus;
	}

	public void setCheckTicketStatus(int checkTicketStatus) {
		this.checkTicketStatus = checkTicketStatus;
	}
}