<#include "layout.ftl"/>
<#macro content>
    <#include "/head.ftl" />
    <div class="main">
        <#include "left_menu.ftl" />
        <!--弹出层-->
        <div id="mask"></div>
        <div class="maskCnt">
            <div class="mk_box">
                <a href="javascript:void(0)" class="mk_del"><i class="iconList"></i></a>
                <div class="mk_bx_cnt">
                </div>
            </div>
        </div>
        <!--#弹出层#-->
        <div class="right">

            <div class="tabBox">
                <ul class="tabNav pull-left">
                    <li class="active"><a href="javascript:void(0)">门票资金</a></li>
                </ul>
            </div>

            <form action="${rootPath}/fund/statement/loadListPage.htm" id="thisForm" method="post">

                <!--搜索-->
                <div class="searchBox">
                    <div class="searchBg">
                        <span class="searchTle">搜索</span>

                        <table cellpadding="0" cellspacing="0" border="0" width="100%" class="search_tbl">
                            <tr>
                                <#if enterpriseList??>
                                    <td width="500">
                                        <label class="inputTle">场馆：</label>
                                        <select class="select_200_33" name="slaveEnterpriseId" id="slaveEnterpriseId"
                                                onchange="filterInfoList(this.value)">
                                            <#if slaveEnterpriseId == 0>
                                                <option value="0" selected>全部</option>
                                            </#if>
                                            <#list enterpriseList as item>
                                                <option value="${item.id}"
                                                        <#if item.id == slaveEnterpriseId && slaveEnterpriseId != 0>selected</#if>
                                                >${item.name}</option>
                                            </#list>
                                        </select>
                                    </td>
                                </#if>
                                <td>
                                    <label class="inputTle">展览：</label>
                                    <select class="select_200_33" name="informationId" id="informationId">
                                        <option value="">请选择展览</option>
                                    </select>
                                </td>
                                <td>
                                    <label class="inputTle">类型：</label>
                                    <select class="input_120_33" name="type">
                                        <option value="">全部</option>
                                        <option value="1" <#if type=='1'>selected</#if>>收入</option>
                                        <option value="2" <#if type=='2'>selected</#if>>退款</option>
                                        <option value="3" <#if type=='3'>selected</#if>>提现</option>
                                        <option value="4" <#if type=='4'>selected</#if>>提现拒绝</option>
                                    </select>
                                </td>

                            </tr>

                            <tr>
                                <td>  <label class="inputTle">时间：</label>
                                    <ul class="dataSearch my_today">
                                        <li data-days="1" class="btnBg">今天</li>
                                    </ul>
                                    <input onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endDate\');}'})"
                                           class="input_120_33 Wdate" id="startDate" name="startDate"
                                           value="${startDate?string("yyyy-MM-dd")}"/>&nbsp;&nbsp;-&nbsp;&nbsp;
                                    <input onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startDate\');}',maxDate:'%y-%M-%d'})"
                                           class="input_120_33 Wdate" id="endDate" name="endDate"
                                           value="${endDate?string("yyyy-MM-dd")}"/>

                                </td>
                                <td>
                                    <input type="submit" class="btnBg" value="确定"/>
                                    <input type="button" class="btnBg" id="btn-excel" value="资金流水"/>

                                    <input type="button" class="btnBg" id="btn-excel-daily" value="每日报表"/>

                                    <input type="button" class="btnBg" id="btn-excel-sale" value="每日销售额"/>

                                    <#if enterpriseId==4>
                                    <input type="button" class="btnBg" onclick="performance();" value="销售员报表"/>
                                    </#if>
                                    <#if enterpriseId==4>
                                    <input type="button" class="btnBg" onclick="saleReport();" value="营业报表"/>
                                    </#if>

                                    <#if  enterpriseId == 2 || enterpriseId == 4>
                                        <input type="button" class="btnBg" id="btn-excel-viewdate"
                                               value="每日销售分析"/>
                                        <input type="button" class="btnBg" id="btn-excel-information"
                                               value="票务销售分析"/>
                                    </#if>
                                </td>
                            </tr>
                        </table>


                    </div>
                </div>

                <div class="blank_10"></div>
                <table cellpadding="0" cellspacing="0" border="0" width="100%" class="listTbl">
                    <tr>
                        <th width="150">时间</th>
                        <th>类型</th>
                        <th>订单号</th>
                        <th>名称</th>
                        <th>支付方式</th>
                        <th>说明</th>
                        <th>金额(元)</th>
                    </tr>
                    <#list pageBean.result as item>
                        <tr>
                            <td align="center">${item.createTime?string('yyyy-MM-dd')} ${item.createTime?string('HH:mm:ss')}</td>
                            <td align="center">
                                <#if item.type==1>
                                    收入
                                </#if>
                                <#if item.type==2>
                                    退款
                                </#if>
                                <#if item.type==3>
                                    提现
                                </#if>
                                <#if item.type==4>
                                    提现
                                </#if>
                            </td>
                            <td align="center">${item.orderSerial}</td>
                            <td align="center">
                                <#list infoList as info>
                                    <#if info.id == item.informationId>
                                        ${info.name}
                                    </#if>
                                </#list>
                            </td>
                            <td align="center">
                                <#if item.payWay==0>--</#if>
                                <#if item.payWay==1>微信</#if>
                                <#if item.payWay==2>支付宝</#if>
                                <#if item.payWay==3>现金</#if>
                                <#if item.payWay==4>余额</#if>
                                <#if item.payWay==5>银联</#if>
                                <#if item.payWay==6>银联刷卡</#if>
                            </td>
                            <td align="center">${item.feeRmk}</td>
                            <td align="center"
                                style="<#if item.type==1>;color:red</#if><#if item.type==4>;color:#ccc</#if>">
                                <#if item.type==1>+</#if>
                                <#if item.type==2 || item.type==3>-&nbsp;</#if>

                                ${item.fee?string('0.00')}
                            </td>
                        </tr>
                    </#list>
                </table>
                <div class="pageList ">
                    <input type="hidden" name="pageNum" id="pageNum" value="${pageNum}"/>
                    <@c.pagebar 'javascript:doPage({page})'/>
                </div>
            </form>
        </div>
    </div>


</#macro>
<#macro script>
    <script type="text/javascript">
        function doPage(pageNum) {
            $("#pageNum").val(pageNum);
            $("#thisForm").submit();
        }

        function changeInfo(infoId) {
            var data = {};
            data.informationId = infoId;
            if (infoId == 0) {
                $("#btn-excel-sale-nb").show();
                $("#btn-excel-daily").show();
                $("#btn-excel-daily-nb").show();
                $("#btn-excel-sale").show();
                $("#btn-excel-explain").show();
                $("#btn-excel-viewdate").show();
                $("#btn-excel-information").show();
            } else {
                $.ajax({
                    dataType: 'json',
                    type: 'post',
                    data: data,
                    url: "${rootPath}/fund/selectInformationById.htm",
                    success: function (retData) {
                        if (retData.success) {
                            var type = retData.result.type;
                            if (type == 1) {
                                $("#btn-excel-information").show();
                            } else {
                                $("#btn-excel-information").hide();
                            }
                            if (type == 4) {
                                $("#btn-excel-sale-nb").hide();
                                $("#btn-excel-daily").hide();
                                $("#btn-excel-daily-nb").hide();
                                $("#btn-excel-sale").hide();
                                $("#btn-excel-explain").hide();
                                $("#btn-excel-viewdate").hide();
                            } else {
                                $("#btn-excel-sale-nb").show();
                                $("#btn-excel-daily").show();
                                $("#btn-excel-daily-nb").show();
                                $("#btn-excel-sale").show();
                                $("#btn-excel-explain").show();
                                $("#btn-excel-viewdate").show();
                            }
                        } else {
                            myDialog(1, retData.errorMessage);
                        }
                    }
                })
            }
        }

        $(function () {
            changeInfo('${informationId}');
            $(".dataSearch li").click(function () {
                $(".dataSearch li").removeClass("btnBg");
                $(this).addClass("btnBg");
                var days = $(this).attr("data-days");
                $("#endDate").val(new Date().format("yyyy-MM-dd"));
                var m = new Date().getTime() - 86400000 * (days - 1);
                $("#startDate").val(new Date(m).format("yyyy-MM-dd"));
                $("#thisForm").submit();
            });

            $("#btn-excel").click(function () {
                var searchParam = $("#thisForm").serialize();
                window.location.href = "${rootPath}/fund/statement/exportFlow.htm?" + searchParam;
            });

            $("#btn-excel-daily").click(function () {
                if ($("input[name='informationId']").val() == 0) {
                    myDialog(1, "请选择展览");
                    return;
                }
                var sdate = $("#startDate").val();
                var edate = $("#endDate").val();
                if (sdate == '' || edate == '') {
                    myDialog(1, "请筛选日期");
                    return;
                }
                if (GetNumberOfDays(sdate, edate) > 31) {
                    myDialog(1, "单次选择日期的最长跨度为31天");
                    return;
                }
                var searchParam = $("#thisForm").serialize();
                window.location.href = "${rootPath}/fund/statement/exportReport.htm?" + searchParam;
            });


            $("#btn-excel-daily-nb").click(function () {
                if ($("input[name='informationId']").val() == 0) {
                    myDialog(1, "请选择展览");
                    return;
                }
                var sdate = $("#startDate").val();
                var edate = $("#endDate").val();
                if (sdate == '' || edate == '') {
                    myDialog(1, "请筛选日期");
                    return;
                }
                if (GetNumberOfDays(sdate, edate) > 31) {
                    myDialog(1, "单次选择日期的最长跨度为31天");
                    return;
                }
                var searchParam = $("#thisForm").serialize();
                window.location.href = "${rootPath}/fund/fund-log-excel-daily-nb.htm?" + searchParam;
            });

            $("#btn-excel-sale").click(function () {
                if ($("input[name='informationId']").val() == 0) {
                    myDialog(1, "请选择展览");
                    return;
                }
                var sdate = $("#startDate").val();
                var edate = $("#endDate").val();
                if (sdate == '' || edate == '') {
                    myDialog(1, "请筛选日期");
                    return;
                }
                if (GetNumberOfDays(sdate, edate) > 31) {
                    myDialog(1, "单次选择日期的最长跨度为31天");
                    return;
                }
                var searchParam = $("#thisForm").serialize();
                window.location.href = "${rootPath}/fund/statement/exportSales.htm?" + searchParam;
            });

            $("#btn-excel-sale-nb").click(function () {
                if ($("input[name='informationId']").val() == 0) {
                    myDialog(1, "请选择展览");
                    return;
                }
                var sdate = $("#startDate").val();
                var edate = $("#endDate").val();
                if (sdate == '' || edate == '') {
                    myDialog(1, "请筛选日期");
                    return;
                }
                if (GetNumberOfDays(sdate, edate) > 31) {
                    myDialog(1, "单次选择日期的最长跨度为31天");
                    return;
                }
                var searchParam = $("#thisForm").serialize();
                window.location.href = "${rootPath}/fund/fund-log-excel-sale-nb.htm?" + searchParam;
            });

            $("#btn-excel-explain").click(function () {
                if ($("input[name='informationId']").val() == 0) {
                    myDialog(1, "请选择展览");
                    return;
                }
                var sdate = $("#startDate").val();
                var edate = $("#endDate").val();
                if (sdate == '' || edate == '') {
                    myDialog(1, "请筛选日期");
                    return;
                }
                if (GetNumberOfDays(sdate, edate) > 31) {
                    myDialog(1, "单次选择日期的最长跨度为31天");
                    return;
                }
                var searchParam = $("#thisForm").serialize();
                window.location.href = "${rootPath}/fund/fund-log-excel-explain.htm?" + searchParam;
            });

            $("#btn-excel-viewdate").click(function () {
                if ($("input[name='informationId']").val() == 0) {
                    myDialog(1, "请选择展览");
                    return;
                }
                var sdate = $("#startDate").val();
                var edate = $("#endDate").val();
                if (sdate == '' || edate == '') {
                    myDialog(1, "请筛选日期");
                    return;
                }
                if (GetNumberOfDays(sdate, edate) > 31) {
                    myDialog(1, "单次选择日期的最长跨度为31天");
                    return;
                }
                var searchParam = $("#thisForm").serialize();
                window.location.href = "${rootPath}/fund/fund-log-excel-viewdate.htm?" + searchParam;
            });

            $("#btn-excel-information").click(function () {
                if ($("input[name='informationId']").val() == 0) {
                    myDialog(1, "请选择展览");
                    return;
                }
                var sdate = $("#startDate").val();
                var edate = $("#endDate").val();
                if (sdate == '' || edate == '') {
                    myDialog(1, "请筛选日期");
                    return;
                }
                if (GetNumberOfDays(sdate, edate) > 31) {
                    myDialog(1, "单次选择日期的最长跨度为31天");
                    return;
                }
                var searchParam = $("#thisForm").serialize();
                window.location.href = "${rootPath}/fund/fund-log-excel-information.htm?" + searchParam;
            });
        })

        function GetNumberOfDays(date1, date2) {//获得天数
            //date1：开始日期，date2结束日期
            var a1 = Date.parse(new Date(date1));
            var a2 = Date.parse(new Date(date2));
            var day = parseInt((a2 - a1) / (1000 * 60 * 60 * 24));//核心：时间戳相减，然后除以天数
            return day
        };

        function employ() {
            if ($("input[name='informationId']").val() == 0) {
                myDialog(1, "请选择展览");
                return;
            }
            var searchParam = $("#thisForm").serialize();
            window.location.href = "${rootPath}/fund/fund-log-employ-excel-daily.htm?" + searchParam;
        }

        $(function () {
            $(".value").click(function () {
                selectVal(this);
            });
        })




        let fullInfoList = [];

        function initInfoList() {
            <#if infoList??>
            fullInfoList = [
                <#list infoList as item>
                {
                    id: ${item.id},
                    name: "${item.name}",
                    enterpriseId: ${item.enterpriseId}
                }<#if item_has_next>,</#if>
                </#list>
            ];
            updateInfoSelect(fullInfoList);
            </#if>
        }

        function filterInfoList(selectedEnterpriseId) {
            let filteredList;
            if (selectedEnterpriseId === "0") {
                filteredList = fullInfoList;
            } else {
                filteredList = fullInfoList.filter(item =>
                    item.enterpriseId.toString() === selectedEnterpriseId
                );
            }

            // 如果筛选后列表为空,重置展览下拉框并添加提示选项
            if (!filteredList || filteredList.length === 0) {
                const infoSelect = document.getElementById('informationId');
                infoSelect.innerHTML = '<option value="0">该场馆暂无展览</option>';
                // 触发 change 事件
                const event = new Event('change');
                infoSelect.dispatchEvent(event);
            } else {
                updateInfoSelect(filteredList);
            }
        }

        function updateInfoSelect(infoList) {
            const infoSelect = document.getElementById('informationId');
            infoSelect.innerHTML = ''; // 清空现有选项

            if (infoList && infoList.length > 0) {
                // 添加所有选项
                infoList.forEach((item, index) => {
                    const option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = item.name;

                    // 如果有预选值，选中预选值；否则选中第一个选项
                    <#if informationId??>
                    if (item.id === ${informationId}) {
                        option.selected = true;
                    }
                    <#else>
                    if (index === 0) {
                        option.selected = true;
                    }
                    </#if>

                    infoSelect.appendChild(option);
                });
            }
        }



        function performance(){
            if($("input[name='informationId']").val() == 0) {
                myDialog(1,"请选择展览") ;
                return ;
            }
            var startDate = $("#startDate").val();
            var endDate = $("#endDate").val();
            var informationId = $("#informationId").val();
            var slaveEnterpriseId = $("#slaveEnterpriseId").val();
            window.location.href="${rootPath}/fund/fund-log-employ-excel-daily-all.htm?startDate="+startDate+"&endDate="+endDate+"&informationId="+informationId+"&slaveEnterpriseId="+slaveEnterpriseId;
        }

        function saleReport(){
            if($("input[name='informationId']").val() == 0) {
                myDialog(1,"请选择展览") ;
                return ;
            }
            var startDate = $("#startDate").val();
            var endDate = $("#endDate").val();
            var informationId = $("#informationId").val();
            var slaveEnterpriseId = $("#slaveEnterpriseId").val();
            window.location.href="${rootPath}/fund/export-business-report.htm?startDate="+startDate+"&endDate="+endDate+"&informationId="+informationId+"&slaveEnterpriseId="+slaveEnterpriseId;
        }

        document.addEventListener('DOMContentLoaded', function() {
            initInfoList();
            const slaveEnterpriseSelect = document.getElementById('slaveEnterpriseId');
            if (slaveEnterpriseSelect && slaveEnterpriseSelect.value !== "0") {
                filterInfoList(slaveEnterpriseSelect.value);
            }
        });
    </script>
</#macro>