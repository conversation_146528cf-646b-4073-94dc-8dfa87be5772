<#include "layout.ftl"/>
<#macro content>
<#include "/head.ftl" />
<div class="main">
	<!--弹出层-->
    <div id="mask"></div>
    <div class="maskCnt">
    	<div class="mk_box">
    		<a href="javascript:void(0)" class="mk_del"><i class="iconList"></i></a>
            <div class="mk_bx_cnt">
           </div>
        </div>
    </div>
	<!--#弹出层#-->
    <#include "left_menu.ftl" />
	<!--right-->
	<form action="${rootPath}/ticket/order/list.htm" method="post" id="thisForm">
	<div class="right">
		<div class="pisitionDiv">
        	<div class="pn_tle">门票订单</div>
        </div>
		
		<!--搜索-->
    	<div class="searchBox">
    	<div class="searchBg">
    		<span class="searchTle">搜索</span>
        	<table cellpadding="0" cellspacing="0" border="0" width="100%" class="search_tbl"  >
				<tr>
					<td><label class="inputTle">场<ins class="space25"></ins>馆：</label>
						<select class="select_300_33" name="enterpriseId" onChange="selectEnter(this.value)">
							<#list enterpriseList as item>
								<option value="${item.id}" <#if item.id==enterpriseId>selected</#if>>${item.name}</option>
							</#list>
						</select>
					</td>
					<td><label class="inputTle">展览列表：</label>
						<select class="select_300_33" name="informationId" id="informationId">
							<option value="0" <#if 0==informationId>selected</#if>>全部</option>
							<#list infoList as item>
								<option value="${item.id}" <#if item.id==informationId>selected</#if>>${item.name}</option>
							</#list>
						</select>
					</td>
					<td><label class="inputTle">门票类型：</label>
						<select class="selectBox" name="admissiontype" >
							<option value="-1" <#if -1==admissiontype>selected</#if>>全部</option>
							<option value="1"  <#if 1==admissiontype>selected</#if>>赠票</option>
							<option value="0"  <#if 0==admissiontype>selected</#if>>销售票</option>
						</select>
					</td>
				</tr>
         		<tr>
                 	<td><label class="inputTle">下单时间：</label>
                 		<input onFocus="WdatePicker({dateFmt:'yyyy-MM-dd'})" class="input_120_33 Wdate" id="buyDateStart" name="buyDateStart" value="<#if buyDateStart??>${buyDateStart}</#if>"/> -
                 		<input onFocus="WdatePicker({dateFmt:'yyyy-MM-dd'})" class="input_120_33 Wdate" id="buyDateEnd" name="buyDateEnd" value="<#if buyDateEnd??>${buyDateEnd}</#if>"/>
                   	</td>
					<td><label class="inputTle">付款方式：</label>
						<div class="selectBox">
							<input type="hidden" name="payWay" value="${payWay}">
							<a href="javascript:void(0)" class="value">
								<#if payWay == 1>微信</#if>
								<#if payWay == 2>支付宝</#if>
								<#if payWay == 3>现金</#if>
								<#if payWay == 4>余额</#if>
								<#if payWay == 5>银联</#if>
								<#if payWay == 6>银联刷卡</#if>
								<#if payWay == 8>携程</#if>
								<#if payWay == "">全部</#if>
								<i class="iconList"></i>
							</a>
							<ul>
								<li><a href="javascript:void(0)" data-value=''>全部</a></li>
								<li><a href="javascript:void(0)" data-value='1'>微信</a></li>
								<li><a href="javascript:void(0)" data-value='2'>支付宝</a></li>
								<li><a href="javascript:void(0)" data-value='3'>现金</a></li>
								<li><a href="javascript:void(0)" data-value='5'>银联</a></li>
								<li><a href="javascript:void(0)" data-value='6'>银联刷卡</a></li>
								<li><a href="javascript:void(0)" data-value='8'>携程</a></li>

							</ul>
						</div>
					</td>
					<td><label class="inputTle">购买途径：</label>
						<div class="selectBox">
							<input type="hidden" name="buyWay" value="${buyWay}">
							<a href="javascript:void(0)" class="value">
								<#if buyWay==1>网络订票</#if>
								<#if buyWay==2>人工购票</#if>
								<#if buyWay==3>自助售票机</#if>
								<#if buyWay==4>APP</#if>
								<#if buyWay==5>携程</#if>
								<#if buyWay=="">全部</#if>
								<i class="iconList"></i>
							</a>
							<ul>
								<li><a href="javascript:void(0)" data-value=''>全部</a></li>
								<li><a href="javascript:void(0)" data-value='1'>网络订票</a></li>
								<li><a href="javascript:void(0)" data-value='2'>人工购票</a></li>
								<li><a href="javascript:void(0)" data-value='3'>自助售票机</a></li>
								<li><a href="javascript:void(0)" data-value='4'>APP</a></li>
								<li><a href="javascript:void(0)" data-value='5'>携程</a></li>
							</ul>
						</div>
					</td>
            	</tr>
     			<tr>
					<td ><label class="inputTle">订单编号：</label><input  class="input_300_33" name="orderSerial" value="${orderSerial}" /></td>
					<td ><label class="inputTle">证件号码：</label><input  class="input_240_33" name="identification" value="${identification}" placeholder="支持输入证件号码、手机号、姓名"/></td>
     				<td ><label class="inputTle">核销码值：</label><input class="input_300_33" name="verificationCode" value="${verificationCode}" /></td>
     			</tr>
             	<tr>
                             <td  ><label class="inputTle">状<ins class="space25"></ins>态：</label>
                             	<div class="selectBox">
	                           		<input type="hidden" name="orderStatus" value="${orderStatus}">
	                            	<a href="javascript:void(0)" class="value">
	                            		<#if orderStatus=="-1">全部</#if>
	                            		<#if orderStatus==1>未付款</#if>
	                            		<#if orderStatus==2>已付款</#if>
	                            		<#if orderStatus==3>交易关闭</#if>
	                            		<#if orderStatus==4>已取票</#if>
	                            		<#if orderStatus==5>付款未取票</#if>
	                            		<#if orderStatus==6>未取完订单</#if>
	                            		<i class="iconList"></i>
	                            	</a>
		                            <ul>
		                            	<li><a href="javascript:void(0)" data-value='-1'>全部</a></li>
		                                <li><a href="javascript:void(0)" data-value='1'>未付款</a></li>
		                                <li><a href="javascript:void(0)" data-value='2'>已付款</a></li>
		                                <li><a href="javascript:void(0)" data-value='3'>交易关闭</a></li>
		                                <li><a href="javascript:void(0)" data-value='4'>已取票</a></li>
		                                <li><a href="javascript:void(0)" data-value='5'>付款未取票</a></li>
		                                <li><a href="javascript:void(0)" data-value='6'>未取完订单</a></li>
		                            </ul>
		                        </div>
                            </td>
                    
                    <td><label class="inputTle">门票时间：</label>
                 		<input onFocus="WdatePicker({dateFmt:'yyyy-MM-dd'})" class="input_120_33 Wdate" id="viewDateStart" name="viewDateStart" value="${viewDateStart}"/> - 
                 		<input onFocus="WdatePicker({dateFmt:'yyyy-MM-dd'})" class="input_120_33 Wdate" id="viewDateEnd" name="viewDateEnd" value="${viewDateEnd}"/>
                   	</td>
                    <td  ><input type="submit" class="btnBg" value="搜索" /></td>
					<td><input type="button" class="btnBg" value="导出" id="btn-excel" /></td>
              	</tr>
          	</table>
        </div>
        </div>
        <!--#搜索#-->
        
        
        <!--list-->
        <table cellpadding="0" cellspacing="0" border="0" width="100%" class="listTbl" >
        	<tr>
				<th width="5%"></th>
            	<th >订单编号</th><th class="th-left" width="25%">展览名称</th><th >下单时间</th><th >门票时间</th>
				<th >付款金额</th><th >门票数量</th>
            	<th >付款方式</th><th>交易状态</th><th >操作</th>
            </tr>
            <#list pageBean.result as item>
	            <tr>
					<td align="center"><input type="checkbox" name="cklist" value="${item.id}"/></td>
	                <td align="center">${item.orderSerial}</td>
	                <td >${item.informationName}</td>	                 
	                <td align="center">${item.buyTime?string('yyyy-MM-dd')}</td>
					<td align="center">${item.viewDate?string('yyyy-MM-dd')}</td>
					<td align="center" >&yen;${item.payPrice?string('0.00')}</td>
	                <td align="center" >${item.quantity}</td>
	                <td align="center">
						<#if item.payWay == 1>微信</#if>
	                	<#if item.payWay == 2>支付宝</#if>
	                	<#if item.payWay == 3>现金</#if>
	                	<#if item.payWay==4>余额</#if>
	                    <#if item.payWay==5>银联</#if>
	                    <#if item.payWay==6>银联刷卡</#if>
	                    <#if item.payWay==7>体验劵</#if>
						<#if item.payWay==8>携程</#if>
					</td>
	                <td align="center" style="<#if item.status == 1>color:orange</#if><#if item.status == 2>
	                		<#if (item.isTakeTicket==1 || item.isEVerification == 1 ) && item.quantity!=(item.takeTicketQuantity + item.eVerificationQuantity)>color:#ec0000
							<#elseif  (item.isTakeTicket==1 || item.isEVerification == 1 ) && (item.quantity==(item.takeTicketQuantity + item.eVerificationQuantity)) >
								color:green
							<#else>	                		
	                		color:#0693c1 </#if></#if><#if item.status == 3></#if>">
						<#if item.status == 1>未付款</#if>
	                	<#if item.status == 2>已付款
	                		<#if item.isEVerification == 1>(电子核销${item.eVerificationQuantity}/${item.quantity}张,取票${item.takeTicketQuantity}/${item.quantity}张<#if item.refundQuantity gt 0>,退票${item.refundQuantity}/${item.quantity}张</#if>)
	                		<#elseif item.isTakeTicket==1>(已取票${item.takeTicketQuantity}/${item.quantity}张<#if item.refundQuantity gt 0>,退票${item.refundQuantity}/${item.quantity}张</#if>)<#else></#if>
	                		<#if item.isTakeTicket==0 && item.isEVerification == 0 >(未取票<#if item.refundQuantity gt 0>,退票${item.refundQuantity}/${item.quantity}张</#if>)</#if>
	                	</#if>
	                	<#if item.status == 3>交易关闭</#if>
	                	<#if item.closeReson?? && item.closeReson != "">
	                	<br/>
	                	(${item.closeReson})
						</#if>
					</td>
	                <td align="center">
	                	<a href="${rootPath}/ticket/order/detail.htm?id=${item.id}" class="opera_icon_btn"><i class="smallIconList op_read_icon"></i>查看</a>
	                	<#if item.status == 2 && item.payWay != 7>&nbsp;&nbsp;&nbsp;&nbsp;<a href="javascript:void(0)" onclick="checkRefundPermission('${item.id}', '${item.ctripOrderId!""}')" class="opera_icon_btn"><i class="smallIconList op_money_return_icon"></i>退款</a></#if>
	                </td>
	            </tr>
            </#list>
        </table>
		<div>
			<a href="javascript:void(0)" class="operaBtn" onclick="checkAllList()">全选</a>&nbsp;&nbsp;&nbsp;
			<a href="javascript:void(0)" class="operaBtn" onclick="cancelCheck()">取消</a>&nbsp;&nbsp;&nbsp;
			<#--取消预约按钮隐藏-->
			<a href="javascript:void(0)" class="operaBtn" onclick="visitorsSignIn()">批量核销</a>&nbsp;&nbsp;&nbsp;
		</div>
        <div class="pageList ">
        	<input type="hidden" name="pageNum" id="pageId" />
           <@c.pagebar 'javascript:doPage({page})'/>
        </div>
    </div>
    </form>
    <!--#right#-->
</div>


</#macro>
<#macro script>
<script type="text/javascript">
$(function(){
	$(".value").click(function(){
		selectVal(this);
	});
	
	$("#btn-excel").click(function(){
		var sdate = $("#viewDateStart").val();
		var edate = $("#viewDateEnd").val();
		if(sdate == '' || edate == ''){
			myDialog(1,"请筛选门票时间") ;
			return ;
		}
		if(GetNumberOfDays(sdate,edate) > 31){
			myDialog(1,"单次选择日期的最长跨度为31天") ;
			return ;
		}
		
		var searchParam = $("#thisForm").serialize() ;
		window.location.href="${rootPath}/ticket/order/ticket-order-excel.htm?"+searchParam ;
	}) ;
})


function visitorsSignIn(){
	var ids = "" ;
	if($("input:checkbox[name='cklist']:checked").length==0)  {
		myDialog(1,"请先选择一条记录");
		return ;
	}
	$.each($("input:checkbox[name='cklist']:checked") , function() {
		var id = $(this).val() ;
		ids+=id+"," ;
	})
	myDialog(2,"确认是否一键核销?",function(){
		$.ajax({
			dataType:'json',
			type:"post" ,
			data:"ids="+ids ,
			url:"${rootPath}/ticket/order/ticketSignIn.htm",
			success:function(retData){
				if(retData.success) {
					$("#thisForm").submit();
				}else {
					myDialog(1,retData.errorMessage) ;
				}
			}
		})
	})
}
function checkAllList() {
	$("[name = 'cklist']:checkbox").each(function () {
		if(!$(this).is(':disabled')){
			$(this).prop('checked',true);
			$(this).attr("checked","checked") ;
		}
	})
}
function cancelCheck() {
	$("[name = 'cklist']:checkbox").removeAttr("checked") ;
}




function selectEnter(id){
	var html = "<option value=\"0\" selected=\"selected\">请选择</option>";
	$("#informationId").empty();
	$("#informationId").append(html);
	var data={};
	data.enterpriseId = id;
	data.type=1;
	$.ajax({
		type:"post",
		dataType:"text",
		data:data,
		url : "${rootPath}/ticket/information/selectByEnterpriseId.htm",
		success:function(retData) {
			retData = eval("(" + retData + ")");
			if(retData.success) {
				var jsonList = retData.list;
				if (jsonList.length > 0) {
					for (var i = 0; i < jsonList.length; i++) {
						var e = jsonList[i];
						$("#informationId").append("<option value=" + e.id + ">" + e.name + "</option>");
					}
				}
			}
		}
	})
}



function GetNumberOfDays(date1,date2){//获得天数
    //date1：开始日期，date2结束日期
    var a1 = Date.parse(new Date(date1));
    var a2 = Date.parse(new Date(date2));
    var day = parseInt((a2-a1)/ (1000 * 60 * 60 * 24));//核心：时间戳相减，然后除以天数
    return day
};

//翻页 
function doPage(page){
	$("#pageId").val(page);
	$("#thisForm").submit();
}

function checkRefundPermission(id, ctripOrderId) {
    // 检查是否为携程订单
    if (ctripOrderId && ctripOrderId.trim() !== "") {
        myDialog(1, "携程订单不允许退款");
        return;
    }
    // 如果不是携程订单，则允许退款
    refundPopup(id);
}

function refundPopup(id) {
    var data = {};
    data.orderId = id;
    $.ajax({
        dataType:'json',
        type:"post",
        url:"${rootPath}/ticket/order/ajax-ticket-refund.htm",
        data:data,
        success:function(redata) {
            if(redata.success){
				var list = redata.result;
				var html = '<form  method="post" enctype="multipart/form-data" >'
                        + ' <div>'
                        + ' <li  align="center">门票信息</li>'
                        + ' </div>'
                        + '<table cellpadding="0" cellspacing="0" border="0" width="100%"  >'
                        + ' <tr>'
                        + ' <th  width="30"><input type="checkbox" onclick="checkAll()" id="ckAll"/></th><th width="100" align="left">姓名</th><th width="80" align="left">状态</th><th width="87" align="left">票价</th><th width="90" align="left">名称</th><th width="80" align="left">核销码尾号</th>'
                        + ' </tr>'
                        + '</table>'
                        + ' <div id="refund_tbl">'
                        + '<table cellpadding="0" cellspacing="0" border="0" width="100%"  class="return_tbl"   >';
				if(list.length > 0){
                    for(var i=0;i<list.length;i++) {
                        html = html + '<tr><th  width="30"><input style="width: 25px;" type="checkbox" name="ck" onclick="checkPrice()" value="'+list[i].id+'" data-price="'+list[i].ticketPrice+'" data-refundPrice="'+list[i].refundPrice+'"/></th>' +
								 '</td><td width="100" align="left">'+list[i].name+'</td><td width="80" align="left">可退款</td><td width="80" align="left"><span>'+list[i].refundPrice+'</span><div class="max_refund">最高'+list[i].ticketPrice+'</div><i class="iconList edit_price"></i></td><td width="80" align="left">'+list[i].ticketName+'</td><td width="80" align="center" >'+list[i].verificationCode.slice(-4)+'</td></tr>';
                    }
				}
                html = html + '</table></div>'
                        + '<table cellpadding="0" cellspacing="0" border="0" width="100%"  >'
                        + '<tr><td colspan="4" align="left">退款金额:<span id="refundPrice">0</span></td></tr>'
                        + '<tr><td colspan="4" align="center">'
                        + ' <input type="button" class="btnBg btnBg200" value="确定"  onclick="refund('+id+')"/>&nbsp;&nbsp;'
                        + ' <input type="button" class="btnBg btnBg200" value="取消"  onclick="cancel()"/></td></tr>'
                        + '</table>'
                        + '</form>';
                myDialog(3,html);
                $(".mk_ask_box").removeClass("mk_ask_box");
                
                
                $(".edit_price").click(function(){
                	if($("input[name='editPrice']").length > 0){
                   	 myDialog(1,"请先保存退款金额");
                        return ;
                    }
                	var _price=$(this).parent().find("span").html();
                	$(this).parent().find("i").hide();
                	$(this).parent().find("div").show();
                	$(this).parent().find("span").html('<input name="editPrice" class="digits" value='+_price+' /><a href="javascript:void(0)" class="save_price_btn">保存</a>');
                	
                	$(".save_price_btn").click(function(){
                		var _update_price=$(this).parent().find("input").val();
                		if(isNaN(_update_price)){
                			myDialog(2,'请输入正确的金额');
                			return;
                		}
                		var max_price = $(this).parent().parent().parent().find("input:checkbox[name='ck']").attr("data-price");
                		if(max_price > 0 && _update_price <= 0){
                			myDialog(2,'退款金额需大于0');
                			return;
                		}
                		if(parseFloat(_update_price) > max_price){
                			return;
                		}
                		$(this).parent().parent().find("i").show()
                		$(this).parent().parent().find("div").hide();
                		$(this).parent().parent().parent().find("input:checkbox[name='ck']").attr("data-refundprice",_update_price);
                		$(this).parent().parent().find("span").html(_update_price);
                		checkPrice();
                	})
                	
                });
            }else{
				myDialog(1,data.errorMessage);
			}
        }
    });
}
function checkPrice(){
    var text = 0;
    if($("input:checkbox[name='ck']:checked").length > 0)  {
        var total = 0;
        $.each($("input:checkbox[name='ck']:checked") , function() {
            total = (parseFloat(total) + parseFloat($(this).attr("data-refundPrice"))).toFixed(2) ;
        });
        text = total;
    }
    $("#refundPrice").text(text);
}
function checkAll(){
    var text = 0;
    if ($("#ckAll").attr("checked") == "checked"){
        $("#ckAll").prop('checked',false);
        $("#ckAll").removeAttr("checked") ;
        $("[name = 'ck']:checkbox").each(function () {
            $(this).prop('checked',false);
            $(this).removeAttr("checked") ;
        })
    } else {
        $("#ckAll").prop('checked',true);
        $("#ckAll").attr("checked","checked") ;
        $("[name = 'ck']:checkbox").each(function () {
            $(this).prop('checked',true);
            $(this).attr("checked","checked") ;
        });
        if($("input:checkbox[name='ck']:checked").length > 0)  {
            var total = 0;
            $.each($("input:checkbox[name='ck']:checked") , function() {
                total = (parseFloat(total) + parseFloat($(this).attr("data-refundPrice"))).toFixed(2) ;
            });
            text = total;
        }
    }
    $("#refundPrice").text(text);
}
function cancel(){
    $(".mk_del").click();
}
var x;
function refund(id){
    var ids = "" ;
    var relationList = [];
    if($("input:checkbox[name='ck']:checked").length==0)  {
        myDialog(1,"请至少选择一条记录");
        return ;
    }
    if($("input[name='editPrice']").length > 0){
    	 myDialog(1,"请先保存退款金额");
         return ;
    }
    
    $.each($("input:checkbox[name='ck']:checked") , function() {
        var id = $(this).val() ;
        ids+=id+"," ;
        var relation = {};
    	relation.id = $(this).val() ;
    	relation.refundPrice = parseFloat($(this).attr("data-refundPrice")).toFixed(2);
    	relationList.push(relation);
    })
    ids = ids.substr(0,ids.length-1)  ;
    var price = parseFloat($("#refundPrice").text()).toFixed(2);
	var vdata = {};
	vdata.id = id;
	$.ajax({
		dataType:'json',
		type:"post",
		url:"${rootPath}/ticket/order/ajax-order-verification.htm",
		data:vdata,
		success:function(redata) {
			if(redata.success){
				//没有已核销
				myDialog(2,"<p class='font_size16 lh35'><span class='font_normal font_size14'>退款金额：</span><span class='font_red'>"+price+"元</span><br />是否确定退款？</p>",function(){
					$("#askOkBtn").attr("disabled","true");
					var datas = {};
					datas.id = id;
                    datas.relationIdList = ids;
                    datas.payPrice = price;
                    datas.relationList = relationList;
					$.ajax({
						type:"post",
						contentType: "application/json", 
						data:JSON.stringify(datas),
						dataType:"json",
						url:"${rootPath}/ticket/order/ajax-order-refund.htm",
						success:function(data) {
							if(data.success){
								var timesRun = 0;
								x = setInterval(function(){
									timesRun += 1;
									$.ajax({
										dataType:'json',
										type:"post",
										data:datas,
										url:"${rootPath}/ticket/order/ticket_refund_is_success.htm",
										success:function(retData){
											if(retData.success) {
												myDialog(1,"退款成功");
												setTimeout(function(){
													window.location.reload();
												}, 800)
											}
										}
									});
									if(timesRun === 10){
										clearInterval(x);
										myDialog(1,"退款失败");
									}
								},1500) ;
							}else{
								myDialog(1,data.errorMessage);
							}
						}
					})
				});
			}else{
				//有已核销
				myDialog(2,"<p class='font_size16 lh35'><span class='font_normal font_size14'>退款金额：</span><span class='font_red'>"+price+"元</span><br />该订单<span class='font_red'>已检票</span>！是否确定退款？</p>",function(){
					$("#askOkBtn").attr("disabled","true");
					var datas = {};
					datas.id = id;
                    datas.relationIdList = ids;
                    datas.payPrice = price;
                    datas.relationList = relationList;
					$.ajax({
						type:"post",
						contentType: "application/json", 
						data:JSON.stringify(datas),
						dataType:"json",
						url:"${rootPath}/ticket/order/ajax-order-refund.htm",
						success:function(data) {
							if(data.success){
								var timesRun = 0;
								x = setInterval(function(){
									timesRun += 1;
									$.ajax({
										dataType:'json',
										type:"post",
										data:datas,
										url:"${rootPath}/ticket/order/ticket_refund_is_success.htm",
										success:function(retData){
											if(retData.success) {
												myDialog(1,"退款成功");
												setTimeout(function(){
													window.location.reload();
												}, 800)
											}
										}
									});
									if(timesRun === 10){
										clearInterval(x);
										myDialog(1,"退款失败");
									}
								},1500) ;
							}else{
								myDialog(1,data.errorMessage);
							}
						}
					})
				});
			}
		}
	});
}
</script>
</#macro>