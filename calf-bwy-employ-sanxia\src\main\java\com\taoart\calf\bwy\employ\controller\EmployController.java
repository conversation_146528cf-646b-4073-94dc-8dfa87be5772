package com.taoart.calf.bwy.employ.controller;

import cn.hutool.core.codec.Base64Decoder;
import cn.hutool.core.codec.Base64Encoder;
import com.alibaba.fastjson.JSONObject;
import com.taoart.calf.bwy.domain.TicketAuth;
import com.taoart.calf.bwy.domain.TicketEmploy;
import com.taoart.calf.bwy.domain.TicketRole;
import com.taoart.calf.bwy.domain.UserThirdBind;
import com.taoart.calf.bwy.employ.Constant;
import com.taoart.calf.bwy.employ.IgnoreAuth;
import com.taoart.calf.bwy.employ.SessionContextUtils;
import com.taoart.calf.bwy.service.*;
import com.taoart.calf.bwy.service.bwg.BwgProjectService;
import com.taoart.calf.bwy.service.bwg.BwgRoleProjectService;
import com.taoart.calf.sms.api.SmsServiceApi;
import com.taoart.calf.wypw.domain.*;
import com.taoart.common.bean.JsonResponse;
import com.taoart.common.bean.PageBean;
import com.taoart.common.redis.CalfRedis;
import com.taoart.common.utils.*;
import com.taoart.common.web.RequestThreadLocal;
import com.taoart.common.web.ResponseThreadLocal;
import com.taoart.domain.user.User;
import com.taoart.wypw.sale.api.*;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.math.BigDecimal;
import java.net.Inet4Address;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Stream;

/**
 *
 * @ClassName: TicketEmployController
 * @Description: TODO
 * <AUTHOR>
 * @Date 2017年05月12日 11:21:21
 * @Modify
 * @CopyRight 杭州淘艺数据技术有限公司
 */
@Controller
@RequestMapping("employ")
public class EmployController extends DefaultController {

	@Autowired
	private TicketEmployService ticketEmployService;
	@Autowired
	private TicketRoleService ticketRoleService;
	@Autowired
	private TicketAuthService ticketAuthService;
	@Autowired
	private BwgRoleProjectService bwgRoleProjectService;
	@Autowired
	private BwgProjectService bwgProjectService;
	@Autowired
	private TicketInformationSaleServiceApi ticketInformationService;
	@Autowired
	private TicketOrderSaleServiceApi ticketOrderService;
	@Autowired
	private EnterpriseSaleServiceApi ticketEnterpriseService;
	@Autowired
	private TicketFreeEmployRelationSaleServiceApi ticketFreeEmployRelationService;
	@Autowired
	private TicketSaleServiceApi ticketService;
	@Autowired
	private EmployOperLogService employOperLogService;
	@Autowired
	private SmsServiceApi smsServiceApi;
	@Autowired
	private EmployEnterpriseAuthService employEnterpriseAuthService;

	public static final String VALID_CODE = "valid_code";
	@Autowired
	private CalfRedis calfRedis;
	@Autowired
	private TicketTimeSaleServiceApi ticketTimeService;

	@Autowired
	private TicketUserService userService;
	@Autowired
	private UserThirdBindService userThirdBindService;

	@Value("${domain.upload.path:}")
	private String uploadPath;
	@Value("${domain.pic.path:}")
	private String picPath;

	private static final Log log = LogFactory.getLog(EmployController.class);

	@RequestMapping("error")
	@IgnoreAuth
	public ModelAndView error() {
		ModelAndView mav = new ModelAndView("admin/error");
		return mav;
	}

	@RequestMapping("login")
	@IgnoreAuth
	public ModelAndView login() {
		// System.out.println(RequestThreadLocal.get().getSession().getMaxInactiveInterval());
		ModelAndView mav = new ModelAndView("admin/login_new");
		HttpSession session = RequestThreadLocal.get().getSession();
		session.removeAttribute(Constant.LOING_TICKET_SESSION_ID);
		Cookie cookie = new Cookie("sxsession", null);
		cookie.setMaxAge(0);
		cookie.setPath("/");
		ResponseThreadLocal.get().addCookie(cookie);
		return mav;
	}

	@RequestMapping("systemIndex")
	@IgnoreAuth(requireLogin = true)
	public ModelAndView systemIndex() {
		ModelAndView mav = new ModelAndView("admin/systemIndex");
		return mav;
	}

	@RequestMapping("index")
	@IgnoreAuth
	public ModelAndView index() {
		ModelAndView mav = new ModelAndView("admin/login_new");
		return mav;
	}

	@RequestMapping("doLogout")
	@IgnoreAuth
	public ModelAndView doLogout(HttpServletRequest request, HttpServletResponse response) throws UnknownHostException {
		ModelAndView mav = new ModelAndView("admin/login_new");
		// 日志记录
		TicketEmploy employee = (TicketEmploy) SessionContextUtils.getLoginObject();
		if (employee == null) {
			return new ModelAndView("forward:/employ/login");
		}
		employOperLogService.insertLog(employee.getEnterpriseId(),"1",employee.getId(),4,"用户:" + employee.getLoginName() + " 登出" + ",用户后台ip:" + IpAddressUtils.getRemoteIp(request));
		HttpSession session = RequestThreadLocal.get().getSession();
		session.removeAttribute(Constant.LOING_TICKET_SESSION_ID);
		Cookie cookie = new Cookie("sxsession", null);
		cookie.setMaxAge(0);
		cookie.setPath("/");
		ResponseThreadLocal.get().addCookie(cookie);

		calfRedis.delete("bwyLogin"+employee.getId());
		return mav;
	}

	private Boolean checkVerifyCode(String checkCode) {

		HttpSession session = RequestThreadLocal.get().getSession();
		String verifyCode = "";
		if (null != session.getAttribute(Constants.LEAGUE_VERIFYCODE)) {
			verifyCode = String.valueOf(session.getAttribute(Constants.LEAGUE_VERIFYCODE));
		}
		if (verifyCode.equals("")) {
			return false;
		}

		if (verifyCode.toLowerCase().equals(checkCode.toLowerCase())) {

			return true;
		} else {
			return false;
		}

	}

	/**
	 * 基于base64解密
	 * @param value
	 * @return
	 */
	public static String base64Decrypt(String value) {
		byte[] b = null;
		if( value != null){
			b = Base64.getDecoder().decode(value.getBytes(StandardCharsets.UTF_8));
			value = new String(b, StandardCharsets.UTF_8);
		}
		return value;
	}

	@RequestMapping("doLogin")
	@ResponseBody
	@IgnoreAuth
	public JsonResponse doLogin(String userName, String password, String checkCode,String mobileCode, HttpServletRequest request) {
		try {
			//前端传值的时候在头部加了一个随机数，需要先截取掉
			password=password.substring(1,password.length());
			// 原来不允许中文的
//			userName = new String(Base64.getDecoder().decode(userName));
//			password = new String(Base64.getDecoder().decode(password));
//			checkCode = new String(Base64.getDecoder().decode(checkCode));
//			mobileCode = new String(Base64.getDecoder().decode(mobileCode));
			userName = base64Decrypt(userName);
			password = base64Decrypt(password);
			checkCode = base64Decrypt(checkCode);
			mobileCode = base64Decrypt(mobileCode);
			// 先判断是否被锁定
			Object lockObject = calfRedis.getObject("employ-login-lock" + userName);
			if (lockObject != null) {
				return this.fail("失败次数过多，暂时锁定");
			}
			// 判断验证码正确性
			if (!checkVerifyCode(checkCode)) {
				// 验证码错误后 刷新 删除原来的验证码
				RequestThreadLocal.get().getSession().removeAttribute(Constants.LEAGUE_VERIFYCODE);
				return this.fail("验证码错误");
			}

			// 验证成功，删除验证码
			RequestThreadLocal.get().getSession().removeAttribute(Constants.LEAGUE_VERIFYCODE);

			password = MD5.encode(password);
			TicketEmploy employee = this.ticketEmployService.doLogin(userName, password);
			if (null == employee) {
				// 未锁定判断登录次数
				String key = "employ-login-count" + userName;
				Integer loginCount = (Integer) calfRedis.getObject(key);
				if (loginCount == null) {
					loginCount = 0;
				}
				if (loginCount > 3) {
					calfRedis.setObject("employ-login-lock" + userName, "1", 1800);// 锁定30分钟
				} else {
					loginCount++;
					calfRedis.setObject("employ-login-count" + userName, loginCount, 60);
				}
				return this.fail("用户密码错误");
			}
			if (employee.getStatus() == 0) {
				return this.fail("此账号已停用");
			}

			// 判断是否有进入系统的权限
			int roleId = employee.getRoleId();
			if (roleId != 0) {
//				BwgProject project = this.bwgProjectService.getByEnterpriseAndProjectCode(employee.getEnterpriseId(),
//						Constants.BWG_PROJECT_MANAGE);
//				if (null == project) {
//					return this.fail("没有配置该系统");
//				}
//				boolean flag = this.bwgRoleProjectService.existAuthByEnterpriseIdAndRoleIdAndProjectId(
//						employee.getEnterpriseId(), employee.getRoleId(), project.getId());
//				if (!flag) {
//					return this.fail("您无进入该系统的权限");
//				}
			}
			if(employee.getLoginName().indexOf("superadmin") > -1){//超级管理员
				if(StringUtils.isBlank(mobileCode)){ // 发送短信
					sendCodeNoTimes(employee.getMobile());
					JsonResponse json = new JsonResponse();
					json.setSuccess(false);
					json.setExports(1);
					json.setErrorMessage("短信已发送至"+employee.getMobile()+",请输入短信验证码");
					return json;
				}else{
					String sessionMsgCode = (String) RequestThreadLocal.get().getSession().getAttribute(VALID_CODE);
					if (null == sessionMsgCode) {
						return this.fail("短信验证码错误");
					}

					String[] arr = sessionMsgCode.split("#");
					String sessionMobile = arr[0];
					String code = arr[1];
					String codeDate = arr[2];

					// 注册手机号是否一致
					if (!sessionMobile.equals(employee.getMobile()) || !code.equals(mobileCode)) {
						return this.fail("短信验证码错误");
					}

					Date sendTime = DateUtil.strToDate(codeDate);
					long times = DateUtil.getDateDiff(sendTime, new Date());
					if ((times / 60) > 10) {
						RequestThreadLocal.get().getSession().removeAttribute(VALID_CODE);
						return this.fail("短信验证码已过期");
					}

					String ip = IpAddressUtils.getRemoteIp(request);
					if(!ip.equals("************")){
						return this.fail("IP限制登录");
					}
				}
			}


			Enterprise en = this.ticketEnterpriseService.selectById(employee.getEnterpriseId());
			employee.setPath(en.getPath());
			employee.setLogoPic(en.getLogoPic());
			employee.setEnterpriseName(en.getName());
			// 放入session
			SessionContextUtils.setLoginObject(employee);
			Cookie cookie = new Cookie("sxsession", Base64Encoder.encode(AesEncodeUtil.encrypt(employee.getId() + "")));
			cookie.setMaxAge(60 * 60 * 2);
			//cookie.setSecure(true);
			cookie.setPath("/");
			cookie.setDomain("localhost");
			//cookie.setHttpOnly(true);
			ResponseThreadLocal.get().addCookie(cookie);
			log.info("用户:" + employee.getName() + "于" + DateUtils.dateToStr(new Date(), "yyyy-MM-dd HH:mm:ss")
					+ "登入bwy后台系统" + ",用户后台ip:" + Inet4Address.getLocalHost().getHostAddress());
			// 日志记录
			employOperLogService.insertLog(employee.getEnterpriseId(),"1",employee.getId(),4,"用户:" + employee.getLoginName() + " 登出" + ",用户后台ip:" + IpAddressUtils.getRemoteIp(request));

			//录入缓存
			calfRedis.setObject("bwyLogin"+employee.getId(),"1",60 * 60 * 2);
			return this.success();
		} catch (Exception e) {
			log.error(e);
			return this.fail("系统异常");
		}
	}

	private JsonResponse sendCodeNoTimes(@RequestParam(defaultValue = "") String mobile) {

		if (StringUtils.isBlank(mobile)) {
			return this.fail("手机号不能为空");
		}
		if (!mobile.matches(Constants.MOBILE_REG)) {
			return this.fail("手机格式错误");
		}
		String code = SmsTool.getRandomNumber(6);
		try {
			Map<String, Object> map = new HashMap<String, Object>();
			String text = "【淘艺】您的验证码是：" + code + "。(10分钟内有效)";
			map.put("mobile", mobile);
			map.put("text", text);
			map.put("enterpriseId", 0);
			map.put("informationId", 0);
			map.put("src", "bwy");
			smsServiceApi.sendSms(map);
		} catch (Exception e) {
			log.error("短信发送错误"+mobile,e);
		}
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
		String date = format.format(new Date());
		code = mobile + "#" + code + "#" + date;
		RequestThreadLocal.get().getSession().setAttribute(VALID_CODE, code);
		return this.success();
	}

	/**
	 * 编辑用户
	 *
	 * @Title: editEmployee
	 * @Description: TODO
	 * @Param @return
	 * @Return ModelAndView
	 * @Throws
	 */
	@RequestMapping("edit-employee")
	public ModelAndView editEmployee(@RequestParam(defaultValue = "0") int id,
									 @RequestParam(defaultValue = "0") int roleId) {
		ModelAndView mav = new ModelAndView("admin/employee_edit");
		TicketEmploy loginEmployee = (TicketEmploy) SessionContextUtils.getLoginObject();
		int entepriseId = loginEmployee.getEnterpriseId();
		List<TicketRole> roleList = this.ticketRoleService.listByEntepriseId(entepriseId);
		mav.addObject("roleList", roleList);
		if (id > 0) {
			TicketEmploy employee = this.ticketEmployService.selectById(id);
//								|| employee.getEnterpriseId() != loginEmployee.getEnterpriseId()
			if (employee == null ) {
				return new ModelAndView("redirect:/auth/no-auth.htm");
			}

			mav.addObject("dto", employee);
		}
		if (roleId > 0) {
			TicketEmploy employee = new TicketEmploy();
			employee.setRoleId(roleId);
			mav.addObject("dto", employee);
		}
		List<Enterprise> enterpriseList =employEnterpriseAuthService.enterpriseListByEmployId(loginEmployee.getId());
		mav.addObject("enterpriseList", enterpriseList);
		return mav;
	}

	@RequestMapping("do-save-employee")
	@ResponseBody
	public JsonResponse doSaveEmployee(TicketEmploy employee) {
		JsonResponse json = this.success();
		if (employee.getRoleId() == 0) {
			return this.fail("请先分配员工的角色");
		}
		String photo = employee.getPhoto();
		if (!StringUtils.isBlank(photo)) {

//			try {
//
//				// 直接内外网统一操作
//				//图片质量检测，要求比较严格，暂时去掉，有需要再使用
//				String imageBase64 = ImageTools.imageToBase64ByOnline(photo);
//				boolean faceResult = FaceAI.isFaceEffective(imageBase64, FaceAI.BASE64);
//				boolean faceResult =true;
//				if (faceResult) {
//					URL photoUrl = new URL(photo);
//					employee.setPhoto(photoUrl.getPath());
//				} else {
//
//					json.setSuccess(false);
//					json.setErrorMessage("人脸照片不符合要求");
//					json.setMessage("人脸照片不符合要求");
//					return json;
//				}
//			} catch (MalformedURLException e) {
//				log.error(e);
//				json.setSuccess(false);
//				json.setErrorMessage("人脸系统异常");
//				json.setMessage("人脸系统异常");
//				return json;
//			}
			employee.setPhoto(photo);

		}
//		TicketEmploy loginEmployee = (TicketEmploy) SessionContextUtils.getLoginObject();

//		employee.setEnterpriseId(loginEmployee.getEnterpriseId());
		if(!StringUtils.isBlank(employee.getMobile())){
			if(employee.getMobile().length() > 11){
				return this.fail("手机号长度错误");
			}
			if(employee.getMobile().charAt(0) != '1'){
				this.fail("手机号异常");
			}
		}
		if (null != employee.getId() && 0 != employee.getId()) {
			TicketEmploy entity = this.ticketEmployService.selectById(employee.getId());
//			if (entity == null || entity.getEnterpriseId() != loginEmployee.getEnterpriseId()){
//				return this.fail("无权限！");
//			}
			employee.setStatus(entity.getStatus());
			if ((employee.getPhoto() != null && !(employee.getPhoto()).equals(entity.getPhoto()))
					|| (entity.getPhoto() != null && !(entity.getPhoto()).equals(employee.getPhoto()))) {
				// 修改photo
				employee.setPhotoUpdateTime(new Date());
			}
			if (StringUtils.isBlank(employee.getPassword())) {
				employee.setPassword(entity.getPassword());
			} else {
				employee.setPassword(MD5.encode(employee.getPassword()));
			}
			this.ticketEmployService.update(employee);
		} else {
			employee.setPassword(MD5.encode(employee.getPassword()));
			List<TicketEmploy> existList = this.ticketEmployService.listByLoginName(employee.getLoginName());
			if (CollectionUtils.isEmpty(existList)) {
				employee.setPhotoUpdateTime(new Date());
				this.ticketEmployService.insert(employee);
			} else {
				return this.fail("账号已存在");
			}
		}
		this.employEnterpriseAuthService.insertOrUpdate(employee.getId(),employee.getEnterpriseId());
		return json;
	}

	/**
	 * 编辑用户状态
	 *
	 * @Title: editEmployee
	 * @Description: TODO
	 * @Param @return
	 * @Return ModelAndView
	 * @Throws
	 */
	@RequestMapping("edit-employee-status")
	@ResponseBody
	public JsonResponse editEmployeestatus(@RequestParam(defaultValue = "0") int id) {
		JsonResponse json = this.success();
//		TicketEmploy loginEmployee = (TicketEmploy) SessionContextUtils.getLoginObject();
		TicketEmploy employee = this.ticketEmployService.selectById(id);
//		if (loginEmployee.getEnterpriseId() != employee.getEnterpriseId()) {
//			json.setSuccess(false);
//			json.setErrorMessage("信息不匹配");
//			return json;
//		}
		if (employee.getStatus() == 0) {
			employee.setStatus(1);
		} else if (employee.getStatus() == 1) {
			employee.setStatus(0);
		}
		employee.setPhotoUpdateTime(new Date());
		this.ticketEmployService.update(employee);
		return json;
	}

	/**
	 * 用户列表
	 *
	 * @Title: index
	 * @Description: TODO
	 * @Param @return
	 * @Return ModelAndView
	 * @Throws
	 */
	@RequestMapping("list-employee")
	public ModelAndView employeeList(@RequestParam(defaultValue = "1") int pageNum,
									 @RequestParam(defaultValue = "") String name,@RequestParam(defaultValue = "0") int enterpriseId) {
		ModelAndView mav = new ModelAndView("admin/employee_list");
		TicketEmploy employee = (TicketEmploy) SessionContextUtils.getLoginObject();
		if (!StringUtils.isBlank(name)) {
			name = name.trim();
		}
		List<Enterprise> enterpriseList = employEnterpriseAuthService.enterpriseListByEmployId(employee.getId());
		if(enterpriseId == 0){
			enterpriseId = enterpriseList.get(0).getId();
		}
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("enterpriseIds", enterpriseId);
		params.put("name", name);
		params.put("state",0);
		PageBean<TicketEmploy> list = this.ticketEmployService.listByPage(params, pageNum, Constant.PAGE_SIZE);
		mav.addObject("pageBean", list);
		mav.addObject("enterpriseList", enterpriseList);
		mav.addObject("enterpriseId", enterpriseId);
		mav.addObject("truename", name);
		return mav;
	}

	/**
	 * 员工赠票列表
	 *
	 * @Title: list-employee-free
	 * @Description: TODO
	 * @Param @return
	 * @Return ModelAndView
	 * @Throws
	 */
	@RequestMapping("list-employee-free")
	public ModelAndView employeeListFree(@RequestParam(defaultValue = "1") int pageNum,
										 @RequestParam(defaultValue = "0") int informationId, @RequestParam(defaultValue = "0") int roleId,
										 @RequestParam(defaultValue = "") String truename) {
		ModelAndView mav = new ModelAndView("admin/employee_list_free");
		TicketEmploy employee = (TicketEmploy) SessionContextUtils.getLoginObject();
		if (informationId > 0){
			TicketInformation information = ticketInformationService.selectById(informationId);
			/*|| information.getEnterpriseId() != employee.getEnterpriseId()*/
			if (information == null ){
				return new ModelAndView("redirect:/auth/no-auth.htm");
			}
		}
		if (roleId > 0){
			TicketRole role = ticketRoleService.selectById(roleId);
//			|| role.getEnterpriseId() != employee.getEnterpriseId()
			if (role == null ){
				return new ModelAndView("redirect:/auth/no-auth.htm");
			}
		}
		TicketEmploy params = new TicketEmploy();
		Map<String, Object> map = new HashMap<>();
		List<TicketInformation> infoList = this.ticketInformationService.listByEnterpriseId(employee.getEnterpriseId(),
				0);
		if (!StringUtils.isBlank(truename)) {
			truename = truename.trim();
		}
		if (informationId == 0 && infoList.size() > 0) {
			informationId = infoList.get(0).getId();
		}
		params.setEnterpriseId(employee.getEnterpriseId());
		map.put("enterpriseId", employee.getEnterpriseId());
		map.put("roleId", roleId);
		map.put("name", truename);
		map.put("status", 1);
		PageBean<TicketEmploy> pageBean = this.ticketEmployService.listByPage(map, pageNum, 20);
		if (!pageBean.getResult().isEmpty()) {
			Map<String, TicketFreeEmployRelation> relationMap = new HashMap<>();
			List<Integer> list = new ArrayList<>();
			for (TicketEmploy ticketEmploy : pageBean.getResult()) {
				list.add(ticketEmploy.getId());
			}
			List<TicketFreeEmployRelation> relationList = ticketFreeEmployRelationService.totalQuantity(list,
					informationId);
			for (TicketFreeEmployRelation ticketFreeEmployRelation : relationList) {
				relationMap.put(ticketFreeEmployRelation.getTicketEmployId() + "", ticketFreeEmployRelation);
			}
			mav.addObject("relationMap", relationMap);
		}
		mav.addObject("pageBean", pageBean);
		List<TicketRole> list = ticketRoleService.listByEntepriseId(employee.getEnterpriseId());
		mav.addObject("infoList", infoList);
		mav.addObject("roleList", list);
		mav.addObject("informationId", informationId);
		mav.addObject("roleId", roleId);
		mav.addObject("truename", truename);
		mav.addObject("pageNumber", pageNum);
		return mav;
	}

	/**
	 * 编辑用户赠票数量
	 *
	 * @Title: editEmployeeFree
	 * @Description: TODO
	 * @Param @return
	 * @Return ModelAndView
	 * @Throws
	 */
	@RequestMapping("edit-employee-free")
	@ResponseBody
	public JsonResponse editEmployeeFree(@RequestParam(defaultValue = "0") int freeEmployId,
										 @RequestParam(defaultValue = "0") int informationId) {
		JsonResponse jp = this.success();

//		TicketEmploy loginEmployee = (TicketEmploy) SessionContextUtils.getLoginObject();
		TicketFreeEmployRelation relation = ticketFreeEmployRelationService
				.selectByEmployIdAndInformation(freeEmployId, informationId);
		TicketInformation information = ticketInformationService.selectById(informationId);
//		if (loginEmployee.getEnterpriseId() != information.getEnterpriseId()) {
//			jp.setSuccess(false);
//			jp.setErrorMessage("信息不匹配");
//			return jp;
//		}
		jp.setResult(relation);
		jp.setExports(information);
		// mav.addObject("ticketEmployId",ticketEmployId);
		// mav.addObject("information",information);
		return jp;
	}

	/**
	 * 编辑用户赠票数量
	 *
	 * @Title: editEmployeeFree
	 * @Description: TODO
	 * @Param @return
	 * @Return ModelAndView
	 * @Throws
	 */
	@RequestMapping("save-employee-free")
	@ResponseBody
	public synchronized JsonResponse editEmployeeFree(TicketFreeEmployRelation relation) {
		JsonResponse jp = this.success();

		TicketEmploy loginEmployee = (TicketEmploy) SessionContextUtils.getLoginObject();
//		TicketEmploy freeEmployee = this.ticketEmployService.selectById(relation.getTicketEmployId());
//		if (loginEmployee.getEnterpriseId() != freeEmployee.getEnterpriseId()) {
//			jp.setSuccess(false);
//			jp.setErrorMessage("信息不匹配");
//			return jp;
//		}

		TicketFreeEmployRelation freeRelation = ticketFreeEmployRelationService
				.selectByEmployIdAndInformation(relation.getTicketEmployId(), relation.getInformationId());
		int ticketCount = 1;
		if (freeRelation == null) {
			ticketCount = relation.getCanTicketQuantity();
		} else {
			if (relation.getCanTicketQuantity() <= freeRelation.getCanTicketQuantity()) {
				jp.setSuccess(false);
				jp.setErrorMessage("赠票数量要大于原有数量");
				return jp;
			}
			ticketCount = relation.getCanTicketQuantity() - freeRelation.getCanTicketQuantity();
		}

		int enterpriserId = loginEmployee.getEnterpriseId();
		// 获取赠票id
		Ticket ticket = new Ticket();
		ticket.setTypeCode(3);
		ticket.setInformationId(relation.getInformationId());
		List<Ticket> ticketList = ticketService.listAll(ticket);
		if (CollectionUtils.isEmpty(ticketList)) {
			return this.fail("无门票信息，请先创建赠票门票");
		}
		int ticketId = ticketList.get(0).id;
		// 获取场次id,此处需要后期优化，多个场次时应该在前端选择
		TicketTime time = new TicketTime();
		time.setInformationId(relation.getInformationId());
		List<TicketTime> timeList = ticketTimeService.listAll(time);
		if (CollectionUtils.isEmpty(timeList)) {
			return this.fail("无场次信息，请先创建创次");
		}
		int timeId = timeList.get(0).getId();
		TicketOrder order = new TicketOrder();
		order.setInformationId(relation.getInformationId());
		order.setEnterpriseId(enterpriserId);
		order.setTimeId(timeId);
		order.setQuantity(ticketCount);
		order.setPayPrice(new BigDecimal(0));
		order.setStatus(1);
		order.setOperId(loginEmployee.getId());
		order.setBuyTime(new Date());
		order.setCreateTime(new Date());
		order.setUpdateTime(new Date());
		order.setIsFreeTicket(1);
		order.setIsTransfer(0);
		order.setBuyWay(2);
		Date date = new Date();
		date = DateUtils.addDay(date, 1);
		order.setViewDate(date);

		List<TicketOrderRelation> list = new ArrayList<>();
		TicketOrderRelation ticketOrderRelation = null;
		ticketOrderRelation = new TicketOrderRelation();
		ticketOrderRelation.setIdcard("");
		ticketOrderRelation.setName("观众");
		ticketOrderRelation.setTicketId(ticketId);
		ticketOrderRelation.setTimeId(timeId);
		ticketOrderRelation.setTicketEmployId(relation.getTicketEmployId());
		for (int i = 0; i < ticketCount; i++) {
			list.add(ticketOrderRelation);
		}
		order.setRelationList(list);
		if (freeRelation == null) {
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("relation", relation);
			map.put("order", order);
			jp = ticketFreeEmployRelationService.update(map);
		} else {
			freeRelation.setCanTicketQuantity(relation.getCanTicketQuantity());
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("relation", freeRelation);
			map.put("order", order);
			jp = ticketFreeEmployRelationService.update(map);
		}
		return jp;
	}

	@RequestMapping("do-delete-employee")
	@ResponseBody
	public JsonResponse deleteInformation(int id) {
		JsonResponse json = this.success();

		try {

			TicketEmploy employee = this.ticketEmployService.selectById(id);
//    if (loginEmployee.getEnterpriseId() != employee.getEnterpriseId()) {
//       json.setSuccess(false);
//       json.setErrorMessage("信息不匹配");
//       return json;
//    }
			employee.setState(1);
			employee.setPhotoUpdateTime(new Date());
			this.ticketEmployService.update(employee);


//			TicketEmploy employ = this.ticketEmployService.selectById(id);
//
//			this.ticketEmployService.delete(id);
//			// 删除
//			this.employEnterpriseAuthService.deleteById(id);
//			DFSFileUtils.delImgOfDfs(employ.getPic(), uploadPath);
		} catch (Exception e) {
			log.error(e.getMessage());
			return this.fail(e.getMessage());
		}
		return json;
	}

	@RequestMapping("welcome")
	@IgnoreAuth(requireLogin = true)
	public ModelAndView main(String systemType) {
		TicketEmploy employee = (TicketEmploy) SessionContextUtils.getLoginObject();
		if(employee == null){
			return new ModelAndView("admin/login");
		}
		// 只有标注的这3个账号才有智慧导览系统的权限
		if("5".equals(systemType)) {
			if (Stream.of(2,13, 15, 1000011,1000014).anyMatch(id -> id.equals(employee.getId()))) {
				return new ModelAndView("redirect:https://dh.3gmuseum.cn/admin/#/third-login");
			} else {
				return new ModelAndView("redirect:/auth/no-auth.htm");
			}
		}
		ModelAndView mav = new ModelAndView("admin/welcome");
		List<TicketAuth> authList = null;
		if (employee.getRoleId() != 0) {
			authList = this.ticketAuthService.listByEnterpriseIdAndRoleId(employee.getEnterpriseId(), employee.getRoleId(),systemType);
		} else {
			authList = this.ticketAuthService.listByEnterpriseId(employee.getEnterpriseId(),systemType);
		}
		employee.setSystemType(systemType);
		employee.setAuthList(authList);
		log.info("当前登录的用户systemType = " +systemType+ " , authList " + JSONObject.toJSONString(authList));
		SessionContextUtils.setLoginObject(employee);
		return mav;
	}

	@RequestMapping("/getEmploy")
	@IgnoreAuth
	@ResponseBody
	public String getEmploy(@RequestParam(value = "employ_key") String employ_key,
							@RequestParam("sign") String sign,
							@RequestParam("timestamp") Long timestamp){
		JSONObject resultJson = new JSONObject();
		try {
			resultJson.put("result", false);
			if (StringUtils.isBlank(employ_key) || StringUtils.isBlank(sign) || timestamp == null) {
				resultJson.put("message", "参数值错误");
				return resultJson.toJSONString();
			}
			String encode = MD5.encode(employ_key + "sxtys99!@" + timestamp);
			if (!encode.equals(sign)) {
				log.error(employ_key+" "+timestamp+" "+sign +" 正确值："+encode);
				resultJson.put("message", "签名验证失败，请重新生成签名");
				return resultJson.toJSONString();
			}
			String decrypt = AesEncodeUtil.decrypt(Base64Decoder.decodeStr(employ_key));
			//先查询缓存是否被冒用
			String isValue=(String) calfRedis.getObject("bwyLogin"+decrypt);
			if(isValue==null){
				resultJson.put("message", "请重新登录");
				return resultJson.toJSONString();
			}
			// 查询 信息
			TicketEmploy ticketEmploy = ticketEmployService.selectById(Integer.parseInt(decrypt));
			if(ticketEmploy == null){
				resultJson.put("message", "用户不存在!");
				return resultJson.toJSONString();
			}
			resultJson.put("result", true);
			resultJson.put("message", "查询成功");
			resultJson.put("id", ticketEmploy.getId());
			resultJson.put("loginName", ticketEmploy.getLoginName());
			resultJson.put("name", ticketEmploy.getName());
			return resultJson.toJSONString();
		}catch (Exception e){
			e.printStackTrace();
			resultJson.put("result", false);
			resultJson.put("message", "系统异常: "+e.getMessage());
			return resultJson.toJSONString();
		}
	}



	@RequestMapping("/getWxuser")
	@IgnoreAuth
	@ResponseBody
	public String getWxuser(@RequestParam(value = "union_id") String union_id,
							@RequestParam("sign") String sign,
							@RequestParam("timestamp") Long timestamp){
		JsonResponse json = new JsonResponse();
		JSONObject resultJson = new JSONObject();
		json.setSuccess(true);
		if(StringUtils.isBlank(union_id) || StringUtils.isBlank(sign) || timestamp == null){
			resultJson.put("message","参数值错误");
			resultJson.put("result",false);
			return resultJson.toJSONString();
		}
		String encode = MD5.encode(union_id + "sxwx1010" + timestamp);
		if(!encode.equals(sign) ){
			resultJson.put("message","签名验证失败");
			resultJson.put("result",false);
			return resultJson.toJSONString();
		}
		UserThirdBind params = new UserThirdBind();
		params.setOpenId(union_id);
		List<UserThirdBind> userList = this.userThirdBindService.listAll(params);
		User loginUser = null;
		if (!userList.isEmpty()) {
			if (userList.size() > 1) {
				resultJson.put("message","该union_id查询到多个用户");
				resultJson.put("result",false);
				return resultJson.toJSONString();
			}
			UserThirdBind thirdUser = userList.get(0);
			loginUser = userService.selectById(thirdUser.getUserId());
			resultJson.put("result",true);
			resultJson.put("message","查询成功");
			resultJson.put("mobile",loginUser.getMobile());
			resultJson.put("third_pic",loginUser.getUserAvatar());
			resultJson.put("nick_name",loginUser.getUserNick());
			resultJson.put("union_id",union_id);
		}
		return resultJson.toJSONString();
	}


	@RequestMapping("/saveWxuser")
	@IgnoreAuth
	@ResponseBody
	public String saveWxuser(@RequestParam(value = "union_id") String union_id,
							 @RequestParam(value = "mobile") String mobile,
							 @RequestParam(value = "nick_name") String nick_name,
							 @RequestParam(value = "third_pic") String third_pic,
							 @RequestParam("sign") String sign,
							 @RequestParam("timestamp") Long timestamp){
		JSONObject resultJson = new JSONObject();
		if(StringUtils.isBlank(union_id) || StringUtils.isBlank(sign) || timestamp == null){
			resultJson.put("message","参数值错误");
			resultJson.put("result",false);
			return resultJson.toJSONString();
		}
		String encode = MD5.encode(union_id + "sxwx1010" + timestamp);
		if(!encode.equals(sign) ){
			resultJson.put("message","签名验证失败");
			resultJson.put("result",false);
			return resultJson.toJSONString();
		}

		try {
			User loginUser = null;
			User user = new User();
			user.setCreateTime(new Date());
			user.setUpdateTime(new Date());
			user.setGrade(1); // 等级默认1
			user.setStatus(1);// 状态默认激活1 user.setEmailStatus(0); // 邮箱
			user.setUseType(1);// 用户类型默认1普通用户
			user.setPassword(MD5.encode("123456"));
			//user.setLoginIp(IpAddressUtils.getRemoteIp(request));
			user.setMobile(mobile);
			user.setRegisterSrc("XCX");
			user.setLoginTime(new Date());
			user.setUserNick(nick_name);
			if (StringUtils.isBlank(third_pic) || !third_pic.startsWith("http")) {
				user.setUserAvatar(
						//设置默认头像
						"[{\"pic_type\":\"5\",\"pic_path\":\"https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132\"}]");
			} else {
				user.setUserAvatar("[{\"pic_type\":\"5\",\"pic_path\":\"" + third_pic + "\"}]");
			}
			// 判断手机号是否已经注册
			User olduser = this.userService.selectByMobileNoPassword(mobile);
			if (olduser != null) {
				loginUser = olduser;
			} else {
				this.userService.insert(user);
				loginUser = user;
			}

			// 保存第三方登录记录表
			UserThirdBind userThirdBind = new UserThirdBind();
			userThirdBind.setUserId(loginUser.getId());
			userThirdBind.setType("XCX");
			userThirdBind.setThirdNickName(nick_name);
			userThirdBind.setThirdPic(third_pic);
			userThirdBind.setOpenId(union_id);
			//userThirdBind.setWxOpenId(openId);
			userThirdBind.setCreateTime(new Date());
			userThirdBind.setUpdateTime(new Date());
			userThirdBindService.insert(userThirdBind);
		}catch (Exception e) {
			log.error(e, e);
			resultJson.put("message","保存用户信息出现异常");
			resultJson.put("result",false);
			return resultJson.toJSONString();
		}
		resultJson.put("result",true);
		resultJson.put("message","查询成功");
		return resultJson.toJSONString();
	}

	@RequestMapping("/dfsGetAuth")
	@IgnoreAuth
	@ResponseBody
	public String dfsGetAuth( String userkey, String src) {
		JSONObject resultJson = new JSONObject();

		//
		try{

			if(!StringUtils.isBlank(userkey) ){
				Object isValue=null;

				if("bwy".equals(src)){//来自bwy
					String key = AesEncodeUtil.decrypt(Base64Decoder.decodeStr(userkey));
					//先查询缓存是否被冒用
					isValue=(String) calfRedis.getObject("bwyLogin"+key);
				}else{//来自小程序

					String enc = AesEncodeUtil.decrypt(Base64Decoder.decodeStr(userkey));
					String[] encs = enc.split("-");
					Integer userID=Integer.parseInt(encs[0]);
					isValue =  calfRedis.getObject("xcx-login-id" + userID);
				}
				if(isValue==null){
					resultJson.put("result",false);
					return resultJson.toJSONString();
				}else{
					resultJson.put("result",true);
					return resultJson.toJSONString();
				}

			}else{
				resultJson.put("result",false);
				return resultJson.toJSONString();
			}
		}catch(Exception e){
			log.error(e);
			resultJson.put("result",false);
			return resultJson.toJSONString();
		}


	}
}