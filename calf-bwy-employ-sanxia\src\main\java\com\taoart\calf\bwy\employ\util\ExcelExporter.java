package com.taoart.calf.bwy.employ.util;

import com.taoart.calf.bwy.domain.TicketExcel;
import com.taoart.calf.bwy.domain.TicketOrderExcel;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/30 15:41
 * @Description: TODO
 * @Version 1.0
 */
public class ExcelExporter {
    public static void exportSalesmen(List<TicketOrderExcel> salesmen,String startDate,String endDate, String filePath, HttpServletResponse response) throws Exception {

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        String fileName = URLEncoder.encode("人工售票销售报表.xlsx", "UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

        // 1. 创建工作簿和工作表
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("售票员数据");

            CellStyle mainHeaderStyle = createMainHeaderStyle(workbook);

            // 创建主标题行（合并单元格）
            Row mainHeaderRow = sheet.createRow(0);
            mainHeaderRow.setHeightInPoints(20); // 设置行高
            Cell mainHeaderCell = mainHeaderRow.createCell(0);
            mainHeaderCell.setCellValue("售票员票务数据"+startDate+"-"+endDate);
            mainHeaderCell.setCellStyle(mainHeaderStyle);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 10));
            // ====== 创建字体样式 ======
            // 标题字体
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setFontHeightInPoints((short) 12);
            headerFont.setColor(IndexedColors.WHITE.getIndex());
            headerFont.setFontName("Arial");

            // 数据字体
            Font dataFont = workbook.createFont();
            dataFont.setFontHeightInPoints((short) 11);
            dataFont.setColor(IndexedColors.BLACK.getIndex());
            dataFont.setFontName("微软雅黑");

            // ====== 创建单元格样式 ======
            // 标题样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.ROYAL_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            headerStyle.setWrapText(true);

            // 数据样式1 (浅色背景)
            CellStyle dataStyle1 = workbook.createCellStyle();
            dataStyle1.setFont(dataFont);
            dataStyle1.setFillForegroundColor(IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex());
            dataStyle1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            dataStyle1.setBorderTop(BorderStyle.THIN);
            dataStyle1.setBorderRight(BorderStyle.THIN);
            dataStyle1.setBorderBottom(BorderStyle.THIN);
            dataStyle1.setBorderLeft(BorderStyle.THIN);
            dataStyle1.setAlignment(HorizontalAlignment.CENTER);
            dataStyle1.setVerticalAlignment(VerticalAlignment.CENTER);
            dataStyle1.setWrapText(true);

            // 数据样式2 (白色背景交替)
            CellStyle dataStyle2 = workbook.createCellStyle();
            dataStyle2.setFont(dataFont);
            dataStyle2.setFillForegroundColor(IndexedColors.WHITE.getIndex());
            dataStyle2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            dataStyle2.setBorderTop(BorderStyle.THIN);
            dataStyle2.setBorderRight(BorderStyle.THIN);
            dataStyle2.setBorderBottom(BorderStyle.THIN);
            dataStyle2.setBorderLeft(BorderStyle.THIN);
            dataStyle2.setAlignment(HorizontalAlignment.CENTER);
            dataStyle2.setVerticalAlignment(VerticalAlignment.CENTER);
            dataStyle2.setWrapText(true);

            // 数字样式
            CellStyle numberStyle = workbook.createCellStyle();
            numberStyle.cloneStyleFrom(dataStyle2);  // 继承基础样式


            // 3. 创建标题行
            Row headerRow = sheet.createRow(1);
            headerRow.setHeightInPoints(12 * 2.0f);
            String[] headers = {"渠道", "票名称","单价", "门票总数","退票数量","订单金额","退票金额","人数","现金","银联","总收入"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // 4. 填充数据（嵌套循环）
            int rowNum = 2;

            Double priceAll =0.0;
            int orderCountAll=0;
            int refundQuantityAll=0;
            Double totalPriceAll=0.0;
            Double refundPirceAll=0.0;
            int peopleNumAll=0;
            Double cachStrAll=0.0;
            Double yinlianStrAll=0.0;
            Double cachandyinlianAll=0.0;

            for (int i=0;i< salesmen.size();i++) {
                TicketOrderExcel salesman=salesmen.get(i);
                List<TicketExcel> tickets = salesman.getTicketList();

                // 处理没有票的情况
                if (tickets == null || tickets.isEmpty()) {
                    Row row = sheet.createRow(rowNum++);
                    row.createCell(0).setCellValue(salesman.getOperName());
                    // 其他单元格留空
                    continue;
                }

                Double price =0.0;
                int orderCount=0;
                int refundQuantity=0;
                Double totalPrice=0.0;
                Double refundPirce=0.0;
                int peopleNum=0;
                Double cachStr=0.0;
                Double yinlianStr=0.0;
                Double cachandyinlian=0.0;

                // 遍历票列表
                for (TicketExcel ticket : tickets) {
                    Row row = sheet.createRow(rowNum++);
                    // 售票员信息（每张票都重复）
                    row.createCell(0).setCellValue(salesman.getOperName());
                    //合计信息
                    if(ticket.getPrice()!=null&&!ticket.getPrice().isEmpty()){
                        double numPrice = Double.parseDouble(ticket.getPrice());
                        price+=numPrice;
                    }

                    orderCount+=Integer.parseInt(ticket.getOrderCount());



                    refundQuantity+=ticket.getRefundQuantity();

                    if(ticket.getTotalPrice()!=null&&!ticket.getTotalPrice().isEmpty()){
                        double numtotalPrice = Double.parseDouble(ticket.getTotalPrice());
                        totalPrice+=numtotalPrice;
                    }

                    if(ticket.getRefundPirce()!=null&&!ticket.getRefundPirce().isEmpty()){
                        double numRefundPirce=Double.parseDouble(ticket.getRefundPirce());
                        refundPirce+=numRefundPirce;
                    }

                    peopleNum+=ticket.getPeopleNum();
                    double numCachStr=0.0;
                    if(ticket.getCachStr()!=null&&!ticket.getCachStr().isEmpty()){
                         numCachStr=Double.parseDouble(ticket.getCachStr());
                        cachStr+=numCachStr;
                    }
                    double numYinlianStr=0.0;
                    if(ticket.getYinlianStr()!=null&&!ticket.getYinlianStr().isEmpty()){
                        numYinlianStr=Double.parseDouble(ticket.getYinlianStr());
                        yinlianStr+=numYinlianStr;
                    }

                    cachandyinlian=cachStr+yinlianStr;
                    // 票信息
                    row.createCell(1).setCellValue(ticket.getName());
                    row.createCell(2).setCellValue(ticket.getPrice());
                    row.createCell(3).setCellValue(ticket.getOrderCount());
//                    row.createCell(4).setCellValue(ticket.getVerificationQuantity());
//                    row.createCell(5).setCellValue(ticket.getNoverificationQuantity());
                    row.createCell(4).setCellValue(ticket.getRefundQuantity());
                    row.createCell(5).setCellValue(ticket.getTotalPrice());
//                    row.createCell(6).setCellValue(ticket.getVerificationPrice());
//                    row.createCell(7).setCellValue(ticket.getNoverificationPrice());
                    row.createCell(6).setCellValue(ticket.getRefundPirce());
                    row.createCell(7).setCellValue(ticket.getPeopleNum());
                    row.createCell(8).setCellValue(ticket.getCachStr());
                    row.createCell(9).setCellValue(ticket.getYinlianStr());
                    row.createCell(10).setCellValue(numCachStr+numYinlianStr);
                }
                int rows=rowNum++;
                Row row = sheet.createRow(rows);
                row.createCell(0).setCellValue("小计");
                row.createCell(1).setCellValue("");
                row.createCell(2).setCellValue("");
                row.createCell(3).setCellValue(orderCount);
//                row.createCell(4).setCellValue(verificationQuantity);
//                row.createCell(5).setCellValue(noverificationQuantity);
                row.createCell(4).setCellValue(refundQuantity);
                row.createCell(5).setCellValue(totalPrice);
//                row.createCell(6).setCellValue(verificationPrice);
//                row.createCell(7).setCellValue(noverificationPrice);
                row.createCell(6).setCellValue(refundPirce);
                row.createCell(7).setCellValue(peopleNum);
                row.createCell(8).setCellValue(cachStr);
                row.createCell(9).setCellValue(yinlianStr);
                row.createCell(10).setCellValue(cachandyinlian);
                mergeCells(sheet,rows,0,rows,1);



                 priceAll +=price;
                 orderCountAll+=orderCount;
                 refundQuantityAll+=refundQuantity;
                 totalPriceAll+=totalPrice;
                 refundPirceAll+=refundPirce;
                 peopleNumAll+=peopleNum;
                 cachStrAll+=cachStr;
                 yinlianStrAll+=yinlianStr;
                 cachandyinlianAll+=cachandyinlian;
                 if((salesmen.size()-1)==i){
                     Row rowLast = sheet.createRow(rows+1);
                     System.out.println(sheet.getLastRowNum()+1+"getLastRowNum");
                     rowLast.createCell(0).setCellValue("总计");
                     rowLast.createCell(1).setCellValue("");
                     rowLast.createCell(2).setCellValue("");
                     rowLast.createCell(3).setCellValue(orderCountAll);
                     rowLast.createCell(4).setCellValue(refundQuantityAll);
                     rowLast.createCell(5).setCellValue(totalPriceAll);
                     rowLast.createCell(6).setCellValue(refundPirceAll);
                     rowLast.createCell(7).setCellValue(peopleNumAll);
                     rowLast.createCell(8).setCellValue(cachStrAll);
                     rowLast.createCell(9).setCellValue(yinlianStrAll);
                     rowLast.createCell(10).setCellValue(cachandyinlianAll);
                     mergeCells(sheet,rows+1,0,rows+1,1);
                 }

            }

//            // 5. 自动调整列宽
//            for (int i = 0; i < headers.length; i++) {
//                sheet.autoSizeColumn(i);
//                sheet.setColumnWidth(i,256 * 15);
//            }
            int rowNums = 0; // 从第一行开始，假设第一行是标题行或不需要设置样式的行
            for (Row row : sheet) {
                row.setHeightInPoints(20);
                if (rowNums++ > 1) { // 跳过第一行（标题行）
                    int cellNum = 0; // 从第一列开始
                    for (Cell cell : row) {
                        sheet.setColumnWidth(rowNums,256 * 12);
                        cell.setCellStyle(dataStyle2);
                        cellNum++; // 移动到下一列
                    }
                }
            }
            // 7. 写入到响应流
            workbook.write(response.getOutputStream());
        }
    }
    public static String convertZeroToEmpty(Object value) {
        // 处理null值
        if (value == null) {
            return "";
        }

        // 处理字符串类型
        if (value instanceof String) {
            String strValue = ((String) value).trim();

            // 尝试解析为数字
            try {
                // 处理整数
                if (strValue.matches("-?\\d+")) {
                    long longValue = Long.parseLong(strValue);
                    return longValue == 0 ? "" : strValue;
                }

                // 处理浮点数
                double doubleValue = Double.parseDouble(strValue);
                return doubleValue == 0.0 ? "" : strValue;
            } catch (NumberFormatException e) {
                // 不是数字格式的字符串，直接返回
                return strValue;
            }
        }

        // 处理数值类型
        if (value instanceof Number) {
            // 对于整数类型
            if (value instanceof Integer || value instanceof Long || value instanceof Short || value instanceof Byte) {
                long longValue = ((Number) value).longValue();
                return longValue == 0 ? "" : value.toString();
            }

            // 对于浮点数类型
            if (value instanceof Double || value instanceof Float) {
                double doubleValue = ((Number) value).doubleValue();
                return doubleValue == 0.0 ? "" : value.toString();
            }

            // 对于BigDecimal
            if (value instanceof java.math.BigDecimal) {
                java.math.BigDecimal decimalValue = (java.math.BigDecimal) value;
                return decimalValue.compareTo(java.math.BigDecimal.ZERO) == 0 ? "" : value.toString();
            }
        }

        // 其他类型直接返回字符串表示
        return value.toString();
    }
    private static void mergeCells(Sheet sheet, int firstRow, int firstCol, int lastRow, int lastCol) {
        CellRangeAddress cellRangeAddress = new CellRangeAddress(firstRow, lastRow, firstCol, lastCol);
        sheet.addMergedRegion(cellRangeAddress);
    }
    private static CellStyle createMainHeaderStyle(Workbook workbook) {
        // 创建字体
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 15);
        font.setColor(IndexedColors.WHITE.getIndex());
        font.setFontName("微软雅黑");

        // 创建样式
        CellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setFillForegroundColor(IndexedColors.ROYAL_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置边框
        style.setBorderTop(BorderStyle.MEDIUM);
        style.setBorderRight(BorderStyle.MEDIUM);
        style.setBorderBottom(BorderStyle.MEDIUM);
        style.setBorderLeft(BorderStyle.MEDIUM);
        style.setTopBorderColor(IndexedColors.DARK_BLUE.getIndex());
        style.setBottomBorderColor(IndexedColors.DARK_BLUE.getIndex());

        return style;
    }
}