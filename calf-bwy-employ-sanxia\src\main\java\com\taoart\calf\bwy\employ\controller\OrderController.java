package com.taoart.calf.bwy.employ.controller;

import com.taoart.calf.bwy.domain.TicketEmploy;
import com.taoart.calf.bwy.domain.bwg.BwgOrder;
import com.taoart.calf.bwy.employ.Constant;
import com.taoart.calf.bwy.employ.IgnoreAuth;
import com.taoart.calf.bwy.employ.SessionContextUtils;
import com.taoart.calf.bwy.service.EmployEnterpriseAuthService;
import com.taoart.calf.bwy.service.EmployOperLogService;
import com.taoart.calf.bwy.service.TicketUserService;
import com.taoart.calf.wypw.domain.*;
import com.taoart.common.bean.ExcelBean;
import com.taoart.common.bean.JsonResponse;
import com.taoart.common.bean.PageBean;
import com.taoart.common.utils.*;
import com.taoart.domain.user.User;
import com.taoart.wypw.sale.api.EnterpriseSaleServiceApi;
import com.taoart.wypw.sale.api.RefundFeeRuleSaleServiceApi;
import com.taoart.wypw.sale.api.TicketInformationSaleServiceApi;
import com.taoart.wypw.sale.api.TicketOrderRelationSaleServiceApi;
import com.taoart.wypw.sale.api.TicketOrderSaleServiceApi;
import com.taoart.wypw.sale.api.TicketSaleServiceApi;
import com.taoart.wypw.sale.api.TicketTimeSaleServiceApi;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.jxls.common.Context;
import org.jxls.util.JxlsHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("ticket/order")
public class OrderController extends DefaultController {

	@Autowired
	private TicketOrderSaleServiceApi ticketOrderService;
	@Autowired
	private TicketInformationSaleServiceApi ticketInformationService;
	@Autowired
	private EnterpriseSaleServiceApi ticketEnterpriseService;
	@Autowired
	private TicketOrderRelationSaleServiceApi ticketOrderRelationService;
	@Autowired
	private TicketSaleServiceApi ticketService;
	@Autowired
	private TicketUserService ticketUserService;
	@Autowired
	private TicketTimeSaleServiceApi ticketTimeService;
	@Autowired
	public RefundFeeRuleSaleServiceApi refundFeeRuleService;

	@Autowired
	private EmployOperLogService employOperLogService;

	@Autowired
	private EmployEnterpriseAuthService employEnterpriseAuthService;

	private static final Log log = LogFactory.getLog(OrderController.class);

	@RequestMapping("ticketSignIn")
	@ResponseBody
	public JsonResponse ticketSignIn(@RequestParam(defaultValue = "0") String ids) {
		JsonResponse jp = new JsonResponse();
		TicketEmploy loginEmployee = (TicketEmploy) SessionContextUtils.getLoginObject();
		try {
			TicketOrderRelation order = new TicketOrderRelation();
			order.setOrderIds(ids);
			order.setVerificationUserId(loginEmployee.getId());
			jp=ticketOrderService.batchVerification(order);
			employOperLogService.insertLog(loginEmployee.getEnterpriseId(), loginEmployee.getSystemType(), loginEmployee.getId(), 2, "【手动票务核销】核销ID为:" + ids);
		} catch (Exception e) {
			log.error(e,e);
			jp.setSuccess(false);
			jp.setMessage("核销失败，系统异常");
			jp.setErrorMessage("核销失败，系统异常");
		}
		return jp;
	}






	/**
	 * 订单列表
	 * 
	 * @Title: listOrder
	 * @Description: TODO
	 * @Param @return
	 * @Return ModelAndView
	 * @Throws
	 */
	@RequestMapping("list")
	public ModelAndView listOrder(String orderSerial,  String buyWay,String payWay,String verificationCode,
			@RequestParam(defaultValue = "2") int orderStatus, @RequestParam(defaultValue = "1") int pageNum,
			@RequestParam(defaultValue = "-1") int informationId, String buyDateStart, String buyDateEnd,
			String viewDateStart, String viewDateEnd, @RequestParam(defaultValue = "-1") int admissiontype,
								  @RequestParam(defaultValue = "0") int enterpriseId,String identification) {
		ModelAndView mav = new ModelAndView("admin/order_list");
		Map<String, Object> params = new HashMap<String, Object>();
		TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
		if (enterpriseId == 0) {
			enterpriseId= employ.getEnterpriseId();
		}

		//数据量过大导致列表加载很慢,给个预约时间默认值
		if (org.apache.commons.lang.StringUtils.isBlank(buyDateStart) || org.apache.commons.lang.StringUtils.isBlank(buyDateEnd)) {
			buyDateStart = CalfDateUtil.localDateToStr(LocalDate.now().minusDays(6)); // 默认6天前
			buyDateEnd = CalfDateUtil.localDateToStr(LocalDate.now());
		}

		params.put("orderSerial", orderSerial);

		if(!StringUtils.isBlank(identification)){
			if(identification.length() == 18 || identification.length() == 11) {
				params.put("saleOrderSerial", identification);
			}else {
				params.put("identificationName", identification);
			}
		}

		params.put("verificationCode", verificationCode);
		params.put("buyWay", buyWay);
		params.put("payWay", payWay);
		params.put("buyDateStart", buyDateStart);
		params.put("buyDateEnd", buyDateEnd);
		params.put("viewDateStart", viewDateStart);
		params.put("viewDateEnd", viewDateEnd);
		params.put("admissiontype", admissiontype); // 赠票
		params.put("orderStatus", orderStatus);
		params.put("enterpriseId",enterpriseId);
		params.put("infoType",1); //只查展览的
		List<TicketInformation> infoList = this.ticketInformationService.listAllBySort(enterpriseId,1);
		if (informationId == -1) {
			if (infoList != null && infoList.size() > 0) {
				informationId = infoList.get(0).getId();
			} else {
				informationId = 0;
			}
		}
		params.put("informationId", informationId);
		mav.addObject("orderSerial", orderSerial);
		mav.addObject("verificationCode", verificationCode);
		mav.addObject("identification", identification);
		mav.addObject("buyWay", buyWay);
		mav.addObject("payWay", payWay);
		mav.addObject("orderStatus", orderStatus);
		mav.addObject("buyDateStart", buyDateStart);
		mav.addObject("buyDateEnd", buyDateEnd);
		mav.addObject("admissiontype", admissiontype);
		mav.addObject("viewDateStart", viewDateStart);
		mav.addObject("viewDateEnd", viewDateEnd);
		mav.addObject("enterpriseId", enterpriseId);

		PageBean<TicketOrder> page = this.ticketOrderService.listByPageEmploy(params, pageNum, Constant.PAGE_SIZE);
		mav.addObject("pageBean", page);

		mav.addObject("infoList", infoList);
		mav.addObject("informationId", informationId);

		List<Enterprise> enterpriseList =employEnterpriseAuthService.enterpriseListByEmployId(employ.getId());
		mav.addObject("enterpriseList", enterpriseList);
		return mav;
	}



	/**
	 * 讲解列表
	 *
	 * @Title: listOrder
	 * @Description: TODO
	 * @Param @return
	 * @Return ModelAndView
	 * @Throws
	 */
	@RequestMapping("list-explain")
	public ModelAndView listOrderexplain(String orderSerial, String account, String buyWay,String payWay,String mobile,
								  @RequestParam(defaultValue = "2") int orderStatus, @RequestParam(defaultValue = "1") int pageNum,
								  @RequestParam(defaultValue = "-1") int informationId, String buyDateStart, String buyDateEnd,
								  String viewDateStart, String viewDateEnd, @RequestParam(defaultValue = "-1") int admissiontype,@RequestParam(defaultValue = "0") int enterpriseId) {
		ModelAndView mav = new ModelAndView("admin/order_list_explain");
		Map<String, Object> params = new HashMap<String, Object>();
		TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
		if (enterpriseId == 0) {
			enterpriseId= employ.getEnterpriseId();
		}

		//数据量过大导致列表加载很慢,给个预约时间默认值
		if (org.apache.commons.lang.StringUtils.isBlank(buyDateStart) || org.apache.commons.lang.StringUtils.isBlank(buyDateEnd)) {
			buyDateStart =  CalfDateUtil.localDateToStr(LocalDate.now().minusDays(6)); // 默认6天前
			buyDateEnd = CalfDateUtil.localDateToStr(LocalDate.now());
		}

		params.put("orderSerial", orderSerial);
		params.put("account", account);
		params.put("buyWay", buyWay);
		params.put("mobile", mobile);
		params.put("payWay", payWay);
		params.put("buyDateStart", buyDateStart);
		params.put("buyDateEnd", buyDateEnd);
		params.put("viewDateStart", viewDateStart);
		params.put("viewDateEnd", viewDateEnd);
		params.put("admissiontype", admissiontype); // 赠票
		params.put("orderStatus", orderStatus);
		params.put("enterpriseId",enterpriseId);
		params.put("infoType",3); //只查展览的
		List<TicketInformation> infoList = this.ticketInformationService.listByEnterpriseId(enterpriseId,
				3);
		params.put("informationId", informationId);
		mav.addObject("orderSerial", orderSerial);
		mav.addObject("account", account);
		mav.addObject("buyWay", buyWay);
		mav.addObject("payWay", payWay);
		mav.addObject("orderStatus", orderStatus);
		mav.addObject("buyDateStart", buyDateStart);
		mav.addObject("buyDateEnd", buyDateEnd);
		mav.addObject("admissiontype", admissiontype);
		mav.addObject("viewDateStart", viewDateStart);
		mav.addObject("viewDateEnd", viewDateEnd);
		mav.addObject("enterpriseId", enterpriseId);
		mav.addObject("mobile", mobile);

		PageBean<TicketOrder> page = this.ticketOrderService.listByPageEmploy(params, pageNum, Constant.PAGE_SIZE);
		//因为11月份带回来的新需求，要求展示讲解订单orderRelation表的手机号跟名字在订单列表页  只能这边先查出来
		page.getResult().forEach(v->{
			TicketOrderRelation ticketOrderRelation = new TicketOrderRelation();
			ticketOrderRelation.setOrderId(v.getId());
			List<TicketOrderRelation> list = ticketOrderRelationService.listAll(ticketOrderRelation);
			//手机号order表有 直接脱敏
			if(v.getMobile()!=null){
				v.setMobile(DesensitizationUtils.desensitizedPhoneNumber(v.getMobile()));
			}
			//姓名需要去relation表取，又因三峡这边的讲解订单只会有一个orderRelation 所以直接get（0）
			v.setUserName(DesensitizationUtils.protectedName(list.get(0).getName()));
		});
		mav.addObject("pageBean", page);

		mav.addObject("infoList", infoList);
		mav.addObject("informationId", informationId);

		List<Enterprise> enterpriseList =employEnterpriseAuthService.enterpriseListByEmployId(employ.getId());
		mav.addObject("enterpriseList", enterpriseList);
		return mav;
	}

	@RequestMapping("ticket-order-excel")
	@IgnoreAuth(requireLogin = true)
	public ResponseEntity<byte[]> ticketOrderExcel(HttpServletResponse response, HttpServletRequest request,
			String orderSerial, String account, String buyWay,String payWay, @RequestParam(defaultValue = "2") int orderStatus,
			@RequestParam(defaultValue = "0") int informationId, String buyDateStart, String buyDateEnd,
			String viewDateStart, String viewDateEnd, @RequestParam(defaultValue = "-1") int admissiontype,@RequestParam(defaultValue = "1") int enterpriseId)
					throws IOException {

		ClassLoader classLoader = getClass().getClassLoader();
		URL url = classLoader.getResource("excel/excel-template/ticket_order_template.xls");
		URL url2 = classLoader.getResource("excel/ticket_order_temp.xls");
		String num = RandomUtils.generateRandomNumber(5);
		new File(url2.getPath().replace(".xls", num + ".xls"));
		InputStream in = new FileInputStream(url.getPath());
		FileOutputStream out = new FileOutputStream(url2.getPath().replace(".xls", num + ".xls"));
		ExcelBean excelBean = new ExcelBean();
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("orderSerial", orderSerial);
		params.put("account", account);
		params.put("buyWay", buyWay);
		params.put("payWay", payWay);
		params.put("buyDateStart", buyDateStart);
		params.put("buyDateEnd", buyDateEnd);
		params.put("viewDateStart", viewDateStart);
		params.put("viewDateEnd", viewDateEnd);
		params.put("informationId", informationId);
		params.put("admissiontype", admissiontype); // 赠票
		params.put("orderStatus", orderStatus);
		TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
		params.put("enterpriseId", enterpriseId);

		PageBean<TicketOrder> page = this.ticketOrderService.listByPageEmploy(params, 1, Integer.MAX_VALUE);
		List<TicketOrder> list = page.getResult();
		for (TicketOrder order : list) {
			if(order.getUserId() > 0){
				User user = ticketUserService.selectById(order.getUserId());
				if(user != null){
					order.setMobile(user.getMobile());
				}
			}
			order.setDaily(DateUtil.formatDateTimeJieStr(order.getBuyTime()));

			//chartDate当viewDate先用用
			order.setChartDate(DateUtil.formatDateStr(order.getViewDate()));

			DecimalFormat format = new DecimalFormat("0.00");
			order.setFeeStr(format.format(order.getPayPrice()));
			if (order.getBuyWay() == 1) {
				order.setBuyWayStr("网络订票");
			} else if (order.getBuyWay() == 2) {
				order.setBuyWayStr("人工购票");
			} else if (order.getBuyWay() == 3) {
				order.setBuyWayStr("自助售票机");
			} else if (order.getBuyWay() == 4) {
				order.setBuyWayStr("APP");
			}else if (order.getBuyWay() == 5) {
				order.setBuyWayStr("携程");
			}

			if (order.getPayWay() == 1) {
				order.setPayWayStr("微信");
			} else if (order.getPayWay() == 2) {
				order.setPayWayStr("支付宝");
			} else if (order.getPayWay() == 3) {
				order.setPayWayStr("现金");
			} else if (order.getPayWay() == 4) {
				order.setPayWayStr("余额");
			} else if (order.getPayWay() == 5) {
				order.setPayWayStr("银联");
			} else if (order.getPayWay() == 6) {
				order.setPayWayStr("银联刷卡");
			} else if (order.getPayWay() == 7) {
				order.setPayWayStr("体验劵");
			} else if (order.getPayWay() == 8) {
				order.setPayWayStr("携程");
			}

			if (order.getStatus() == 1) {
				order.setStatusStr("未付款");
			} else if (order.getStatus() == 2) {
				String str = "已付款";
				if (order.getIsEVerification() == 1) {
					str += "(电子核销" + order.geteVerificationQuantity() + "/" + order.getQuantity() + "张,取票"
							+ order.getTakeTicketQuantity() + "/" + order.getQuantity() + "张,退票" + order.getRefundQuantity() + "/" + order.getQuantity() + "张)";
				} else if (order.getIsTakeTicket() == 1) {
					str += "(已取票" + order.getTakeTicketQuantity() + "/" + order.getQuantity() + "张,退票" + order.getRefundQuantity() + "/" + order.getQuantity() + "张)";
				}
				if (order.getIsTakeTicket() == 0 && order.getIsEVerification() == 0) {
					str += "(未取票,退票" + order.getRefundQuantity() + "/" + order.getQuantity() + "张)";
				}
				order.setStatusStr(str);
			} else if (order.getStatus() == 3) {
				String str = "交易关闭";
				if (!StringUtils.isBlank(order.getCloseReson())) {
					str += "(" + order.getCloseReson() + ")";
				}
				order.setStatusStr(str);
			}
		}

		try {
			Context context = new Context();
			excelBean.setListData(list);
			context.putVar("excelBean", excelBean);
			JxlsHelper.getInstance().processTemplate(in, out, context);
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		File f = null;
		f = new File(url2.getPath().replace(".xls", num + ".xls")); // 将服务器上的文件下载下来
		HttpHeaders headers = new HttpHeaders();
		String fileName = new String("订单记录.xls".getBytes("UTF-8"), "iso-8859-1");
		headers.setContentType(MediaType.valueOf("application/x-excel;charset=UTF-8"));
		headers.add("Content-Disposition", "attachment; filename=" + fileName);
		headers.add("Content-Length", String.valueOf(f.length()));
		byte[] b = FileUtils.readFileToByteArray(f);
		if (f.exists())
			f.delete();
		return new ResponseEntity<byte[]>(b, headers, HttpStatus.OK);

	}

	/**
	 * 活动预约列表
	 * 
	 * @Title: listOrder
	 * @Description: TODO
	 * @Param @return
	 * @Return ModelAndView
	 * @Throws
	 */
	@RequestMapping("list-order-relation-activity")
	public ModelAndView listOrderRelationActivity(String orderSerial, String account, String buyWay,String payWay,
												  @RequestParam(defaultValue = "2") int orderStatus, @RequestParam(defaultValue = "1") int pageNum,
												  @RequestParam(defaultValue = "-1") int informationId, String buyDateStart, String buyDateEnd,
												  String viewDateStart, String viewDateEnd, @RequestParam(defaultValue = "-1") int admissiontype,@RequestParam(defaultValue = "0") int enterpriseId) {
		ModelAndView mav = new ModelAndView("admin/order_relation_list_activity");
		TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
		if (enterpriseId == 0) {
			enterpriseId= employ.getEnterpriseId();
		}
		//数据量过大导致列表加载很慢,给个预约时间默认值
		if (org.apache.commons.lang.StringUtils.isBlank(buyDateStart) || org.apache.commons.lang.StringUtils.isBlank(buyDateEnd)) {
			buyDateStart =  CalfDateUtil.localDateToStr(LocalDate.now().minusDays(6)); // 默认6天前
			buyDateEnd = CalfDateUtil.localDateToStr(LocalDate.now());
		}
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("orderSerial", orderSerial);
		params.put("account", account);
		params.put("buyWay", buyWay);
		params.put("payWay", payWay);
		params.put("buyDateStart", buyDateStart);
		params.put("buyDateEnd", buyDateEnd);
		params.put("viewDateStart", viewDateStart);
		params.put("viewDateEnd", viewDateEnd);
		params.put("admissiontype", admissiontype); // 赠票
		params.put("orderStatus", orderStatus);
		params.put("enterpriseId",enterpriseId);
		params.put("infoType",2); //只查活动的
		List<TicketInformation> infoList = this.ticketInformationService.listByEnterpriseId(enterpriseId,
				2);
		params.put("informationId", informationId);
		PageBean<TicketOrder> page = this.ticketOrderService.listByPageEmploy(params, pageNum, Constant.PAGE_SIZE);
		mav.addObject("orderSerial", orderSerial);
		mav.addObject("account", account);
		mav.addObject("buyWay", buyWay);
		mav.addObject("payWay", payWay);
		mav.addObject("orderStatus", orderStatus);
		mav.addObject("buyDateStart", buyDateStart);
		mav.addObject("buyDateEnd", buyDateEnd);
		mav.addObject("admissiontype", admissiontype);
		mav.addObject("viewDateStart", viewDateStart);
		mav.addObject("viewDateEnd", viewDateEnd);
		mav.addObject("enterpriseId", enterpriseId);

		mav.addObject("pageBean", page);

		mav.addObject("informationId", informationId);

		List<Enterprise> enterpriseList =employEnterpriseAuthService.enterpriseListByEmployId(employ.getId());
		mav.addObject("enterpriseList", enterpriseList);
		// 区分意向金活动
		Iterator<TicketInformation> informationIt = infoList.iterator();
		TicketInformation information = new TicketInformation();
		while (informationIt.hasNext()) {
			information = informationIt.next();
			if (information.getFeature() == 4||information.getFeature() == 3) {
				informationIt.remove();
			}

		}
		mav.addObject("infoList", infoList);
		return mav;
	}
	
	/**
	 * 活动预约-意向金
	 * @param account
	 * @param orderStatus
	 * @param pageNum
	 * @param informationId
	 * @param buyDate
	 * @param viewDate
	 * @return
	 */
	@RequestMapping("list-order-relation-activity-earnest")
	public ModelAndView listOrderRelationActivityEarnest(String account,String ticketName, @RequestParam(defaultValue = "1") int orderStatus,
			@RequestParam(defaultValue = "1") int pageNum, @RequestParam(defaultValue = "0") int informationId,
			String buyDate, String viewDate) {
		ModelAndView mav = new ModelAndView("admin/order_relation_list_activity_earnest");
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("account", account);
		params.put("viewDate", viewDate);
		params.put("buyDate", buyDate);
		params.put("informationId", informationId);
		params.put("informationType", 2);
		params.put("orderStatus", orderStatus);
		params.put("feature", 4);
		params.put("ticketName", ticketName);
		mav.addObject("account", account);
		mav.addObject("orderStatus", orderStatus);
		mav.addObject("buyDate", buyDate);
		mav.addObject("viewDate", viewDate);
		mav.addObject("ticketName", ticketName);
		TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
		params.put("enterpriseId", employ.getEnterpriseId());
		
		PageBean<TicketOrderRelation> page = this.ticketOrderRelationService.listByPageActivity(params, pageNum,
				Constant.PAGE_SIZE);
		mav.addObject("pageBean", page);
		
		List<TicketInformation> infoList = this.ticketInformationService.listByEnterpriseId(employ.getEnterpriseId(),
				2);
		
		// 区分意向金活动
		Iterator<TicketInformation> informationIt = infoList.iterator();
		TicketInformation information = new TicketInformation();
		while (informationIt.hasNext()) {
			information = informationIt.next();
			if (information.getFeature() != 4) {
				informationIt.remove();
			}
			
		}
		TicketTime ticketTime = new TicketTime();
		ticketTime.setEnterpriseId(employ.getEnterpriseId());
		ticketTime.setType(2);
		ticketTime.setIsDelete(1);
		List<TicketTime> timeList = this.ticketTimeService.listAll(ticketTime);
		
		Ticket ticket = new Ticket();
		ticket.setEnterpriseId(employ.getEnterpriseId());
		List<Ticket> ticketList = this.ticketService.listAll(ticket);
		
		mav.addObject("infoList", infoList);
		mav.addObject("timeList", timeList);
		mav.addObject("ticketList", ticketList);
		mav.addObject("informationId", informationId);
		return mav;
	}

	@RequestMapping("activity-list-excel")
	@IgnoreAuth(requireLogin = true)
	public ResponseEntity<byte[]> excelMovieDaily(HttpServletResponse response, HttpServletRequest request,
												  String orderSerial, String account, String buyWay,String payWay, @RequestParam(defaultValue = "2") int orderStatus,
												  @RequestParam(defaultValue = "0") int informationId, String buyDateStart, String buyDateEnd,
												  String viewDateStart, String viewDateEnd, @RequestParam(defaultValue = "-1") int admissiontype,@RequestParam(defaultValue = "1") int enterpriseId) throws IOException {
		TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
		ClassLoader classLoader = getClass().getClassLoader();
		/**
		 * getResource()方法会去classpath下找这个文件，获取到url resource,
		 * 得到这个资源后，调用url.getFile获取到 文件 的绝对路径
		 */
		//导入模板
		URL url = classLoader.getResource("excel/excel-template/ticket_order_template.xls");
		URL url2 = classLoader.getResource("excel/ticket_order_temp.xls");
		String num = RandomUtils.generateRandomNumber(5);
		new File(url2.getPath().replace(".xls", num + ".xls"));
		InputStream in = new FileInputStream(url.getPath());
		FileOutputStream out = new FileOutputStream(url2.getPath().replace(".xls", num + ".xls"));
		ExcelBean excelBean = new ExcelBean();
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("orderSerial", orderSerial);
		params.put("account", account);
		params.put("buyWay", buyWay);
		params.put("payWay", payWay);
		params.put("buyDateStart", buyDateStart);
		params.put("buyDateEnd", buyDateEnd);
		params.put("viewDateStart", viewDateStart);
		params.put("viewDateEnd", viewDateEnd);
		params.put("informationId", informationId);
		params.put("informationType", 2);
		params.put("orderStatus", 2);

		params.put("enterpriseId", enterpriseId);
		PageBean<TicketOrder> page = this.ticketOrderService.listByPageEmploy(params, 1, Integer.MAX_VALUE);
		List<TicketOrder> list = page.getResult();
		for (TicketOrder order : list) {
			if(order.getUserId() > 0){
				User user = ticketUserService.selectById(order.getUserId());
				if(user != null){
					order.setMobile(user.getMobile());
				}
			}
			order.setDaily(DateUtil.formatDateTimeJieStr(order.getBuyTime()));

			//chartDate当viewDate先用用
			order.setChartDate(DateUtil.formatDateStr(order.getViewDate()));

			DecimalFormat format = new DecimalFormat("0.00");
			order.setFeeStr(format.format(order.getPayPrice()));
			if (order.getBuyWay() == 1) {
				order.setBuyWayStr("网络订票");
			} else if (order.getBuyWay() == 2) {
				order.setBuyWayStr("人工购票");
			} else if (order.getBuyWay() == 3) {
				order.setBuyWayStr("自助售票机");
			} else if (order.getBuyWay() == 4) {
				order.setBuyWayStr("APP");
			}

			if (order.getPayWay() == 1) {
				order.setPayWayStr("微信");
			} else if (order.getPayWay() == 2) {
				order.setPayWayStr("支付宝");
			} else if (order.getPayWay() == 3) {
				order.setPayWayStr("现金");
			} else if (order.getPayWay() == 4) {
				order.setPayWayStr("余额");
			} else if (order.getPayWay() == 5) {
				order.setPayWayStr("银联");
			} else if (order.getPayWay() == 6) {
				order.setPayWayStr("银联刷卡");
			} else if (order.getPayWay() == 7) {
				order.setPayWayStr("体验劵");
			}

			if (order.getStatus() == 1) {
				order.setStatusStr("未付款");
			} else if (order.getStatus() == 2) {
				String str = "已付款";
				if (order.getIsEVerification() == 1) {
					str += "(电子核销" + order.geteVerificationQuantity() + "/" + order.getQuantity() + "张,取票"
							+ order.getTakeTicketQuantity() + "/" + order.getQuantity() + "张,退票" + order.getRefundQuantity() + "/" + order.getQuantity() + "张)";
				} else if (order.getIsTakeTicket() == 1) {
					str += "(已取票" + order.getTakeTicketQuantity() + "/" + order.getQuantity() + "张,退票" + order.getRefundQuantity() + "/" + order.getQuantity() + "张)";
				}
				if (order.getIsTakeTicket() == 0 && order.getIsEVerification() == 0) {
					str += "(未取票,退票" + order.getRefundQuantity() + "/" + order.getQuantity() + "张)";
				}
				order.setStatusStr(str);
			} else if (order.getStatus() == 3) {
				String str = "交易关闭";
				if (!StringUtils.isBlank(order.getCloseReson())) {
					str += "(" + order.getCloseReson() + ")";
				}
				order.setStatusStr(str);
			}
		}

		try {
			Context context = new Context();
			excelBean.setListData(list);
			context.putVar("excelBean", excelBean);
			JxlsHelper.getInstance().processTemplate(in, out, context);
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		File f = null;
		f = new File(url2.getPath().replace(".xls", num + ".xls")); // 将服务器上的文件下载下来
		HttpHeaders headers = new HttpHeaders();
		String fileName = new String("订单记录.xls".getBytes("UTF-8"), "iso-8859-1");
		headers.setContentType(MediaType.valueOf("application/x-excel;charset=UTF-8"));
		headers.add("Content-Disposition", "attachment; filename=" + fileName);
		headers.add("Content-Length", String.valueOf(f.length()));
		byte[] b = FileUtils.readFileToByteArray(f);
		if (f.exists())
			f.delete();
		return new ResponseEntity<byte[]>(b, headers, HttpStatus.OK);
	}

	/**
	 * 订单列表
	 * 
	 * @Title: listOrder
	 * @Description: TODO
	 * @Param @return
	 * @Return ModelAndView
	 * @Throws
	 */
	@RequestMapping("free-list-order")
	public ModelAndView freeListOrder(@RequestParam(defaultValue = "0") int informationId,
			@RequestParam(defaultValue = "0") int ticketEmployId, @RequestParam(defaultValue = "1") int pageNum) {
		ModelAndView mav = new ModelAndView("admin/free_order_list");
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("informationId", informationId);
		params.put("ticketEmployId", ticketEmployId);
		params.put("admissiontype", 1); // 赠票
		TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
		params.put("enterpriseId", employ.getEnterpriseId());

		PageBean<TicketOrder> page = this.ticketOrderService.listByPageEmploy(params, pageNum, Constant.PAGE_SIZE);
		if (page != null && CollectionUtils.isNotEmpty(page.getResult())) {
			for (TicketOrder ticketOrder : page.getResult()) {
				if (ticketOrder.getEnterpriseId() != employ.getEnterpriseId()) {
					return new ModelAndView("redirect:/auth/no-auth.htm");
				}
			}
		}
		mav.addObject("pageBean", page);

		List<TicketInformation> infoList = this.ticketInformationService.listByEnterpriseId(employ.getEnterpriseId(),
				0);
		mav.addObject("infoList", infoList);
		mav.addObject("informationId", informationId);
		mav.addObject("ticketEmployId", ticketEmployId);
		return mav;
	}

	@RequestMapping("free-order-detail")
	public ModelAndView freeOrderDetail(int id, int ticketEmployId, int informationId) {
		ModelAndView mav = new ModelAndView("admin/free_order_detail");
		TicketOrder order = this.ticketOrderService.selectById(id);
		TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
		if (order == null ) {
			return new ModelAndView("redirect:/auth/no-auth.htm");
		}
		mav.addObject("order", order);
		List<TicketOrderRelation> list = this.ticketOrderRelationService.listByOrderId(id);
		Ticket ticket = ticketService.selectById(list.get(0).getTicketId());
		TicketTime time = ticketTimeService.selectById(list.get(0).getTimeId());
		mav.addObject("list", list).addObject("ticket", ticket).addObject("time", time);
		mav.addObject("ticketEmployId", ticketEmployId);
		mav.addObject("informationId", informationId);
		return mav;
	}

	@RequestMapping("detail")
	public ModelAndView orderDetail(int id) {
		ModelAndView mav = new ModelAndView("admin/order_detail");
		TicketOrder order = this.ticketOrderService.selectById(id);
		TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
		if (order == null) {
			return new ModelAndView("redirect:/auth/no-auth.htm");
		}
		mav.addObject("order", order);
		List<TicketOrderRelation> list = this.ticketOrderRelationService.listByOrderId(id);
		List<Ticket> ticketList = new ArrayList<Ticket>();
		if (list.size() > 0) {
			Ticket ticket = ticketService.selectById(list.get(0).getTicketId());
			if (ticket.getTicketSign() != null && !"".equals(ticket.getTicketSign())) {
				ticketList.add(ticket);
				for (int i = 1; i < list.size(); i++) {
					ticketList.add(ticketService.selectById(list.get(i).getTicketId()));
				}
			}else{
				list.forEach(v->{
					int age=0;
					if(v.getCardType()==1){
						age=StringUtils.getCurrentAge(v.getIdcard());
					}
					v.setAge(age);
					if (v.getCardType() == 1 || v.getCardType() == 9) {
						v.setIdcard(DesensitizationUtils.idMask(v.getIdcard(), 6, 5));
					} else {
						v.setIdcard(DesensitizationUtils.idMask(v.getIdcard(), 2, 2));
					}
					//v.setIdcard(DesensitizationUtils.idMask(v.getIdcard(),6,5));
					v.setName(DesensitizationUtils.protectedName(v.getName()));
					v.setMobile(DesensitizationUtils.desensitizedPhoneNumber(v.getMobile()));
				});
			}
		}

		TicketTime time = ticketTimeService.selectById(list.get(0).getTimeId());
		mav.addObject("list", list).addObject("time", time);
		mav.addObject("ticketList", ticketList);

		return mav;
	}

	@RequestMapping("detail-explain")
	public ModelAndView orderDetailexplain(int id) {
		ModelAndView mav = new ModelAndView("admin/order_detail_explain");
		TicketOrder order = this.ticketOrderService.selectById(id);
		TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
		if (order == null ) {
			return new ModelAndView("redirect:/auth/no-auth.htm");
		}
		mav.addObject("order", order);
		List<TicketOrderRelation> list = this.ticketOrderRelationService.listByOrderId(id);
		List<Ticket> ticketList = new ArrayList<Ticket>();
		if (list.size() > 0) {
			Ticket ticket = ticketService.selectById(list.get(0).getTicketId());
			if (ticket.getTicketSign() != null && !"".equals(ticket.getTicketSign())) {
				ticketList.add(ticket);
				for (int i = 1; i < list.size(); i++) {
					ticketList.add(ticketService.selectById(list.get(i).getTicketId()));
				}
			}else{
				list.forEach(v->{
					v.setIdcard(DesensitizationUtils.idMask(v.getIdcard(),6,5));
					v.setName(DesensitizationUtils.protectedName(v.getName()));
					v.setMobile(DesensitizationUtils.desensitizedPhoneNumber(v.getMobile()));
				});
			}
		}

		TicketTime time = ticketTimeService.selectById(list.get(0).getTimeId());
		mav.addObject("list", list).addObject("time", time);
		mav.addObject("ticketList", ticketList);

		return mav;
	}
	@RequestMapping("detail-activity")
	public ModelAndView orderDetailactivity(int id) {
		ModelAndView mav = new ModelAndView("admin/order_detail_activity");
		TicketOrder order = this.ticketOrderService.selectById(id);
		TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
		if (order == null ) {
			return new ModelAndView("redirect:/auth/no-auth.htm");
		}
		mav.addObject("order", order);
		List<TicketOrderRelation> list = this.ticketOrderRelationService.listByOrderId(id);
		List<Ticket> ticketList = new ArrayList<Ticket>();
		if (list.size() > 0) {
			Ticket ticket = ticketService.selectById(list.get(0).getTicketId());
			if (ticket.getTicketSign() != null && !"".equals(ticket.getTicketSign())) {
				ticketList.add(ticket);
				for (int i = 1; i < list.size(); i++) {
					ticketList.add(ticketService.selectById(list.get(i).getTicketId()));
				}
			}else{
				list.forEach(v->{
					v.setIdcard(DesensitizationUtils.idMask(v.getIdcard(),6,5));
					v.setName(DesensitizationUtils.protectedName(v.getName()));
					v.setMobile(DesensitizationUtils.desensitizedPhoneNumber(v.getMobile()));
				});
			}
		}

		TicketTime time = ticketTimeService.selectById(list.get(0).getTimeId());
		mav.addObject("list", list).addObject("time", time);
		mav.addObject("ticketList", ticketList);

		return mav;
	}

	/**
	 * 退票
	 * 
	 * @Title: ajaxOrderRefund
	 * @Description: TODO
	 * @Param @param order
	 * @Param @return
	 * @Return JsonResponse
	 * @Throws
	 */
	@RequestMapping("ajax-order-refund")
	@ResponseBody
	public JsonResponse ajaxOrderRefund(@RequestBody TicketOrderDto order) {
		order.setCloseReson("后台退款");
		TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
		order.setOperId(employ.getId());
		if (order.getId() > 0){
			TicketOrder orderDto = ticketOrderService.selectById(order.getId());
			if (orderDto == null ){
				return this.fail("无权限！");
			}
		}
		JsonResponse jp = ticketOrderService.refund(order);
		return jp;
	}

	/**
	 * 可退的票
	 *
	 * @Title: ajaxOrderRefund
	 * @Description: TODO
	 * @Param @param order
	 * @Param @return
	 * @Return JsonResponse
	 * @Throws
	 */
	@RequestMapping("ajax-ticket-refund")
	@ResponseBody
	@IgnoreAuth(requireLogin = true)
	public JsonResponse ajaxTicketRefund(int orderId) {
		JsonResponse jp = this.success();
		TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
		TicketOrder order = ticketOrderService.selectById(orderId);
		if (order == null){
			return this.fail("无权限！");
		}
		TicketOrderRelation params = new TicketOrderRelation();
		params.setOrderId(orderId);
		params.setRefundStatus(2);
		List<TicketOrderRelation> result = ticketOrderRelationService.listAll(params);
		for (TicketOrderRelation tor : result) {
			RefundFeeRule refundFeeRule = new RefundFeeRule();
			refundFeeRule.setTicketId(tor.getTicketId());
			List<RefundFeeRule> rfList = refundFeeRuleService.listAll(refundFeeRule);
			if (rfList != null && rfList.size() > 0) { // 阶梯退款
				TicketTime time = ticketTimeService.selectById(tor.getTimeId());
				String viewTime = DateUtils.dateToStr(order.getViewDate(), "yyyy-MM-dd") + " "
						+ DateUtils.dateToStr(time.getStartTime(), "HH:mm:ss");
				long datediff = DateUtil.getDateDiff(new Date(), DateUtils.strToDate(viewTime));
				int percent = 0;
				for (RefundFeeRule rf : rfList) {
					if (datediff < (rf.getHour() * 3600)) {
						percent = rf.getPercent();
						break;
					}
				}
	
				java.text.DecimalFormat dfd = new java.text.DecimalFormat("#0.00");
				BigDecimal b1 = new BigDecimal(dfd.format((double) (100 - percent) * 0.01));
				tor.setRefundPrice(tor.getTicketPrice().multiply(b1));
			}else{
					tor.setRefundPrice(tor.getTicketPrice());
			}
		
			Ticket ticket = ticketService.selectById(tor.getTicketId());
			tor.setTicketName(ticket.getName());
		}
		jp.setResult(result);
		return jp;
	}

	/**
	 * 是否有已核销
	 * 
	 * @Title: ajaxOrderVerification
	 * @Description: TODO
	 * @Param @param order
	 * @Param @return
	 * @Return JsonResponse
	 * @Throws
	 */
	@RequestMapping("ajax-order-verification")
	@ResponseBody
	@IgnoreAuth(requireLogin = true)
	public JsonResponse ajaxOrderVerification(TicketOrder order) {
		JsonResponse jp = this.success();
		TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
		TicketOrder sorder = this.ticketOrderService.selectById(order.getId());
		if (sorder != null) {
			TicketInformation ticketInformation = this.ticketInformationService.selectById(sorder.getInformationId());
			if (null != ticketInformation && ticketInformation.getIsVerification() == 1) {
				List<TicketOrderRelation> relist = sorder.getRelationList();
				for (int i = 0; i < relist.size(); i++) {
					if (relist.get(i).getIsVerification() == 1) {
						jp.setSuccess(false);
					}
				}
			}
		}
		return jp;
	}

	@RequestMapping("eticket-list")
	public ModelAndView eticketList(@RequestParam(defaultValue = "1") int pageNum,
			@RequestParam(defaultValue = "-1") int informationId, @RequestParam(defaultValue = "") String name) {
		ModelAndView mav = new ModelAndView("admin/bwg/eticket_list");
		TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
		if (informationId > 0){
			TicketInformation information = ticketInformationService.selectById(informationId);
			if (information == null ){
				return new ModelAndView("redirect:/auth/no-auth.htm");
			}
		}
		if (!StringUtils.isBlank(name)) {
			name = name.trim();
		}
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("enterpriseId", employ.getEnterpriseId());
		if (informationId != -1) {
			params.put("informationId", informationId);
		}
		params.put("name", name);
		PageBean<TicketOrder> page = this.ticketOrderService.getEticketList(params, pageNum, Constant.PAGE_SIZE);
		TicketInformation ticketInformation = new TicketInformation();
		ticketInformation.setEnterpriseId(employ.getEnterpriseId());
		List<TicketInformation> informationList = ticketInformationService.listAll(ticketInformation);
		List<TicketInformation> informationzpList = ticketInformationService.listZp(employ.getEnterpriseId());
		for (int i = 0; i < informationzpList.size(); i++) {
			Date nowTime = new Date();
			Date endTime = informationzpList.get(i).getEndTime();
			if (nowTime.compareTo(endTime) == 1) {
				informationzpList.remove(i);
			}
		}
		mav.addObject("pageBean", page).addObject("informationList", informationList)
				.addObject("informationId", informationId).addObject("informationzpList", informationzpList)
				.addObject("truename", name);
		return mav;
	}

	@RequestMapping("check-pwd")
	@ResponseBody
	public JsonResponse refundGoods(BwgOrder bwgOrder) {
		JsonResponse json = this.success();
		// 判断二级密码
		TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
		if (employ != null) {
			Enterprise tep = ticketEnterpriseService.selectById(employ.getEnterpriseId());
			try {
				if (!tep.getRefundPassword().equals(bwgOrder.getRefundPassword())) {
					return this.fail("密码错误！");
				}
			} catch (Exception e) {
				return this.fail("密码错误！");
			}
		} else {
			return this.fail("请重新登录");
		}
		return json;
	}

	@RequestMapping("add-eticket")
	@ResponseBody
	public JsonResponse addEticket(BwgOrder bwgOrder, int informationId) {
		JsonResponse json = this.success();
		int ticketCount = bwgOrder.getQuantity();
		TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
		if (informationId > 0){
			TicketInformation information = ticketInformationService.selectById(informationId);
			if (information == null ){
				return this.fail("无权限！");
			}
		}
		int enterpriserId = employ.getEnterpriseId();
		Enterprise enterprise = ticketEnterpriseService.selectById(enterpriserId);
		if (enterprise == null) {
			return this.fail("企业信息错误！");
		}
		// 获取赠票id
		Ticket ticket = new Ticket();
		ticket.setTypeCode(3);
		ticket.setInformationId(informationId);
		List<Ticket> ticketList = ticketService.listAll(ticket);
		if (CollectionUtils.isEmpty(ticketList)) {
			return this.fail("无门票信息，请先创建赠票门票");
		}
		int ticketId = ticketList.get(0).id;
		// 获取场次id,此处需要后期优化，多个场次时应该在前端选择
		TicketTime time = new TicketTime();
		time.setInformationId(informationId);
		List<TicketTime> timeList = ticketTimeService.listAll(time);
		if (CollectionUtils.isEmpty(timeList)) {
			return this.fail("无场次信息，请先创建创次");
		}
		int timeId = timeList.get(0).getId();
		TicketOrder order = new TicketOrder();
		order.setInformationId(informationId);
		order.setEnterpriseId(enterpriserId);
		order.setTimeId(timeId);
		order.setQuantity(ticketCount);
		order.setPayPrice(new BigDecimal(0));
		order.setStatus(1);
		order.setOperId(employ.getId());
		order.setBuyTime(new Date());
		order.setCreateTime(new Date());
		order.setUpdateTime(new Date());
		order.setIsFreeTicket(1);
		order.setIsTransfer(0);
		order.setBuyWay(2);
		Date date = new Date();
		date = DateUtils.addDay(date, 1);
		order.setViewDate(date);
		List<TicketOrderRelation> list = new ArrayList<>();
		TicketOrderRelation ticketOrderRelation = null;
		ticketOrderRelation = new TicketOrderRelation();
		ticketOrderRelation.setIdcard("");
		ticketOrderRelation.setName("观众");
		ticketOrderRelation.setTicketId(ticketId);
		ticketOrderRelation.setTimeId(timeId);
		ticketOrderRelation.setTicketEmployId(0);
		for (int i = 0; i < ticketCount; i++) {
			list.add(ticketOrderRelation);
		}
		order.setRelationList(list);
		json = ticketOrderService.createOrder(order);

		return json;
	}

	@RequestMapping("ticket_refund_is_success")
	@ResponseBody
	@IgnoreAuth
	public JsonResponse ticketPaySccess(@RequestParam(defaultValue = "0") int id,
			@RequestParam(defaultValue = "") String relationIdList) {
		TicketOrder order = ticketOrderService.selectById(id);
		TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
		if (order == null
				|| org.apache.commons.lang.StringUtils.isBlank(relationIdList)) {
			return this.fail("退款失败");
		}
		List<String> list = Arrays.asList(relationIdList.split(","));
		int count = 0;
		for (String s : list) {
			TicketOrderRelation relation = ticketOrderRelationService.selectById(Integer.parseInt(s));
			if (relation.getRefundStatus() == 1) {
				count++;
			}
		}
		if (count == list.size()) {
			return this.success();
		}
		return this.fail("退款失败");
	}

	@RequestMapping("ticket-activity-refund")
	@ResponseBody
	public JsonResponse ticketRefund(@RequestParam(defaultValue = "0") int id) {
		JsonResponse json = this.success();
		TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
		if (id > 0) {
			TicketOrderRelation relation = ticketOrderRelationService.selectById(id);
			TicketOrder order = ticketOrderService.selectById(relation.getOrderId());
			if (order == null || order.getEnterpriseId() != employ.getEnterpriseId()){
				return this.fail("无权限！");
			}
			ticketOrderRelationService.refundTicketById(id);
		}
		return json;
	}
}
