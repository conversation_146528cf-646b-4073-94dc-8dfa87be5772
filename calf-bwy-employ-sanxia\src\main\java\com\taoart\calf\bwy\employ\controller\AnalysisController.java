package com.taoart.calf.bwy.employ.controller;

import com.taoart.calf.bwy.domain.PrebookVisit;
import com.taoart.calf.bwy.domain.TicketEmploy;
import com.taoart.calf.bwy.employ.Constant;
import com.taoart.calf.bwy.employ.IgnoreAuth;
import com.taoart.calf.bwy.employ.SessionContextUtils;
import com.taoart.calf.bwy.service.EmployEnterpriseAuthService;
import com.taoart.calf.bwy.service.PrebookVisitService;
import com.taoart.calf.bwy.service.TicketUserService;
import com.taoart.calf.wypw.domain.*;
import com.taoart.calf.wypw.model.OrderArea;
import com.taoart.common.bean.ExcelBean;
import com.taoart.common.bean.JsonResponse;
import com.taoart.common.bean.PageBean;
import com.taoart.common.utils.*;
import com.taoart.domain.user.User;
import com.taoart.wypw.sale.api.*;
import com.taoart.wypw.statistic.api.*;
import net.sf.json.JSONArray;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.io.FileUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.jxls.common.Context;
import org.jxls.util.JxlsHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URL;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

@Controller
@RequestMapping("analysis")
public class AnalysisController extends DefaultController {

    private static final Log log = LogFactory.getLog(AnalysisController.class);

    @Autowired
    private TicketInformationSaleServiceApi ticketInformationService;

    @Autowired
    private TicketOrderStatisticServiceApi ticketOrderService;

    @Autowired
    private TicketOrderRelationStatisticServiceApi ticketOrderRelationService;

    @Autowired
    private WeatherStatisticServiceApi weatherService;

    @Autowired
    private EnterpriseSaleServiceApi ticketEnterpriseService;

    @Autowired
    private TicketUserDataStatisticServiceApi ticketUserDataService;

    @Autowired
    private VisitorStatisticServiceApi visitorService;

    @Autowired
    private VisitorSignoutStatisticServiceApi visitorSignoutService;

    @Autowired
    private PrebookInfoConfigSaleServiceApi prebookInfoConfigService;

    @Autowired
    private PrebookPeriodSaleServiceApi prebookService;

    @Autowired
    private PrebookVisitService prebookVisitService;

    @Autowired
    private EnterpriseSaleServiceApi enterpriseSaleServiceApi;

    @Autowired
    private PrebookStatisVisitorServiceApi statisVisitorService;
    @Autowired
    private PrebookStatisAgeServiceApi statisAgeService;
    @Autowired
    private PrebookStatisAreaServiceApi statisAreaService;

    @Autowired
    private EmployEnterpriseAuthService employEnterpriseAuthService;

    @Autowired
    private TicketUserService ticketUserService;
    @Autowired
    private PrebookRecordSaleServiceApi prebookRecordSaleServiceApi;
    @Autowired
    private TicketUserRelationSaleServiceApi ticketUserRelationSaleServiceApi;


    @RequestMapping("wz_visitor_year")
    public ModelAndView wz_visitor_year() throws Exception {
        ModelAndView mav = new ModelAndView("admin/wz_visitor_year");
        return mav;
    }


    @RequestMapping("cq-data")
    public ModelAndView cqdata(@RequestParam(defaultValue = "1") int enterpriseId) throws Exception {
        ModelAndView mav = new ModelAndView("cq_data");
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();


        //注册用户
        int userCount=ticketUserService.selectByNameOrMobileOrEmail(new User());
        mav.addObject("userCount", userCount);

        //得到当日的进馆人数 预约数量 进馆人次
        List<Visitor> list = visitorService.getByDatePrebookCount(new Date(), new Date(), enterpriseId, 1);
        Map<String, Integer> map = visitorDataCount(list);
        int yyCount = map.get("yyCount");
        mav.addObject("jgCount", map.get("jgCount"));  //进馆人数
        mav.addObject("yyCount", map.get("yyCount"));  //预约数量
        mav.addObject("rcCount", map.get("rcCount"));  //进馆人次


        //得到昨日的进馆人数 预约数量 进馆人次
        list = visitorService.getByDatePrebookCount(DateUtils.addDay(new Date(), -1), DateUtils.addDay(new Date(), -1), enterpriseId, 1);
        map = visitorDataCount(list);
        mav.addObject("yesterdayJgCount", map.get("jgCount"));  //进馆人数
        mav.addObject("yesterdayYyCount", map.get("yyCount"));  //预约数量
        mav.addObject("yesterdayRcCount", map.get("rcCount"));  //进馆人次


        //男女进馆比例
        list = visitorService.getBySexcount(DateUtils.dateToStr(new Date(), "yyyy-MM-dd"), DateUtils.dateToStr(new Date(), "yyyy-MM-dd"), enterpriseId, 1);
        int womenCount = 0;
        int visitCount = 0;
        int wzCount = 0;
        for (Visitor visitor : list) {
            if (visitor.getSex() == 1) {
                visitCount = visitor.getSignCount();
            } else if (visitor.getSex() == 2) {
                womenCount = visitor.getSignCount();
            } else if (visitor.getSex() == 0) {
                wzCount = visitor.getSignCount();
            }
        }
        mav.addObject("womenCount", womenCount);  //女性进馆人数
        mav.addObject("visitCount", visitCount);  //男性进馆人数
        mav.addObject("wzCount", wzCount);  //未知进馆人数
        //年龄分布
        list = visitorService.selectAll(new Date(), new Date(), enterpriseId, null);
        map = visitorAgeDataCount(list);
        mav.addObject("b0To30", map.get("b0To30"));
        mav.addObject("b30To39", map.get("b30To39"));
        mav.addObject("b40To49", map.get("b40To49"));
        mav.addObject("b50To59", map.get("b50To59"));
        mav.addObject("b60To99", map.get("b60To99"));
        mav.addObject("b100", map.get("b100"));
        mav.addObject("other", map.get("other"));
        mav.addObject("total", map.get("total"));


        //今日团队预约数
        int tdyyCount = 0;
        List<Visitor> visitors = visitorService.listAllByStatisPre(new Date(), new Date(), enterpriseId);
        for (Visitor visitor : visitors) {
            if (visitor.getRecordType() == 2) {
                tdyyCount += visitor.getPreCount();
            }
        }
        Double bfb = (double) tdyyCount / (double) yyCount;
        DecimalFormat df = new DecimalFormat("0.00%"); // 格式化为百分数
        String percentage = df.format(bfb); // 格式化结果


        mav.addObject("toDayTeamYyBfb", percentage); //当日团队散比
        mav.addObject("toDayTeamYyCount", map.get("tdyyCount"));
        return mav;
    }


    /**
     * 票务/活动/讲解
     */
    @RequestMapping("cq-ticketOrder-data")
    @ResponseBody
    public JsonResponse getActivityPrebookData(int enterpriseId, int informationType) {
        JsonResponse json = new JsonResponse();
        //今年
        String startDate = new SimpleDateFormat("yyyy").format(new Date()) + "-01-01";
        String todayDateStr = CalfDateUtil.localDateToStr(LocalDate.now());

        Map<String, Object> returnMap = new HashMap<>();
        if (informationType == 2) {
            //活动
            TicketOrder today = ticketOrderService.getOrderDataByDate(todayDateStr, todayDateStr, enterpriseId, informationType, 0);
            TicketOrder yesterDay = ticketOrderService.getOrderDataByDate(DateUtils.dateToStr(DateUtils.addDay(new Date(), -1)), DateUtils.dateToStr(DateUtils.addDay(new Date(), -1)), enterpriseId, informationType, 0);
            TicketOrder total = ticketOrderService.getOrderDataByDate(startDate, todayDateStr, enterpriseId, informationType, 0);

            returnMap.put("today", today.getTicketQuantity());
            returnMap.put("yesterDay", yesterDay.getTicketQuantity());
            returnMap.put("total", total.getTicketQuantity());
        } else if (informationType == 1) {
            //票务
            TicketOrder today = ticketOrderService.getOrderDataByDate(todayDateStr, todayDateStr, enterpriseId, informationType, 0);
            TicketOrder total = ticketOrderService.getOrderDataByDate(startDate, todayDateStr, enterpriseId, informationType, 0);

            returnMap.put("today", today.getTicketQuantity());
            returnMap.put("todayPrice", today.getPayPrice());
            returnMap.put("totalPrice", total.getPayPrice());
        } else if (informationType == 3) {
            //讲解
            TicketOrder today = ticketOrderService.getOrderDataByDate(todayDateStr, todayDateStr, enterpriseId, informationType, 0);
            TicketOrder total = ticketOrderService.getOrderDataByDate(startDate, todayDateStr, enterpriseId, informationType, 0);
            TicketOrder yesterDay = ticketOrderService.getOrderDataByDate(DateUtils.dateToStr(DateUtils.addDay(new Date(), -1)), DateUtils.dateToStr(DateUtils.addDay(new Date(), -1)), enterpriseId, informationType, 0);
            returnMap.put("today", today.getPeopleNum());
            returnMap.put("yesterDay", yesterDay.getPeopleNum());
            returnMap.put("total", total.getPeopleNum());
        }
        json.setReturnMap(returnMap);
        json.setSuccess(true);
        return json;
    }


    @RequestMapping("load-ticket-data")
    @ResponseBody
    public JsonResponse getMovieToday(int enterpriseId, int type, String startDate, String endDate) {
        JsonResponse json = new JsonResponse();
        Map<String, Object> map = ticketOrderService.getOrderStatisticsData(startDate, endDate, enterpriseId, 0, type);
        //int verificationCount = ticketOrderRelationService.loadVerificationCount(startDate, endDate, 0, enterpriseId, 4);
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("ticketQuantityToday", map.get("ticketQuantity"));
        //returnMap.put("verificationCountToday", verificationCount);
        json.setReturnMap(returnMap);
        return json;
    }


    /**
     * 当年进馆观众分布
     *
     * @param
     * @return
     */
    @RequestMapping("cq-getByArea-history")
    @ResponseBody
    public JsonResponse getSignCountByAreaHisttory(int enterpriseId) {
        JsonResponse json = new JsonResponse();
        String startDate1 = new SimpleDateFormat("yyyy").format(new Date()) + "-01-01";
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);
        Date time = cal.getTime();
        String endDate1 = new SimpleDateFormat("yyyy-MM-dd").format(time);
        List<Integer> enterpriseList = new ArrayList<>();
        enterpriseList.add(enterpriseId);
        List<PrebookStatisArea> list = statisAreaService.listByEnterpriseIdAndDate(startDate1, endDate1, enterpriseList, 1, 3);
        int totalCount = 0;
        for (PrebookStatisArea td : list) {
            if (td.getPrebookCount() > 0) {
                totalCount += td.getSigninVisitorCount();
            }
        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("table", list);
        returnMap.put("totalCount", totalCount);
        json.setReturnMap(returnMap);
        json.setSuccess(true);
        json.setList(list);
        return json;
    }

    /**
     * 月进馆人数
     *
     * @return
     */
    @RequestMapping("cq-signin-month-data")
    @ResponseBody
    public JsonResponse signinMonth(int enterpriseId) {
        JsonResponse jp = this.success();
        String startDate = new SimpleDateFormat("yyyy").format(new Date()) + "-01-01";

        List<PrebookStatisVisitor> list = statisVisitorService.listByEnterpriseIdAndYear(startDate, enterpriseId);
        List<String> dateList = new ArrayList<>();
        dateList.add("1月");
        dateList.add("2月");
        dateList.add("3月");
        dateList.add("4月");
        dateList.add("5月");
        dateList.add("6月");
        dateList.add("7月");
        dateList.add("8月");
        dateList.add("9月");
        dateList.add("10月");
        dateList.add("11月");
        dateList.add("12月");
        HashMap<String, PrebookStatisVisitor> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (PrebookStatisVisitor statisVisitor : list) {
                map.put(Integer.parseInt(statisVisitor.getPreviewMonth().substring(5)) + "月", statisVisitor);
            }
        }
        ArrayList<Integer> yList = new ArrayList<Integer>();
        for (String string : dateList) {
            int yValue = 0;
            if (map.get(string) != null) {
                yValue = map.get(string).getSigninVisitorCount();
            }
            yList.add(yValue);
        }
        jp.setResult(yList);
        jp.setList(dateList);
        return jp;
    }


    /**
     * 按年查进馆数量 && 按年的团队百分比
     *
     * @param enterpriseId
     * @return
     */
    @RequestMapping("cq-signin-year-data")
    @ResponseBody
    public JsonResponse getSigninYear(int enterpriseId) {
        JsonResponse json = new JsonResponse();
        //今年
        String startDate1 = new SimpleDateFormat("yyyy").format(new Date()) + "-01-01";
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);
        Date time = cal.getTime();
        String endDate1 = new SimpleDateFormat("yyyy-MM-dd").format(time);
        List<Integer> enterpriseList = new ArrayList<>();
        enterpriseList.add(enterpriseId);
        PrebookStatisVisitor countData = statisVisitorService.getCountByDate(startDate1, endDate1, 0, enterpriseList);
        //今年团队预约数
        int tdyyCount = 0;
       // List<Visitor> visitors = visitorService.listAllByStatisPre(DateUtils.strToDate(startDate1 + " 00:00:00"), new Date(), enterpriseId);
        tdyyCount = countData.getTeamSigninVisitorCount();
        Double bfb = (double) tdyyCount / (double) countData.getSigninVisitorCount();
        DecimalFormat df = new DecimalFormat("0.00%"); // 格式化为百分数
        String percentage = df.format(bfb); // 格式化结果


        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("signCountYear", countData.getSigninVisitorCount());
        returnMap.put("toYearTeamYyBfb", percentage);
        json.setReturnMap(returnMap);
        return json;
    }


//	/**
//	 * 今日进馆
// 	 * @param enterpriseId
//	 * @return
//	 */
//	@RequestMapping("cq-visitor-today-data")
//	@ResponseBody
//	public JsonResponse getTodayData(int enterpriseId) {
//		JsonResponse json = new JsonResponse();
//		//今日
//		String startDate1 = CalfDateUtil.localDateToStr(LocalDate.now());
//		String endDate1 = CalfDateUtil.localDateToStr(LocalDate.now());
//		int signCountToday = visitorService.getSignCountByDate(startDate1, endDate1, enterpriseId);
//		Map<String, Object> returnMap = new HashMap<>();
//		returnMap.put("data1", signCountToday);
//		json.setReturnMap(returnMap);
//		json.setSuccess(true);
//		return json;
//	}
//
//	/**
//	 * 昨日进馆
//	 * @param enterpriseId
//	 * @return
//	 */
//	@RequestMapping("cq-visitor-yesterday-data")
//	@ResponseBody
//	public JsonResponse getYesterdayData(int enterpriseId) {
//		JsonResponse json = new JsonResponse();
//		//昨日
//		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
//		Calendar calendar = Calendar.getInstance();
//		calendar.set(Calendar.HOUR_OF_DAY, -24);
//		String previewDate = dateFormat.format(calendar.getTime());
//		PrebookStatisVisitor statisVisitor = prebookStatisVisitorService.selectByEnterpriseIdAndPreviewDate(previewDate, enterpriseId);
//		Map<String, Object> returnMap = new HashMap<>();
//		if (statisVisitor == null){
//			returnMap.put("data1", 0);
//		}else {
//			returnMap.put("data1", statisVisitor.getSigninVisitorCount());
//		}
//		json.setReturnMap(returnMap);
//		json.setSuccess(true);
//		return json;
//	}


    /**
     * 时段进馆
     *
     * @param enterpriseId
     * @return
     */
    @RequestMapping("cq-getByPeriod-today")
    @ResponseBody
    public JsonResponse getByPeriodToday(int enterpriseId) {
        JsonResponse json = this.success();
        String startDate = CalfDateUtil.localDateToStr(LocalDate.now());
        String endDate = CalfDateUtil.localDateToStr(LocalDate.now());
        List<Visitor> preRecord = visitorService.getByPeriodPreHistory(startDate, endDate, enterpriseId);
        List<Visitor> signRecord = visitorService.getByPeriodSignHistory(startDate, endDate, enterpriseId);
        PrebookPeriod period = new PrebookPeriod();
        period.setEnterpriseId(enterpriseId);
        period.setStatus(1);
        List<PrebookPeriod> periodRecord = prebookService.listAll(period);
        NumberFormat numberFormat = NumberFormat.getInstance();
        numberFormat.setMaximumFractionDigits(1);
        List<Integer> preList = new ArrayList<>();
        List<Integer> signList = new ArrayList<>();
        List<String> periodList = new ArrayList<>();
        for (PrebookPeriod prebookPeriod : periodRecord) {
            int preCount = 0;
            int signCount = 0;
            if (CollectionUtils.isNotEmpty(preRecord)) {
                for (Visitor pre : preRecord) {
                    if (pre.getPeriodId() == prebookPeriod.getId()) {
                        preCount = pre.getPreCount();
                        continue;
                    }
                }
            }
            preList.add(preCount);
            if (CollectionUtils.isNotEmpty(signRecord)) {
                for (Visitor sign : signRecord) {
                    if (sign.getPeriodId() == prebookPeriod.getId()) {
                        signCount = sign.getSignCount();
                        continue;
                    }
                }
            }
            signList.add(signCount);
            periodList.add(prebookPeriod.getName());
        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("preList", preList);
        returnMap.put("signList", signList);
        returnMap.put("periodList", periodList);
        json.setReturnMap(returnMap);
        return json;
    }


    private Map<String, Integer> visitorAgeDataCount(List<Visitor> list) {
        Map<String, Integer> map = new HashedMap();
        int b0To30 = 0;
        int b30To39 = 0;
        int b40To49 = 0;
        int b50To59 = 0;
        int b60To99 = 0;
        int b100 = 0;
        int other = 0;
        int total = 0;
        for (Visitor visitor : list) {
            if (visitor.getIdentification() != null && visitor.getIdentification().length() == 18
                    && !"".equals(visitor.getIdentification())
                    && !"888888888888888888".equals(visitor.getIdentification())) {
                int age = StringUtils.IdNOToAge(visitor.getIdentification());
                if (age >= 0 && age <= 30) {
                    b0To30 += visitor.getSigninQuantity();
                } else if (age >= 30 && age <= 39) {
                    b30To39 += visitor.getSigninQuantity();
                } else if (age >= 40 && age <= 49) {
                    b40To49 += visitor.getSigninQuantity();
                } else if (age >= 50 && age <= 59) {
                    b50To59 += visitor.getSigninQuantity();
                } else if (age >= 60 && age <= 99) {
                    b60To99 += visitor.getSigninQuantity();
                } else if (age >= 100) {
                    b100 += visitor.getSigninQuantity();
                } else {
                    other += visitor.getSigninQuantity();
                }
            } else {
                other += visitor.getSigninQuantity();
            }
            total += visitor.getSigninQuantity();
        }
        map.put("b0To30", b0To30);
        map.put("b30To39", b30To39);
        map.put("b40To49", b40To49);
        map.put("b50To59", b50To59);
        map.put("b60To99", b60To99);
        map.put("b100", b100);
        map.put("other", other);
        map.put("total", total);
        return map;
    }

    private Map<String, Integer> visitorDataCount(List<Visitor> list) {
        Map<String, Integer> map = new HashedMap();
        int yyCount = 0; //预约
        int jgCount = 0; //进馆人数
        int rcCount = 0; //进馆人次
        for (Visitor visitor : list) {
            if (visitor.getPeriodId() != 0 && visitor.getStatus() != 3) {
                yyCount += Integer.parseInt(visitor.getCount());
            }
            if (visitor.getStatus() == 2) {
                if (visitor.getCount() != null && !"".equals(visitor.getCount())) {
                    jgCount += Integer.parseInt(visitor.getCount());
                }
                rcCount += visitor.getSigninQuantity();
            }
        }
        map.put("jgCount", jgCount);  //进馆人数
        map.put("yyCount", yyCount);  //预约数量
        map.put("rcCount", rcCount);  //进馆人次
        return map;
    }

    /**
     * @throws Exception
     * @Title: analysis
     * @Description: TODO
     * @Param @param timeOff值 (相对今天差值)
     * @Param @param beginTime
     * @Param @param endTime
     * @Param @return
     * @Return ModelAndView
     * @Throws
     */
    @RequestMapping("analysis-page")
    public ModelAndView analysis(@RequestParam(defaultValue = "1") int timeOff, String startDate, String endDate,
                                 @RequestParam(defaultValue = "0") int systemType, @RequestParam(defaultValue = "-1") int informationId,
                                 @RequestParam(defaultValue = "0") int informationType) throws Exception {

        ModelAndView mav = new ModelAndView("admin/analysis");
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
            if (information == null || information.getEnterpriseId() != employ.getEnterpriseId()) {
                return new ModelAndView("redirect:/auth/no-auth.htm");
            }
        }
        informationType = 0;
        mav.addObject("url3", "analysis/analysis-page");
        mav.addObject("url2", "analysis/analysishow");
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        mav.addObject("enterpriseId", employ.getEnterpriseId());
        List<TicketInformation> infoList = this.ticketInformationService.listByEnterpriseId(employ.getEnterpriseId(), informationType);
        mav.addObject("infoList", infoList);
        if (informationId == -1) {
            if (infoList != null && infoList.size() > 0) {
                informationId = infoList.get(0).getId();
            } else {
                informationId = 0;
            }
        }
        mav.addObject("informationId", informationId);
        mav.addObject("informationType", informationType);

        mav.addObject("startDate", startDate);
        mav.addObject("endDate", endDate);
        mav.addObject("timeOff", timeOff);
        List<TicketOrderRelation> ticketOrderRelationList = this.ticketOrderRelationService
                .loadVerificationType(startDate, endDate, informationId, employ.getEnterpriseId(), informationType);
        mav.addObject("ticketOrderRelationList", ticketOrderRelationList);
        return mav;
    }

    @RequestMapping("analysis-page-zl")
    public ModelAndView analysis1(@RequestParam(defaultValue = "1") int timeOff, String startDate, String endDate,
                                  @RequestParam(defaultValue = "0") int systemType, @RequestParam(defaultValue = "-1") int informationId,
                                  @RequestParam(defaultValue = "0") int informationType,@RequestParam(defaultValue = "0")int slaveEnterpriseId) throws Exception {
        informationType = 1;
        ModelAndView mav = new ModelAndView("admin/analysis");
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        List<Enterprise> enterpriseList = employEnterpriseAuthService.enterpriseListByEmployId(employ.getId());
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
            if (information == null) {
                return new ModelAndView("redirect:/auth/no-auth.htm");
            }
        }
        mav.addObject("url3", "analysis/analysis-page-zl");
        mav.addObject("url2", "analysis/analysishow-zl");
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        int enterpriseId =   employ.getEnterpriseId();
        if(slaveEnterpriseId>0){
            enterpriseId = slaveEnterpriseId;
        }
        mav.addObject("enterpriseId",enterpriseId );
        // 获取所有场馆的展览信息，用于前端级联筛选
        List<TicketInformation> infoList = this.ticketInformationService.listByEnterpriseId(0,
                informationType);
        if (informationId == -1) {
            if (infoList != null && infoList.size() > 0) {
                informationId = infoList.get(0).getId();
            } else {
                informationId = 0;
            }
        }
        mav.addObject("infoList", infoList);
        mav.addObject("informationId", informationId);
        mav.addObject("informationType", informationType);
        mav.addObject("enterpriseList", enterpriseList);
        mav.addObject("startDate", startDate);
        mav.addObject("endDate", endDate);
        mav.addObject("timeOff", timeOff);
        mav.addObject("slaveEnterpriseId", slaveEnterpriseId);

        List<TicketOrderRelation> ticketOrderRelationList = this.ticketOrderRelationService
                .loadVerificationType(startDate, endDate, informationId, enterpriseId, informationType);
        mav.addObject("ticketOrderRelationList", ticketOrderRelationList);

        return mav;
    }

    @RequestMapping("load-data")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse loadData(@RequestParam(defaultValue = "0") int informationType,
                                 @RequestParam(defaultValue = "0") int informationId, String startDate, String endDate,
                                 @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
        }
        Map<String, Object> map = ticketOrderService.getOrderStatisticsData(startDate, endDate,
                slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationId, informationType);
        JsonResponse json = this.success();
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("orderCount", map.get("orderCount"));
        returnMap.put("ticketQuantity", map.get("ticketQuantity"));
        returnMap.put("xiaoshou_amount", ticketOrderService.getAmountPerDay(startDate, endDate,
                slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationId, informationType));
        // 取票数量
        returnMap.put("ssTicketQuantity", ticketOrderService.getTiketTake(startDate, endDate, slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(),
                informationId, informationType));
        // 退票数量
        returnMap.put("refundTicketQuantity", ticketOrderRelationService.getTiketRefund(startDate, endDate,
                slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationId, informationType));

        // 已转赠数量
        int isGetCount = this.ticketOrderRelationService.getFreeTicketTransferCount(startDate, endDate,
                slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationId, informationType);
        int allFreeCount = this.ticketOrderRelationService.getAllFreeTicket(startDate, endDate,
                slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationId, informationType);
        int isUseCount = this.ticketOrderRelationService.getIsUseFreeTicket(startDate, endDate,
                slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationId, informationType);

        Map<String, Object> relationMap = new HashMap<>();
        relationMap.put("enterpriseId", slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId());
        relationMap.put("startDate", startDate);
        relationMap.put("endDate", endDate);
        relationMap.put("informationId", informationId);
        relationMap.put("informationType", informationType);
        relationMap.put("cardType", 5);
        TicketOrderRelation orderRelation = this.ticketOrderRelationService.getCountByUserRelationType(relationMap);


        returnMap.put("passportCount", orderRelation.getCountValue());
        returnMap.put("isGetCount", isGetCount);
        returnMap.put("allFreeCount", allFreeCount);
        returnMap.put("isUseCount", isUseCount);
        json.setReturnMap(returnMap);
        return json;
    }

    @RequestMapping("analysis-page-hd")
    public ModelAndView analysis2(@RequestParam(defaultValue = "1") int timeOff, String startDate, String endDate,
                                  @RequestParam(defaultValue = "0") int systemType, @RequestParam(defaultValue = "-1") int informationId,
                                  @RequestParam(defaultValue = "0") int informationType,
                                  @RequestParam(defaultValue = "0") int slaveEnterpriseId) throws Exception {
        informationType = 2;
        ModelAndView mav = new ModelAndView("admin/analysis_hd");
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        List<Enterprise> enterpriseList = employEnterpriseAuthService.enterpriseListByEmployId(employ.getId());
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
            if (information == null) {
                return new ModelAndView("redirect:/auth/no-auth.htm");
            }
        }
        mav.addObject("url3", "analysis/analysis-page-hd");
        mav.addObject("url2", "analysis/analysishow-hd");
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        //mav.addObject("enterpriseId", employ.getEnterpriseId());
        List<TicketInformation> infoList = ticketInformationService.listByEnterpriseId(0, informationType);
        if(infoList.size()>0){
            mav.addObject("infoList", infoList);
        }
//        if (informationId == -1) {
//            if (infoList != null && infoList.size() > 0) {
//                informationId = infoList.get(0).getId();
//            } else {
//                informationId = 0;
//            }
//        }
        mav.addObject("slaveEnterpriseId", slaveEnterpriseId);
        mav.addObject("enterpriseList", enterpriseList);
        mav.addObject("informationId", informationId);
        mav.addObject("informationType", informationType);
        mav.addObject("startDate", startDate);
        mav.addObject("endDate", endDate);
        mav.addObject("timeOff", timeOff);
        List<TicketOrderRelation> ticketOrderRelationList = this.ticketOrderRelationService
                .loadVerificationType(startDate, endDate, informationId, slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationType);
        mav.addObject("ticketOrderRelationList", ticketOrderRelationList);
        return mav;
    }

    @RequestMapping("analysis-page-movie")
    public ModelAndView analysisMovie(@RequestParam(defaultValue = "1") int timeOff, String startDate, String endDate,
                                      @RequestParam(defaultValue = "0") int systemType, @RequestParam(defaultValue = "-1") int informationId,
                                      @RequestParam(defaultValue = "0") int informationType) throws Exception {
        informationType = 4;
        ModelAndView mav = new ModelAndView("admin/analysis-movie");
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
            if (information == null || information.getEnterpriseId() != employ.getEnterpriseId()) {
                return new ModelAndView("redirect:/auth/no-auth.htm");
            }
        }
        mav.addObject("url3", "analysis/analysis-page-movie");
        mav.addObject("url2", "analysis/analysishow-movie");
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        mav.addObject("enterpriseId", employ.getEnterpriseId());
        List<TicketInformation> infoList = this.ticketInformationService.listByEnterpriseId(employ.getEnterpriseId(),
                informationType);
        mav.addObject("infoList", infoList);
        if (informationId == -1) {
            if (infoList != null && infoList.size() > 0) {
                informationId = infoList.get(0).getId();
            } else {
                informationId = 0;
            }
        }
        mav.addObject("informationId", informationId);
        mav.addObject("informationType", informationType);
        Map<String, Object> map = ticketOrderService.getOrderStatisticsData(startDate, endDate,
                employ.getEnterpriseId(), informationId, informationType);
        mav.addObject("orderCount", map.get("orderCount"));
        mav.addObject("ticketQuantity", map.get("ticketQuantity"));
        mav.addObject("areaOrderList", ticketOrderService.getOrderByAreaList(startDate, endDate,
                employ.getEnterpriseId(), informationId, informationType, 0));
        mav.addObject("xiaoshou_amount", ticketOrderService.getAmountPerDay(startDate, endDate,
                employ.getEnterpriseId(), informationId, informationType));
        mav.addObject("startDate", startDate);
        mav.addObject("endDate", endDate);
        mav.addObject("timeOff", timeOff);
        List<TicketOrder> pricewayList = ticketOrderService.getPriceByPayWay(startDate, endDate,
                employ.getEnterpriseId(), informationId, informationType);
        mav.addObject("pricewayList", pricewayList);

        // 取票数量
        mav.addObject("ssTicketQuantity", ticketOrderService.getTiketTake(startDate, endDate, employ.getEnterpriseId(),
                informationId, informationType));

        List<TicketOrderRelation> ticketOrderRelationList = this.ticketOrderRelationService
                .loadVerificationType(startDate, endDate, informationId, employ.getEnterpriseId(), informationType);
        mav.addObject("ticketOrderRelationList", ticketOrderRelationList);
        return mav;
    }

    @RequestMapping("analysis-page-jj")
    public ModelAndView analysis3(@RequestParam(defaultValue = "1") int timeOff, String startDate, String endDate,
                                  @RequestParam(defaultValue = "0") int systemType, @RequestParam(defaultValue = "-1") int informationId,
                                  @RequestParam(defaultValue = "0") int informationType) throws Exception {
        informationType = 3;
        ModelAndView mav = new ModelAndView("admin/analysis");
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
            if (information == null || information.getEnterpriseId() != employ.getEnterpriseId()) {
                return new ModelAndView("redirect:/auth/no-auth.htm");
            }
        }
        mav.addObject("url3", "analysis/analysis-page-jj");
        mav.addObject("url2", "analysis/analysishow-jj");
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        mav.addObject("enterpriseId", employ.getEnterpriseId());
        List<TicketInformation> infoList = this.ticketInformationService.listByEnterpriseId(employ.getEnterpriseId(),
                informationType);
        mav.addObject("infoList", infoList);
        if (informationId == -1) {
            if (infoList != null && infoList.size() > 0) {
                informationId = infoList.get(0).getId();
            } else {
                informationId = 0;
            }
        }
        mav.addObject("informationId", informationId);
        mav.addObject("informationType", informationType);
        mav.addObject("startDate", startDate);
        mav.addObject("endDate", endDate);
        mav.addObject("timeOff", timeOff);
        List<TicketOrderRelation> ticketOrderRelationList = this.ticketOrderRelationService
                .loadVerificationType(startDate, endDate, informationId, employ.getEnterpriseId(), informationType);
        mav.addObject("ticketOrderRelationList", ticketOrderRelationList);
        return mav;
    }

    @RequestMapping("load-analysis-statistics-data")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse loadOrderStatistics(String startDate, String endDate,
                                            @RequestParam(defaultValue = "0") int informationId,
                                            @RequestParam(defaultValue = "0") int informationType, @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }

        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
        }
        List<TicketOrder> chartList = this.ticketOrderService.listOrderStatistics(startDate, endDate,
                slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationId, informationType);
        Map<String, BigDecimal> orderMap = new HashMap<String, BigDecimal>();
        if (CollectionUtils.isNotEmpty(chartList)) {
            for (TicketOrder item : chartList) {
                orderMap.put(item.getChartDate(), item.getPayPrice());
            }
        }

        JsonResponse json = this.success();
        //销售金额
        List<Object[]> list = new ArrayList<Object[]>();

        LocalDate startLocalDate = CalfDateUtil.strToLocalDate(startDate);
        LocalDate endLocalDate = CalfDateUtil.strToLocalDate(endDate);

        while (endLocalDate.compareTo(startLocalDate) > -1) {
            Object[] arr = new Object[2];
            arr[0] = CalfDateUtil.localDateToStr(startLocalDate);
            arr[1] = orderMap.get(arr[0]) == null ? 0 : orderMap.get(arr[0]);
            list.add(arr);
            startLocalDate = startLocalDate.plusDays(1);

        }
        //支付方式
        List<TicketOrder> payWay = ticketOrderService.getOrderByPaywayList(startDate, endDate, slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(),
                informationId, informationType);
        String payWayResult = "";
        for (TicketOrder entry : payWay) {
            String payWayTypeName = entry.getPayWay() == 1 ? "'微信'"
                    : entry.getPayWay() == 2 ? "'支付宝'"
                    : entry.getPayWay() == 3 ? "'现金'"
                    : entry.getPayWay() == 4 ? "'余额'"
                    : entry.getPayWay() == 5 ? "'银联'"
                    : entry.getPayWay() == 6 ? "'pos机'"
                    : "'兑换券'";
            payWayResult += "{value:" + entry.getOrderCount() + "," + "name:" + payWayTypeName + "},";
        }
        if (payWayResult.length() > 0) {
            payWayResult = payWayResult.substring(0, payWayResult.length() - 1);
        }
        json.setExports(JSONArray.fromObject("[" + payWayResult + "]"));
        //购票途径
        List<TicketOrder> buyWay = ticketOrderService.getDataByBuyway(startDate, endDate, slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationType);
        String buyWayResult = "";
        for (TicketOrder entry : buyWay) {
            String buyWayTypeName = entry.getBuyWay() == 1 ? "'网络订票'"
                    : entry.getBuyWay() == 2 ? "'人工售票'"
                    : entry.getBuyWay() == 3 ? "'机器购票'"
                    : entry.getBuyWay() == 4 ? "'app购票'"
                    : "'其他'";
            buyWayResult += "{value:" + entry.getOrderCount() + "," + "name:" + buyWayTypeName + "},";
        }
        if (buyWayResult.length() > 0) {
            buyWayResult = buyWayResult.substring(0, buyWayResult.length() - 1);
        }
        // Object[] arr = new Object[2];
        // arr[0] = DateUtils.dateToStr(endDate, "yyyy-MM-dd");
        // arr[1] = orderMap.get(arr[0]) == null ? 0 : orderMap.get(arr[0]);
        // list.add(arr);
        json.setList(list);
        Map<String, Object> returnMap = new HashMap<>();

        // 退款金额
        List<TicketOrderRelation> chartRefundList = this.ticketOrderRelationService.listRefundStatistics(startDate,
                endDate, slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationId, informationType);
        Map<String, BigDecimal> refundMap = new HashMap<String, BigDecimal>();
        if (CollectionUtils.isNotEmpty(chartRefundList)) {
            for (TicketOrderRelation item : chartRefundList) {
                refundMap.put(item.getHourDate(), item.getTicketPrice());
            }
        }
        List<Object[]> refundlist = new ArrayList<Object[]>();

        LocalDate startRefundDate = CalfDateUtil.strToLocalDate(startDate);
        while (endLocalDate.compareTo(startRefundDate) > -1) {
            Object[] arr = new Object[2];
            arr[0] = CalfDateUtil.localDateToStr(startRefundDate);
            arr[1] = refundMap.get(arr[0]) == null ? 0 : refundMap.get(arr[0]);
            refundlist.add(arr);
            startRefundDate = startRefundDate.plusDays(1);

        }
        returnMap.put("refundlist", refundlist);

        //购票证件类型
        List<TicketUserRelation> pei1 = ticketOrderService.getTicketCardTypeData(startDate, endDate, slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(),
                informationId, informationType);
        String result1 = "";
        //证件类型0无证件1身份证5护照6台胞7港澳9社保卡
        for (TicketUserRelation tod : pei1) {
            String cardType = tod.getCardType() == 0 ? "'无证件'"
                    : tod.getCardType() == 1 ? "'身份证'"
                    : tod.getCardType() == 5 ? "'护照'"
                    : tod.getCardType() == 6 ? "'台胞'" : tod.getCardType() == 7 ? "'港澳'" : "'社保卡'";
            result1 += "{value:" + tod.getAge() + "," + "name:" + cardType + "},"; //暂时拿age字段存总数
        }
        if (result1.length() > 0) {
            result1 = result1.substring(0, result1.length() - 1);
        }
        json.setResult(JSONArray.fromObject("[" + result1 + "]"));
        if(informationType==1){
            //如果是展览就复用这个字段输出
            json.setResult(JSONArray.fromObject("[" + buyWayResult + "]"));
        }
        // 地域购票量
        List<OrderArea> map = ticketOrderService.getOrderByAreaList(startDate, endDate, slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(),
                informationId, informationType, 0);
        returnMap.put("areaOrderList", map);
        String result3 = "";
        int allAreaQuantity = 0;
        for (OrderArea tod : map) {
            result3 += "{name:'" + tod.getUserArea() + "'," + "value:" + tod.getTicketQuantity() + "},";
            allAreaQuantity += tod.getTicketQuantity();
        }
        if (result3.length() > 0) {
            result3 = result3.substring(0, result3.length() - 1);
        }
        json.setExports2(JSONArray.fromObject("[" + result3 + "]"));
        returnMap.put("allAreaQuantity", allAreaQuantity);

        // 地域核销量
        List<OrderArea> verificationMap = ticketOrderService.getOrderByAreaList(startDate, endDate,
                slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationId, informationType, 1);
        returnMap.put("areaVerificationList", verificationMap);
        String resultVerification = "";
        int allVQuantity = 0;
        for (OrderArea tod : verificationMap) {
            resultVerification += "{name:'" + tod.getUserArea() + "'," + "value:" + tod.getTicketQuantity() + "},";
            allVQuantity += tod.getTicketQuantity();
        }
        if (resultVerification.length() > 0) {
            resultVerification = resultVerification.substring(0, resultVerification.length() - 1);
        }
        returnMap.put("verificationObject", JSONArray.fromObject("[" + resultVerification + "]"));
        returnMap.put("allVQuantity", allVQuantity);

        // 男女购票
        Map<String, Object> map2 = ticketOrderService.getSexCount(startDate, endDate, slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(),
                informationId, informationType);
        String result4 = "";
        if (Integer.valueOf(map2.get("manCount").toString()) > 0) {
            result4 += "{name:'男',value:" + map2.get("manCount") + "},";
        }
        if (Integer.valueOf(map2.get("womenCount").toString()) > 0) {
            result4 += "{name:'女',value:" + map2.get("womenCount") + "},";
        }
        if (Integer.valueOf(map2.get("visitCount").toString()) > 0) {
            result4 += "{name:'未知',value:" + map2.get("visitCount") + "},";
        }
        if (result4.length() > 0) {
            result4 = result4.substring(0, result4.length() - 1);
        }
        json.setExports3(JSONArray.fromObject("[" + result4 + "]"));
        json.setReturnMap(returnMap);
        return json;
    }

    @RequestMapping("export-area-order-data")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public ResponseEntity<byte[]>  exportAreaOrderData (HttpServletRequest request,String startDate, String endDate,
                                            @RequestParam(defaultValue = "0") int informationId,
                                            @RequestParam(defaultValue = "0") int informationType, @RequestParam(defaultValue = "0") int slaveEnterpriseId)throws IOException {
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        int enterpriseId = 0;
        if (slaveEnterpriseId != 0) {
            enterpriseId = slaveEnterpriseId;
        } else {
            enterpriseId = employ.getEnterpriseId();
        }

        // 地域购票量
        List<OrderArea> orderList = ticketOrderService.getOrderByAreaList(startDate, endDate, enterpriseId, informationId, informationType, 0);
        //汇总总和
        int allAreaQuantity = 0;
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (int i = 0; i < orderList.size(); i++) {
            OrderArea order = orderList.get(i);
            allAreaQuantity += order.getTicketQuantity();
            Map<String, Object> map = new HashMap<>();
            map.put("id", i + 1); // 自动序号
            map.put("userArea", order.getUserArea());
            map.put("ticketQuantity", order.getTicketQuantity());
            dataList.add(map);
        } Map<String, Object> map = new HashMap<>();
        map.put("id", "总人数");
        map.put("ticketQuantity", allAreaQuantity);
        dataList.add(map);
        ClassLoader classLoader = getClass().getClassLoader();
        URL url = classLoader.getResource("excel/excel-template/area_order.xls");
        URL url2 = classLoader.getResource("excel/area_order_temp.xls");
        FileInputStream in = new FileInputStream(url.getPath());
        String templeName = RandomUtils.generateRandomNumber(5) + ".xls";
        new File(url2.getPath().replace(".xls", templeName));
        String templeFilePath = url2.getPath().replace(".xls", templeName);
        FileOutputStream out = new FileOutputStream(templeFilePath);
        try {
            //创建ExcelBean实例
            ExcelBean excelBean = new ExcelBean();
            Context context = new Context();
            excelBean.setListData(dataList);
            context.putVar("excelBean", excelBean);
            context.putVar("startDateStr", startDate);
            context.putVar("endDateStr", endDate);
            //填充Excel模板数据
            JxlsHelper.getInstance().processTemplate(in, out, context);
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        File f = null;
        f = new File(templeFilePath); // 将服务器上的文件下载下来
        HttpHeaders headers = new HttpHeaders();
        String fileDateName = "地域购票统计" + startDate + "至" + endDate + ".xls";
        String fileName = new String(fileDateName.getBytes("UTF-8"), "iso-8859-1");
        headers.setContentType(MediaType.valueOf("application/x-excel;charset=UTF-8"));
        headers.add("Content-Disposition", "attachment; filename=" + fileName);
        headers.add("Content-Length", String.valueOf(f.length()));
        byte[] b = FileUtils.readFileToByteArray(f);
        if (f.exists()) {
            f.delete();
        }
        return new ResponseEntity<byte[]>(b, headers, HttpStatus.OK);
    }




    @RequestMapping("load-analysis-statistics-data-pay")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse loadOrderStatisticsPay(String startDate, String endDate,
                                               @RequestParam(defaultValue = "0") int informationId,
                                               @RequestParam(defaultValue = "0") int informationType, @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        JsonResponse json = this.success();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
        }
        List<TicketOrder> list = ticketOrderService.getOrderByPaywayEveryDayList(startDate, endDate,
                slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationId, informationType);
        List<TicketOrder> wxList = new ArrayList<>();
        List<TicketOrder> alList = new ArrayList<>();
        List<TicketOrder> cashList = new ArrayList<>();
        List<TicketOrder> posList = new ArrayList<>();
        List<TicketOrder> ylList = new ArrayList<>();
        List<String> dateList = new ArrayList<String>();
        if (!list.isEmpty()) {
            for (TicketOrder ticketOrder : list) {
                String paidDateStr = CalfDateUtil.dateToStr(ticketOrder.getPaidTime(), "yyyy-MM-dd");
                if (!dateList.contains(paidDateStr)) {
                    dateList.add(paidDateStr);
                }
                if (ticketOrder.getPayWay() == 1) {
                    wxList.add(ticketOrder);
                } else if (ticketOrder.getPayWay() == 2) {
                    alList.add(ticketOrder);
                } else if (ticketOrder.getPayWay() == 3) {
                    cashList.add(ticketOrder);
                } else if (ticketOrder.getPayWay() == 6) {
                    posList.add(ticketOrder);
                } else if (ticketOrder.getPayWay() == 5) {
                    ylList.add(ticketOrder);
                }
            }
        }
        List<Integer> wx = new ArrayList<>();
        List<Integer> al = new ArrayList<>();
        List<Integer> cash = new ArrayList<>();
        List<Integer> pos = new ArrayList<>();
        List<Integer> yl = new ArrayList<>();
        for (String dateStr : dateList) {
            int wxCount = 0;
            int alCount = 0;
            int cashCount = 0;
            int posCount = 0;
            int ylCount = 0;
            for (TicketOrder ticketOrder : wxList) {
                String paidDateStr = CalfDateUtil.dateToStr(ticketOrder.getPaidTime(), "yyyy-MM-dd");
                if (paidDateStr.equals(dateStr)) {
                    wxCount = ticketOrder.getOrderCount();
                    break;
                }
            }
            wx.add(wxCount);
            for (TicketOrder ticketOrder : alList) {
                String paidDateStr = CalfDateUtil.dateToStr(ticketOrder.getPaidTime(), "yyyy-MM-dd");
                if (paidDateStr.equals(dateStr)) {
                    alCount = ticketOrder.getOrderCount();
                    break;
                }
            }
            al.add(alCount);
            for (TicketOrder ticketOrder : cashList) {
                String paidDateStr = CalfDateUtil.dateToStr(ticketOrder.getPaidTime(), "yyyy-MM-dd");
                if (paidDateStr.equals(dateStr)) {
                    cashCount = ticketOrder.getOrderCount();
                    break;
                }
            }
            cash.add(cashCount);
            for (TicketOrder ticketOrder : posList) {
                String paidDateStr = CalfDateUtil.dateToStr(ticketOrder.getPaidTime(), "yyyy-MM-dd");
                if (paidDateStr.equals(dateStr)) {
                    posCount = ticketOrder.getOrderCount();
                    break;
                }
            }
            pos.add(posCount);
            for (TicketOrder ticketOrder : ylList) {
                String paidDateStr = CalfDateUtil.dateToStr(ticketOrder.getPaidTime(), "yyyy-MM-dd");
                if (paidDateStr.equals(dateStr)) {
                    ylCount = ticketOrder.getOrderCount();
                    break;
                }
            }
            yl.add(ylCount);
        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("wxList", wx);
        returnMap.put("alList", al);
        returnMap.put("cashList", cash);
        returnMap.put("posList", pos);
        returnMap.put("ylList", yl);
        returnMap.put("dateList", dateList);
        json.setReturnMap(returnMap);
        //

        return json;
    }

    @RequestMapping("load-analysis-statistics-data-invoice")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse loadOrderStatisticsInvoice(String startDate, String endDate,
                                                   @RequestParam(defaultValue = "0") int informationId,
                                                   @RequestParam(defaultValue = "0") int informationType, @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        JsonResponse json = this.success();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
        }
        List<TicketOrder> list = ticketOrderService.getOrderByInvoice(startDate, endDate, slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(),
                informationId, informationType);
        List<TicketOrder> kpList = new ArrayList<>();
        List<TicketOrder> wkpList = new ArrayList<>();
        List<BigDecimal> priceList = new ArrayList<BigDecimal>();
        for (TicketOrder ticketOrder : list) {
            BigDecimal price = ticketOrder.getTicketPrice();
            if (!priceList.contains(price)) {
                priceList.add(price);
            }
            if (ticketOrder.getInvoiceStatus() == 1) {
                kpList.add(ticketOrder);
            } else if (ticketOrder.getPayWay() == 0) {
                wkpList.add(ticketOrder);
            }
        }
        List<Integer> kp = new ArrayList<>();
        List<Integer> wkp = new ArrayList<>();
        for (BigDecimal p : priceList) {
            int kpCount = 0;
            int wkpCount = 0;
            for (TicketOrder ticketOrder : kpList) {
                BigDecimal pricetmp = ticketOrder.getTicketPrice();
                if (pricetmp.equals(p)) {
                    kpCount = ticketOrder.getQuantity();
                    break;
                }
            }
            kp.add(kpCount);

            for (TicketOrder ticketOrder : wkpList) {
                BigDecimal pricetmp = ticketOrder.getTicketPrice();
                if (pricetmp.equals(p)) {
                    wkpCount = ticketOrder.getQuantity();
                    break;
                }
            }
            wkp.add(wkpCount);
        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("kpList", kp);
        returnMap.put("wkpList", wkp);
        returnMap.put("priceList", priceList);
        json.setReturnMap(returnMap);
        //

        return json;
    }

    @RequestMapping("load-analysis-statistics-data-age")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse loadOrderStatisticsAge(String startDate, String endDate,
                                               @RequestParam(defaultValue = "1") int buyWay, @RequestParam(defaultValue = "0") int informationId,
                                               @RequestParam(defaultValue = "0") int informationType, @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        JsonResponse json = this.success();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
        }
        List<TicketOrderRelation> list = ticketOrderRelationService.getOrderByList(startDate, endDate,
                slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationId, informationType);
        String[] strArr = {"0-4", "5-9", "10-14", "15-19", "20-24", "25-29", "30-34", "35-39", "40-44", "45-49", "50-54", "55-59", "60-64", "65-69", "70-74", "75-79", "80-84", "85-89", "90-94", "95-99",
                "未知"};
        if (buyWay == 3 || buyWay == 2) {
            strArr = new String[]{"0-4", "5-9", "10-14", "15-19", "20-24", "25-29", "30-34", "35-39", "40-44", "45-49", "50-54", "55-59", "60-64", "65-69", "70-74", "75-79", "80-84", "85-89", "90-94", "95-99"};
        }
        int b0To4 = 0;
        int b5To9 = 0;
        int b10To14 = 0;
        int b15To19 = 0;
        int b20To24 = 0;
        int b25To29 = 0;
        int b30To34 = 0;
        int b35To39 = 0;
        int b40To44 = 0;
        int b45To49 = 0;
        int b50To54 = 0;
        int b55To59 = 0;
        int b60To64 = 0;
        int b65To69 = 0;
        int b70To74 = 0;
        int b75To79 = 0;
        int b80To84 = 0;
        int b85To89 = 0;
        int b90To94 = 0;
        int b95To99 = 0;
        int other = 0;
        for (TicketOrderRelation ticketOrderRelation : list) {
            if (buyWay == 3 && ticketOrderRelation.getBuyWay() != 3) {
                continue;
            }
            if (ticketOrderRelation.getIdcard() != null && !"".equals(ticketOrderRelation.getIdcard())
                    && !"888888888888888888".equals(ticketOrderRelation.getIdcard())) {
                // 杭州观众
                if (buyWay == 2 && !StringUtils.isHangzhou(ticketOrderRelation.getIdcard())) {
                    continue;
                }
                int age = StringUtils.IdNOToAge(ticketOrderRelation.getIdcard());
                if (age >= 0 && age <= 4) {
                    b0To4++;
                } else if (age >= 5 && age <= 9) {
                    b5To9++;
                } else if (age >= 10 && age <= 14) {
                    b10To14++;
                } else if (age >= 15 && age <= 19) {
                    b15To19++;
                } else if (age >= 20 && age <= 24) {
                    b20To24++;
                } else if (age >= 25 && age <= 29) {
                    b25To29++;
                } else if (age >= 30 && age <= 34) {
                    b30To34++;
                } else if (age >= 35 && age <= 39) {
                    b35To39++;
                } else if (age >= 40 && age <= 44) {
                    b40To44++;
                } else if (age >= 45 && age <= 49) {
                    b45To49++;
                } else if (age >= 50 && age <= 54) {
                    b50To54++;
                } else if (age >= 55 && age <= 59) {
                    b55To59++;
                } else if (age >= 60 && age <= 64) {
                    b60To64++;
                } else if (age >= 65 && age <= 69) {
                    b65To69++;
                } else if (age >= 70 && age <= 74) {
                    b70To74++;
                } else if (age >= 75 && age <= 79) {
                    b75To79++;
                } else if (age >= 80 && age <= 84) {
                    b80To84++;
                } else if (age >= 85 && age <= 89) {
                    b85To89++;
                } else if (age >= 90 && age <= 94) {
                    b90To94++;
                } else if (age >= 95 && age <= 99) {
                    b95To99++;
                } else {
                    other++;
                }
            } else {
                other++;
            }
        }
        Integer[] intArr = {b0To4, b5To9, b10To14, b15To19, b20To24, b25To29, b30To34, b35To39, b40To44, b45To49, b50To54, b55To59, b60To64, b65To69, b70To74, b75To79, b80To84, b85To89, b90To94, b95To99,
                other};
        if (buyWay == 3 || buyWay == 2) {
            intArr = new Integer[]{b0To4, b5To9, b10To14, b15To19, b20To24, b25To29, b30To34, b35To39, b40To44, b45To49, b50To54, b55To59, b60To64, b65To69, b70To74, b75To79, b80To84, b85To89, b90To94, b95To99,
                    other};
        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("xAlias", strArr);
        returnMap.put("yAlias", intArr);
        json.setReturnMap(returnMap);
        return json;
    }

    @RequestMapping("load-analysis-statistics-data-age-one")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse loadOrderStatisticsAgeOne(String startDate, String endDate, String age,
                                                  @RequestParam(defaultValue = "1") int buyWay, @RequestParam(defaultValue = "0") int informationId,
                                                  @RequestParam(defaultValue = "0") int informationType, @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        JsonResponse json = this.success();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
        }
        List<TicketOrderRelation> list = ticketOrderRelationService.getAgeOneByList(startDate, endDate,
                slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationId, informationType);
        if ("其他".equals(age)) {
            return json;
        }
        String[] ageArr = age.split("-");
        int begin = Integer.parseInt(ageArr[0]);
        int end = Integer.parseInt(ageArr[1]);
        Map<String, Integer> map = new HashMap<String, Integer>();
        for (int i = begin; i <= end; i++) {
            map.put(i + "", 0);
        }
        int count = 0;
        for (TicketOrderRelation ticketOrderRelation : list) {
            if (buyWay == 3 && ticketOrderRelation.getBuyWay() != 3) {
                continue;
            }
            if (!"".equals(ticketOrderRelation.getIdcard())
                    && !"888888888888888888".equals(ticketOrderRelation.getIdcard())) {
                // 杭州观众
                if (buyWay == 2 && !StringUtils.isHangzhou(ticketOrderRelation.getIdcard())) {
                    continue;
                }
                int age2 = StringUtils.IdNOToAge(ticketOrderRelation.getIdcard());
                if (begin <= age2 && age2 <= end) {
                    map.put(age2 + "", map.get(age2 + "") + 1);
                    count++;
                }
            }
            map.put("合计", count);
        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("yAlias", map);
        json.setReturnMap(returnMap);
        return json;
    }

    @RequestMapping("load-analysis-statistics-data-fullAndDiscount")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse loadOrderStatisticsFullAndDiscount(Date startDate, Date endDate,
                                                           @RequestParam(defaultValue = "0") int informationId,
                                                           @RequestParam(defaultValue = "0") int informationType, @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToDate(LocalDate.now());
            endDate = startDate;
        }
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        JsonResponse json = this.success();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
        }
        List<TicketOrderRelation> list = ticketOrderRelationService.getOrderByFullAndDiscountList(startDate, endDate,
                slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationId, informationType);
        String result = "";
        String export = "";
        for (TicketOrderRelation ticketOrderRelation : list) {
            String name = ticketOrderRelation.getTicketType() == 1 ? "全价票" : "优惠票";
            result += "{'name':'" + name + "(" + ticketOrderRelation.getTicketPrice() + "元)','value':'"
                    + ticketOrderRelation.getCountValue() + "'},";
            export += "'" + name + "(" + ticketOrderRelation.getTicketPrice() + "元)',";
        }
        if (result.length() > 0) {
            result = result.substring(0, result.length() - 1);
            export = export.substring(0, export.length() - 1);
        }
        json.setResult(JSONArray.fromObject("[" + result + "]"));
        json.setExports(JSONArray.fromObject("[" + export + "]"));
        return json;
    }

    @RequestMapping("load-verification-day")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse loadVerificationDay(Date startDate, Date endDate,
                                            @RequestParam(defaultValue = "0") int informationId,
                                            @RequestParam(defaultValue = "0") int informationType, @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        JsonResponse jp = this.success();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToDate(LocalDate.now());
            endDate = startDate;
        }
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
        }
        List<TicketOrderRelation> ticketOrderRelationList = this.ticketOrderRelationService
                .loadVerificationDay(startDate, endDate, informationId, slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationType);
        List<String> dateList = new ArrayList<>();

        LocalDate startLocalDate = CalfDateUtil.dateToLocalDate(startDate);
        LocalDate endLocalDate = CalfDateUtil.dateToLocalDate(endDate);

        while (endLocalDate.compareTo(startLocalDate) > -1) {
            String str = CalfDateUtil.localDateToStr(startLocalDate);
            dateList.add(str);
            startLocalDate = startLocalDate.plusDays(1);

        }

        HashMap<String, TicketOrderRelation> map = new HashMap<>();
        for (TicketOrderRelation ticketOrderRelation : ticketOrderRelationList) {
            map.put(DateUtils.dateToStr(ticketOrderRelation.getUpdateTime(), "yyyy-MM-dd"), ticketOrderRelation);
        }
        ArrayList<Integer> yList = new ArrayList<Integer>();
        for (String string : dateList) {
            if (map.get(string) == null) {
                yList.add(0);
            } else {
                yList.add(map.get(string).getQuantity());
            }
        }
        jp.setResult(yList);
        jp.setList(dateList);
        return jp;
    }

    @RequestMapping("load-analysis-statistics-detail-data")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse loadOrderStatisticsDetail(String chartDate, @RequestParam(defaultValue = "0") int informationId,
                                                  @RequestParam(defaultValue = "0") int informationType, @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        JsonResponse json = this.success();
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
        }
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> map1 = ticketOrderService.getOrderStatisticsData(chartDate, chartDate,
                slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationId, informationType);
        map.put("orderCount", map1.get("orderCount"));
        map.put("ticketQuantity", map1.get("ticketQuantity"));
        Map<String, Object> map2 = this.ticketOrderService.getChartAmountPerDay(chartDate, slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(),
                informationId, informationType);
        map.put("xiaoshou_amount", map2.get("xiaoshou_amount") == null ?"0":map2.get("xiaoshou_amount") );
        json.setResult(map);
        return json;
    }

    @RequestMapping("analysishow")
    public ModelAndView analysishow(@RequestParam(defaultValue = "0") int informationId,
                                    @RequestParam(defaultValue = "0") int informationType) {
        ModelAndView mav = new ModelAndView("admin/analysishow");
        // 查看是否有订单
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
            if (information == null || information.getEnterpriseId() != employ.getEnterpriseId()) {
                return new ModelAndView("redirect:/auth/no-auth.htm");
            }
        }
        List<TicketInformation> infoList = this.ticketInformationService.listByEnterpriseId(employ.getEnterpriseId(),
                informationType);
        if (!infoList.isEmpty()) {
            informationId = infoList.get(0).getId();
        }
        mav.addObject("infoList", infoList);
        mav.addObject("informationId", informationId);
        mav.addObject("informationType", informationType);
        mav.addObject("enterpriseId", employ.getEnterpriseId());
        mav.addObject("orderCount",
                ticketOrderService.getOrderCount(employ.getEnterpriseId(), informationId, informationType));
        // 企业
        List<Enterprise> entetpriseList = ticketEnterpriseService.listAllEnterprise();
        mav.addObject("entetpriseList", entetpriseList);
        return mav;
    }

    @RequestMapping("analysishow-zl")
    public ModelAndView analysishow1(@RequestParam(defaultValue = "0") int informationId,
                                     @RequestParam(defaultValue = "0") int informationType) {
        ModelAndView mav = new ModelAndView("admin/analysishow");
        // 查看是否有订单
        informationType = 1;
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
            if (information == null || information.getEnterpriseId() != employ.getEnterpriseId()) {
                return new ModelAndView("redirect:/auth/no-auth.htm");
            }
        }
        List<TicketInformation> infoList = this.ticketInformationService.listByEnterpriseId(employ.getEnterpriseId(),
                informationType);
        if (!infoList.isEmpty()) {
            informationId = infoList.get(0).getId();
        }
        mav.addObject("infoList", infoList);
        mav.addObject("informationId", informationId);
        mav.addObject("informationType", informationType);
        mav.addObject("enterpriseId", employ.getEnterpriseId());
        mav.addObject("orderCount",
                ticketOrderService.getOrderCount(employ.getEnterpriseId(), informationId, informationType));

        // 企业
        List<Enterprise> entetpriseList = ticketEnterpriseService.listAllEnterprise();
        mav.addObject("entetpriseList", entetpriseList);
        return mav;
    }

    @RequestMapping("analysishow-hd")
    public ModelAndView analysishow2(@RequestParam(defaultValue = "0") int informationId,
                                     @RequestParam(defaultValue = "0") int informationType) {
        ModelAndView mav = new ModelAndView("admin/analysishow");
        // 查看是否有订单
        informationType = 2;
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
            if (information == null || information.getEnterpriseId() != employ.getEnterpriseId()) {
                return new ModelAndView("redirect:/auth/no-auth.htm");
            }
        }
        List<TicketInformation> infoList = this.ticketInformationService.listByEnterpriseId(employ.getEnterpriseId(),
                informationType);
        if (!infoList.isEmpty()) {
            informationId = infoList.get(0).getId();
        }
        mav.addObject("infoList", infoList);
        mav.addObject("informationId", informationId);
        mav.addObject("informationType", informationType);
        mav.addObject("enterpriseId", employ.getEnterpriseId());
        mav.addObject("orderCount",
                ticketOrderService.getOrderCount(employ.getEnterpriseId(), informationId, informationType));
        // 企业
        List<Enterprise> entetpriseList = ticketEnterpriseService.listAllEnterprise();
        mav.addObject("entetpriseList", entetpriseList);
        return mav;
    }

    @RequestMapping("analysishow-movie")
    public ModelAndView analysishowMovie(@RequestParam(defaultValue = "0") int informationId,
                                         @RequestParam(defaultValue = "0") int informationType) {
        ModelAndView mav = new ModelAndView("admin/analysishow");
        // 查看是否有订单
        informationType = 4;
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
            if (information == null || information.getEnterpriseId() != employ.getEnterpriseId()) {
                return new ModelAndView("redirect:/auth/no-auth.htm");
            }
        }
        List<TicketInformation> infoList = this.ticketInformationService.listByEnterpriseId(employ.getEnterpriseId(),
                informationType);
        if (!infoList.isEmpty()) {
            informationId = infoList.get(0).getId();
        }
        mav.addObject("infoList", infoList);
        mav.addObject("informationId", informationId);
        mav.addObject("informationType", informationType);
        mav.addObject("enterpriseId", employ.getEnterpriseId());
        mav.addObject("orderCount",
                ticketOrderService.getOrderCount(employ.getEnterpriseId(), informationId, informationType));
        // 企业
        List<Enterprise> entetpriseList = ticketEnterpriseService.listAllEnterprise();
        mav.addObject("entetpriseList", entetpriseList);
        return mav;
    }

    @RequestMapping("analysishow-jj")
    public ModelAndView analysishow3(@RequestParam(defaultValue = "0") int informationId,
                                     @RequestParam(defaultValue = "0") int informationType) {
        ModelAndView mav = new ModelAndView("admin/analysishow");
        // 查看是否有订单
        informationType = 3;
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
            if (information == null || information.getEnterpriseId() != employ.getEnterpriseId()) {
                return new ModelAndView("redirect:/auth/no-auth.htm");
            }
        }
        List<TicketInformation> infoList = this.ticketInformationService.listByEnterpriseId(employ.getEnterpriseId(),
                informationType);
        if (!infoList.isEmpty()) {
            informationId = infoList.get(0).getId();
        }
        mav.addObject("infoList", infoList);
        mav.addObject("informationId", informationId);
        mav.addObject("informationType", informationType);
        mav.addObject("enterpriseId", employ.getEnterpriseId());
        mav.addObject("orderCount",
                ticketOrderService.getOrderCount(employ.getEnterpriseId(), informationId, informationType));
        // 企业
        List<Enterprise> entetpriseList = ticketEnterpriseService.listAllEnterprise();
        mav.addObject("entetpriseList", entetpriseList);
        return mav;
    }

    @RequestMapping("load-analysishow-statistics-detail-data")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse loadAnalysishowDetail(String chartDate, @RequestParam(defaultValue = "0") int informationId,
                                              @RequestParam(defaultValue = "0") int informationType) {
        JsonResponse json = this.success();
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
        }
        if (informationId == 0) {
            List<TicketInformation> infoList = this.ticketInformationService
                    .listByEnterpriseId(employ.getEnterpriseId(), informationType);
            if (!infoList.isEmpty()) {
                informationId = infoList.get(0).getId();
            }
        }
        Map<String, Object> maps = new HashMap<>();

        // 今日销售额和购票量

        LocalDate localDateNow = LocalDate.now();
        String startDate = CalfDateUtil.localDateToStr(localDateNow);
        String endDate = startDate;

        Map<String, Object> map = ticketOrderService.getOrderStatisticsData(startDate, endDate,
                employ.getEnterpriseId(), informationId, informationType);
        maps.put("ticketQuantity", map.get("ticketQuantity"));
        maps.put("today_amount", ticketOrderService.getAmountPerDay(startDate, endDate, employ.getEnterpriseId(),
                informationId, informationType));
        // 昨日销售额

        LocalDate yesterdayStartLocalDate = localDateNow.plusDays(-1);
        startDate = CalfDateUtil.localDateToStr(yesterdayStartLocalDate);
        endDate = startDate;

        maps.put("yestoday_amount", ticketOrderService.getAmountPerDay(startDate, endDate, employ.getEnterpriseId(),
                informationId, informationType));
        // 累计销售额
        maps.put("total_amount",
                ticketOrderService
                        .getOrderStatisticsData(null, null, employ.getEnterpriseId(), informationId, informationType)
                        .get("totalAmount"));
        // 查看是否有订单
        maps.put("orderCount",
                ticketOrderService.getOrderCount(employ.getEnterpriseId(), informationId, informationType));
        // 出票时间
        maps.put("xianjin", ticketOrderService.getPrintTimeByWay(2));
        maps.put("jiqi", ticketOrderService.getPrintTimeByWay(3));
        json.setResult(maps);
        return json;
    }

    /**
     * 支付方式和购票方式
     *
     * @Title: loadPeiStatistics
     * @Description: TODO
     * @Param @param startDate
     * @Param @param endDate
     * @Param @return
     * @Return JsonResponse
     * @Throws
     */
    @RequestMapping("load-analysisshow-statistics-data")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse loadAnalysisshow(@RequestParam(defaultValue = "0") int informationId,
                                         @RequestParam(defaultValue = "0") int informationType) {

        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
        }
        if (informationId == 0) {
            List<TicketInformation> infoList = this.ticketInformationService
                    .listByEnterpriseId(employ.getEnterpriseId(), informationType);
            if (!infoList.isEmpty()) {
                informationId = infoList.get(0).getId();
            }
        }
        JsonResponse json = this.success();
        // 支付方式
        List<TicketOrder> pei1 = ticketOrderService.getOrderByPaywayNoDate(employ.getEnterpriseId(), informationId,
                informationType);
        String result1 = "";
        String result3 = "";
        for (TicketOrder tod : pei1) {
            String payway = tod.getPayWay() == 1 ? "'微信'"
                    : tod.getPayWay() == 2 ? "'支付宝'"
                    : tod.getPayWay() == 3 ? "'现金'"
                    : tod.getPayWay() == 4 ? "'余额'" : tod.getPayWay() == 5 ? "'银联'" : "'刷pos机'";
            payway = payway.replace("'", "");
            result1 += "{value:" + tod.getOrderCount() + "," + "name:" + "'" + payway + " " + tod.getAreaPercent() + "%"
                    + "'" + "},";
            result3 += "'" + payway + " " + tod.getAreaPercent() + "%" + "'" + ",";
        }
        if (result1.length() > 0) {
            result1 = result1.substring(0, result1.length() - 1);
            result3 = result3.substring(0, result3.length() - 1);
        }
        json.setResult(JSONArray.fromObject("[" + result1 + "]"));
        json.setExports2(JSONArray.fromObject("[" + result3 + "]"));
        // 购票方式
        List<TicketOrder> pei2 = ticketOrderService.getOrderStatisticsWayNoDate(employ.getEnterpriseId(), informationId,
                informationType);
        String result2 = "";
        String result4 = "";
        for (TicketOrder tod : pei2) {
            String buyway = tod.getBuyWay() == 1 ? "'网络订票'"
                    : tod.getBuyWay() == 2 ? "'人工售票'" : tod.getBuyWay() == 3 ? "'机器购票'" : "'app购票'";
            buyway = buyway.replace("'", "");
            result2 += "{value:" + tod.getOrderCount() + "," + "name:" + "'" + buyway + " " + tod.getAreaPercent() + "%"
                    + "'" + "},";
            result4 += "'" + buyway + " " + tod.getAreaPercent() + "%" + "'" + ",";
        }
        if (result2.length() > 0) {
            result2 = result2.substring(0, result2.length() - 1);
            result4 = result4.substring(0, result4.length() - 1);
        }
        json.setExports(JSONArray.fromObject("[" + result2 + "]"));
        json.setExports3(JSONArray.fromObject("[" + result4 + "]"));
        return json;
    }

    // 每个时间段的出票量，默认展示今日和昨日数据；如选择日期，则显示该日期中，每个时间段的出票量
    @RequestMapping("loadChartData_sjd_gpl")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse loadChartData_sjd_gpl(String startDate, String endDate,
                                              @RequestParam(defaultValue = "0") int informationId,
                                              @RequestParam(defaultValue = "0") int informationType, @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        JsonResponse json = this.success();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
        }
        List<ChartBean> list = this.ticketOrderService.loadChartData_sjd_gpl(startDate, endDate,
                slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationId, informationType);
        Map<Object, ChartBean> map = com.taoart.common.utils.CollectionUtils.toMap(list, "xValue");
        List<ChartBean> retList = new ArrayList<ChartBean>();
        for (int i = 0; i < 24; i++) {
            if (null != map.get(new Integer(i))) {
                retList.add(map.get(new Integer(i)));
            } else {
                retList.add(new ChartBean());
            }
        }
        json.setList(retList);
        return json;
    }

    //
    @RequestMapping("loadChartData_gps_ddl")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse loadChartData_gps_ddl(String startDate, String endDate,
                                              @RequestParam(defaultValue = "0") int informationId,
                                              @RequestParam(defaultValue = "0") int informationType, @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        JsonResponse json = this.success();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
        }
        List<ChartBean> list = this.ticketOrderService.loadChartData_gps_ddl(startDate, endDate,
                slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationId, informationType);
        Map<Object, ChartBean> map = com.taoart.common.utils.CollectionUtils.toMap(list, "xValue");
        List<ChartBean> retList = new ArrayList<ChartBean>();
        for (int i = 1; i < 16; i++) {
            if (null != map.get(new Integer(i))) {
                retList.add(map.get(new Integer(i)));
            } else {
                retList.add(new ChartBean());
            }
        }

        int sum16 = 0;
        for (int i = 0; i < list.size(); i++) {
            ChartBean item = list.get(i);
            int v = item.getxValue();
            if (v > 15) {
                sum16 = sum16 + item.getyValue();
            }
        }
        ChartBean b16 = new ChartBean();
        b16.setyValue(sum16);
        retList.add(b16);
        json.setList(retList);
        return json;
    }

    // 天气
    @RequestMapping("loadChartData_weather_ddl")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse loadChartData_weather_ddl(String startDate, String endDate,
                                                  @RequestParam(defaultValue = "0") int informationId,
                                                  @RequestParam(defaultValue = "0") int informationType, @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        JsonResponse json = this.success();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
        }
        List<Weather> wlist = new ArrayList<>();

        wlist = weatherService.getListByHour(startDate, endDate);
        List<Weather> hourList = new ArrayList<Weather>();
        for (Weather weather : wlist) {
            if (weather.getTime().indexOf('-') == -1) {
                hourList.add(weather);
            }
        }

        List<TicketOrder> orderList = ticketOrderService.getListByHour(startDate, endDate, informationId,
                slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationType);

        // 排序
        // 必须是Comparator中的compare方法和Collections.sort方法配合使用才管用
        MyComparator mc = new MyComparator();
        Collections.sort(hourList, mc);
        // 时间list
        List<String> dateList = new ArrayList<String>();
        for (Weather wea : hourList) {
            dateList.add(wea.getTime() + "时");
        }
        List<Integer> ticketQuantityList = new ArrayList<>();
        int maxQuantity = 0;
        for (String str : dateList) {
            TicketOrder o = null;
            boolean flag = false;
            for (TicketOrder order : orderList) {
                if ((order.getHour() + "时").equals(str)) {
                    o = order;
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                o = new TicketOrder();
                o.setTakeTicketQuantity(0);
            }
            if (maxQuantity < o.getTakeTicketQuantity()) {
                maxQuantity = o.getTakeTicketQuantity();
            }
            ticketQuantityList.add(o.getTakeTicketQuantity());
        }
        List<Integer> listint = new ArrayList<>();
        for (Weather wea : hourList) {
            listint.add(Integer.valueOf(wea.getTemperature()));
        }
        List<String> listString = new ArrayList<>();
        for (Weather wea : wlist) {
            listString.add(wea.getText() + "时");
        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("listint", listint);
        returnMap.put("listString", listString);
        returnMap.put("dateList", dateList);
        returnMap.put("wlist", wlist);
        returnMap.put("ticketQuantityList", ticketQuantityList);
        json.setReturnMap(returnMap);
        if (maxQuantity < 10) {
            maxQuantity = maxQuantity + 2;
        } else if (maxQuantity < 100) {
            maxQuantity = maxQuantity + 20;
        } else if (maxQuantity < 1000) {
            maxQuantity = maxQuantity + 200;
        } else if (maxQuantity < 10000) {
            maxQuantity = maxQuantity + 2000;
        }
        json.setResult(maxQuantity);
        return json;
    }

    // 天气
    @RequestMapping("loadChartData_weather_ddl_day")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse loadChartData_weather_ddl_day(String startDate, String endDate,
                                                      @RequestParam(defaultValue = "0") int informationId,
                                                      @RequestParam(defaultValue = "0") int informationType, @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        JsonResponse json = this.success();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
        }
        List<Weather> wlist = new ArrayList<>();
        Date weatherStartDate = CalfDateUtil.strToDate(startDate);
        Date weatherEndDate = CalfDateUtil.strToDate(endDate);
        wlist = weatherService.getListByDay(weatherStartDate, weatherEndDate);
        List<String> listString = new ArrayList<>();

        List<TicketOrder> orderList = ticketOrderService.getListByDay(startDate, endDate, informationId,
                slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationType);

        // 排序
        // 必须是Comparator中的compare方法和Collections.sort方法配合使用才管用
        // MyComparator mc = new MyComparator();
        // Collections.sort(hourList, mc);
        // 时间list
        List<String> dateList = new ArrayList<String>();

        LocalDate startLocalDate = CalfDateUtil.strToLocalDate(startDate);
        LocalDate endLocalDate = CalfDateUtil.strToLocalDate(endDate);

        while (endLocalDate.compareTo(startLocalDate) > -1) {
            String dateStr = CalfDateUtil.localDateToStr(startLocalDate);
            startLocalDate = startLocalDate.plusDays(1);
            dateList.add(dateStr);
        }
        List<Integer> ticketQuantityList = new ArrayList<>();
        int maxQuantity = 0;

        for (String str : dateList) {
            TicketOrder o = null;
            boolean flag = false;
            for (TicketOrder order : orderList) {
                String printDateStr = CalfDateUtil.dateToStr(order.getPrintTicketTime(), "yyyy-MM-dd");
                if (printDateStr.equals(str)) {
                    o = order;
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                o = new TicketOrder();
                o.setTakeTicketQuantity(0);
            }
            if (maxQuantity < o.getTakeTicketQuantity()) {
                maxQuantity = o.getTakeTicketQuantity();
            }
            ticketQuantityList.add(o.getTakeTicketQuantity());

            boolean tempFlag = false;
            for (Weather weather : wlist) {
                if (weather.getTime().equals(str)) {
                    tempFlag = true;
                    listString.add(weather.getTemperature());
                    break;
                }
            }
            if (!tempFlag) {
                listString.add("0");
            }
        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("listint", listString);
        // returnMap.put("listString", listString);
        returnMap.put("dateList", dateList);
        returnMap.put("ticketQuantityList", ticketQuantityList);
        json.setReturnMap(returnMap);
        if (maxQuantity < 10) {
            maxQuantity = maxQuantity + 2;
        } else if (maxQuantity < 100) {
            maxQuantity = maxQuantity + 20;
        } else if (maxQuantity < 1000) {
            maxQuantity = maxQuantity + 200;
        } else if (maxQuantity < 10000) {
            maxQuantity = maxQuantity + 2000;
        }
        json.setResult(maxQuantity);
        return json;
    }

    /**
     * 支付方式销售金额
     *
     * @Title: loadPeiStatistics
     * @Description: TODO
     * @Param @param startDate
     * @Param @param endDate
     * @Param @return
     * @Return JsonResponse
     * @Throws
     */
    @RequestMapping("load-priceWay-statistics-data")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse priceWay(String startDate, String endDate, @RequestParam(defaultValue = "0") int informationId,
                                 @RequestParam(defaultValue = "0") int informationType, @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }

        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        JsonResponse json = this.success();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
        }
        // 支付方式
        List<TicketOrder> pei1 = ticketOrderService.getPriceByPayWay(startDate, endDate, slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(),
                informationId, informationType);
        json.setExports(pei1);
        String result1 = "";
        for (TicketOrder tod : pei1) {
            String payway = tod.getPayWay() == 1 ? "'微信'"
                    : tod.getPayWay() == 2 ? "'支付宝'"
                    : tod.getPayWay() == 3 ? "'现金'"
                    : tod.getPayWay() == 4 ? "'余额'"
                    : tod.getPayWay() == 5 ? "'银联'"
                    : tod.getPayWay() == 8 ? "'携程'" : "'刷pos机'";
            result1 += "{value:" + tod.getPayPrice() + "," + "name:" + payway + "},";
        }
        if (result1.length() > 0) {
            result1 = result1.substring(0, result1.length() - 1);
        }
        json.setResult(JSONArray.fromObject("[" + result1 + "]"));
        return json;
    }

    @RequestMapping("load-nation-data")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getNationData(@RequestParam(defaultValue = "0") int informationType,
                                      @RequestParam(defaultValue = "0") int informationId, String startDate, String endDate, @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
        }
        List<TicketUserData> list = this.ticketUserDataService.getNationData(slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationId,
                startDate, endDate, informationType);
        JsonResponse json = this.success();
        json.setList(list);
        int count = 0;
        for (TicketUserData u : list) {
            count += u.getQuantity();
        }
        json.setResult(count);
        return json;
    }


    @RequestMapping("/visitechars")
    public ModelAndView getared(@RequestParam(defaultValue = "2") int timeOff, Date startDate, Date endDate,
                                @RequestParam(defaultValue = "0") int slaveEnterpriseId, @RequestParam(defaultValue = "1") int chooseType) {
        ModelAndView mav = new ModelAndView("visitechars");
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        int enterpriseId = employ.getEnterpriseId();
        List<Enterprise> enterpriseList = employEnterpriseAuthService.enterpriseListByEmployId(employ.getId());
        if (null == startDate || null == endDate) {
            startDate = new Date();
            endDate = startDate;
        }
        int cuEnterpriseId;
        if (CollectionUtils.isNotEmpty(enterpriseList)) {
            cuEnterpriseId = enterpriseList.get(0).getId();
        } else {
            cuEnterpriseId = enterpriseId;
        }
        //当天核销人数
        int dailyVerification = visitorService.getCountOfDailyVerification(slaveEnterpriseId > 0 ? slaveEnterpriseId : enterpriseId);
        //当天预约人数
        List<Visitor> visitors = visitorService.listAllByStatisPre(startDate, endDate, cuEnterpriseId);
        //
        int personal = 0;
        int team = 0;
        int family = 0;
        for (Visitor v : visitors) {
            if (v.getRecordType() == 1) {
                personal = v.getPreCount();
            } else if (v.getRecordType() == 2) {
                team = v.getPreCount();
            } else if (v.getRecordType() == 3) {
                family = v.getPreCount();
            }
        }
        int preAll = personal + team + family;
        PrebookInfoConfig p = prebookInfoConfigService.selectByEnterpriseId(cuEnterpriseId);
        if (p != null && p.getSigninType() == 2) {
            mav = new ModelAndView("prebookechars_today");
            if (chooseType == 2) {//历史
                mav = new ModelAndView("prebookechars_history");
            }
        }
        if (CollectionUtils.isNotEmpty(enterpriseList)) {
            mav.addObject("enterpriseList", enterpriseList);
        }
        if (slaveEnterpriseId > 0) {
            enterpriseId = slaveEnterpriseId;
        }
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToDate(LocalDate.now());
            endDate = startDate;
        }
        mav.addObject("personal", personal);
        mav.addObject("team", team);
        mav.addObject("family", family);
        mav.addObject("preAll", preAll);

        mav.addObject("dailyVerification", dailyVerification);
        mav.addObject("enterpriseId", enterpriseId);
        mav.addObject("startDate", startDate);
        mav.addObject("endDate", endDate);
        mav.addObject("timeOff", timeOff);
        mav.addObject("slaveEnterpriseId", slaveEnterpriseId);
        mav.addObject("chooseType", chooseType);
        return mav;
    }

    @RequestMapping("people-counting-excel")
    @IgnoreAuth(requireLogin = true)
    public ResponseEntity<byte[]> peopleCountingExcel(HttpServletResponse response, HttpServletRequest request,
                                                      String startDate, String endDate) throws IOException {
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        ClassLoader classLoader = getClass().getClassLoader();
        /**
         * getResource()方法会去classpath下找这个文件，获取到url resource,
         * 得到这个资源后，调用url.getFile获取到 文件 的绝对路径
         */
        URL url = classLoader.getResource("excel/excel-template/people_counting_template.xls");
        URL url2 = classLoader.getResource("excel/people_counting_temp.xls");
        String num = RandomUtils.generateRandomNumber(5);
        new File(url2.getPath().replace(".xls", num + ".xls"));
        InputStream in = new FileInputStream(url.getPath());
        FileOutputStream out = new FileOutputStream(url2.getPath().replace(".xls", num + ".xls"));
        ExcelBean excelBean = new ExcelBean();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        List<Visitor> _list = visitorService.getAllByDate(startDate, endDate, employ.getEnterpriseId());
        Map<Date, List<Visitor>> map = com.taoart.common.utils.CollectionUtils.groupSort(_list, "signinTime");
        List<Visitor> retList = new ArrayList<Visitor>();
        Set<Date> set = map.keySet();
        Iterator<Date> it = set.iterator();

        while (it.hasNext()) {
            Date key = it.next();
            List<Visitor> list = map.get(key);
            Visitor o = new Visitor();
            o.setName(DateUtil.formatDateStr(key));
            int kidsCount = 0, oldCount = 0, tdCount = 0, jbCount = 0, wbCount = 0;
            for (int i = 0; i < list.size(); i++) {
                if ("44444444".equals(list.get(i).getIdentification())) {
                    tdCount += 1;
                } else if ("55555555".equals(list.get(i).getIdentification())) {
                    wbCount += 1;
                } else if ("66666666".equals(list.get(i).getIdentification())) {
                    jbCount += 1;
                } else if ("77777777".equals(list.get(i).getIdentification())) {
                    oldCount += 1;
                } else if ("88888888".equals(list.get(i).getIdentification())) {
                    kidsCount += 1;
                }
            }
            o.setDayCount(list.size());
            o.setTdCount(tdCount);
            o.setWbCount(wbCount);
            o.setJbCount(jbCount);
            o.setOldCount(oldCount);
            o.setKidsCount(kidsCount);
            retList.add(o);
        }
        int allCount = visitorService.selectAllCount(employ.getEnterpriseId());
        String dateStr = startDate + "至" + endDate;
        try {
            Context context = new Context();
            excelBean.setListData(retList);
            context.putVar("excelBean", excelBean);
            context.putVar("dateStr", dateStr);
            context.putVar("allCount", allCount);
            JxlsHelper.getInstance().processTemplate(in, out, context);
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        File f = null;
        f = new File(url2.getPath().replace(".xls", num + ".xls")); // 将服务器上的文件下载下来
        HttpHeaders headers = new HttpHeaders();
        String fileName = new String(("人流统计报表(" + dateStr + ").xls").getBytes("UTF-8"), "iso-8859-1");
        headers.setContentType(MediaType.valueOf("application/x-excel;charset=UTF-8"));
        headers.add("Content-Disposition", "attachment; filename=" + fileName);
        headers.add("Content-Length", String.valueOf(f.length()));
        byte[] b = FileUtils.readFileToByteArray(f);
        if (f.exists())
            f.delete();
        return new ResponseEntity<byte[]>(b, headers, HttpStatus.OK);
    }

    /**
     * 非遗报表导出
     *
     * @param response
     * @param request
     * @param startDate
     * @param endDate
     * @return
     * @throws IOException
     */
    @RequestMapping("feiyi-excel")
    @IgnoreAuth(requireLogin = true)
    public ResponseEntity<byte[]> feiyiExcel(HttpServletResponse response, HttpServletRequest request,
                                             String startDate, String endDate) throws IOException {
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        ClassLoader classLoader = getClass().getClassLoader();
        /**
         * getResource()方法会去classpath下找这个文件，获取到url resource,
         * 得到这个资源后，调用url.getFile获取到 文件 的绝对路径
         */
        URL url = classLoader.getResource("excel/excel-template/feiyi_prebook_template.xls");
        URL url2 = classLoader.getResource("excel/feiyi_prebook_temp.xls");
        String num = RandomUtils.generateRandomNumber(5);
        new File(url2.getPath().replace(".xls", num + ".xls"));
        InputStream in = new FileInputStream(url.getPath());
        FileOutputStream out = new FileOutputStream(url2.getPath().replace(".xls", num + ".xls"));
        ExcelBean excelBean = new ExcelBean();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        List<PrebookStatisVisitor> list = statisVisitorService.listByEnterpriseIdAndDate(startDate, endDate, employ.getEnterpriseId());
        List<String> dateList = new ArrayList<String>();
        for (PrebookStatisVisitor visitor : list) {
            String viewDateStr = CalfDateUtil.dateToStr(visitor.getPreviewDate(), "yyyy-MM-dd");
            if (!dateList.contains(viewDateStr)) {
                dateList.add(viewDateStr);
            }
        }
        //男
        int manCount = 0;
        //女
        int womanCount = 0;
        //未知
        int unCount = 0;
        //亲子
        int family_prebook_count = 0;
        //团队
        int team_prebook_count = 0;
        //现场
        int prebook_onsite_count = 0;
        //团队参观数
        int team_signin_visitor_count = 0;
        //进馆人数
        int jgCount = 0;
        //预约总数
        int yyCount = 0;
        //参观人次
        int rcCount = 0;

        for (String dateStr : dateList) {
            for (PrebookStatisVisitor visitor : list) {
                String viewDateStr = CalfDateUtil.dateToStr(visitor.getPreviewDate(), "yyyy-MM-dd");
                if (viewDateStr.equals(dateStr)) {
                    yyCount += visitor.getPrebookCount();
                    jgCount += visitor.getSigninVisitorCount();
                    rcCount += visitor.getSigninFrequencyCount();
                    manCount += visitor.getManCount();
                    womanCount += visitor.getWomanCount();
                    unCount += visitor.getUnknownSexCount();
                    family_prebook_count += visitor.getFamilyPrebookCount();
                    team_prebook_count += visitor.getTeamPrebookCount();
                    prebook_onsite_count += visitor.getPrebookOnsiteCount();
                    team_signin_visitor_count += visitor.getTeamSigninVisitorCount();
                }
            }
        }
        //参观预约率
        String cgyyl = "";
        java.text.DecimalFormat df = new java.text.DecimalFormat("#0.0000");
        java.text.DecimalFormat dfd = new java.text.DecimalFormat("#0.00");
        if (yyCount > 0) {
            BigDecimal a1 = new BigDecimal(df.format((double) jgCount / (double) yyCount));
            BigDecimal b1 = new BigDecimal(100);
            cgyyl = dfd.format(a1.multiply(b1)) + "%";
            if (((double) jgCount / (double) yyCount) > 1) {
                cgyyl = "100%";
            }

        } else {
            cgyyl = "0%";
        }

        //男女比例
        String manP = "", womanP = "", unP = "";
        if (jgCount > 0) {
            BigDecimal man = new BigDecimal(df.format((double) manCount / (double) jgCount));
            BigDecimal woman = new BigDecimal(df.format((double) womanCount / (double) jgCount));
            BigDecimal un = new BigDecimal(df.format((double) unCount / (double) jgCount));
            BigDecimal b1 = new BigDecimal(100);
            manP = dfd.format(man.multiply(b1)) + "%";
            womanP = dfd.format(woman.multiply(b1)) + "%";
            unP = dfd.format(un.multiply(b1)) + "%";
        } else {
            manP = "0%";
            womanP = "0%";
            unP = "0%";
        }

        List<PrebookStatisAge> ageList = statisAgeService.listByEnterpriseIdAndDate(startDate, endDate, employ.getEnterpriseId());
        int b0To16 = 0;
        int b17To29 = 0;
        int b30To39 = 0;
        int b40To49 = 0;
        int b50To59 = 0;
        int b60To69 = 0;
        int b70To79 = 0;
        int b80To89 = 0;
        int b90To99 = 0;
        int b100 = 0;
        int other = 0;
        for (PrebookStatisAge visitor : ageList) {
            b0To16 += visitor.getA0to16();
            b17To29 += visitor.getA17to29();
            b30To39 += visitor.getA30to39();
            b40To49 += visitor.getA40to49();
            b50To59 += visitor.getA50to59();
            b60To69 += visitor.getA60to69();
            b70To79 += visitor.getA70to79();
            b80To89 += visitor.getA80to89();
            b90To99 += visitor.getA90to99();
            b100 += visitor.getA100();
            other += visitor.getUnknownAge();
        }

        List<PrebookStatisArea> arealist = new ArrayList<PrebookStatisArea>();
        List<Integer> slaveEnterpriseIds = new ArrayList<>();
        slaveEnterpriseIds.add(employ.getEnterpriseId());
        arealist = statisAreaService.listByEnterpriseIdAndDate(startDate, endDate, slaveEnterpriseIds, 1, 3);

        String dateStr = startDate + "至" + endDate;
        try {
            Context context = new Context();
            excelBean.setListData(arealist);
            context.putVar("manCount", manCount);
            context.putVar("womanCount", womanCount);
            context.putVar("unCount", unCount);
            context.putVar("family_prebook_count", family_prebook_count);
            context.putVar("team_prebook_count", team_prebook_count);
            context.putVar("prebook_onsite_count", prebook_onsite_count);
            context.putVar("team_signin_visitor_count", team_signin_visitor_count);
            context.putVar("jgCount", jgCount);
            context.putVar("yyCount", yyCount);
            context.putVar("rcCount", rcCount);
            context.putVar("cgyyl", cgyyl);
            context.putVar("manP", manP);
            context.putVar("womanP", womanP);
            context.putVar("unP", unP);
            context.putVar("b0To16", b0To16);
            context.putVar("b17To29", b17To29);
            context.putVar("b30To39", b30To39);
            context.putVar("b40To49", b40To49);
            context.putVar("b50To59", b50To59);
            context.putVar("b60To69", b60To69);
            context.putVar("b70To79", b70To79);
            context.putVar("b80To89", b80To89);
            context.putVar("b90To99", b90To99);
            context.putVar("b100", b100);
            context.putVar("other", other);
            context.putVar("excelBean", excelBean);
            context.putVar("dateStr", dateStr);
            JxlsHelper.getInstance().processTemplate(in, out, context);
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        File f = null;
        f = new File(url2.getPath().replace(".xls", num + ".xls")); // 将服务器上的文件下载下来
        HttpHeaders headers = new HttpHeaders();
        String fileName = new String(("人流统计报表(" + dateStr + ").xls").getBytes("UTF-8"), "iso-8859-1");
        headers.setContentType(MediaType.valueOf("application/x-excel;charset=UTF-8"));
        headers.add("Content-Disposition", "attachment; filename=" + fileName);
        headers.add("Content-Length", String.valueOf(f.length()));
        byte[] b = FileUtils.readFileToByteArray(f);
        if (f.exists())
            f.delete();
        return new ResponseEntity<byte[]>(b, headers, HttpStatus.OK);
    }

    @RequestMapping("infoCount")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse infoCount(Date startDate, Date endDate, String info,
                                  @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        JsonResponse json = new JsonResponse();
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        List<Visitor> td = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                td = visitorService.selectAllForMS(startDate, endDate, slaveEnterpriseIds, info);
            } else {
                td = visitorService.selectAll(startDate, endDate, employ.getEnterpriseId(), info);
            }
        } else {
            td = visitorService.selectAll(startDate, endDate, employ.getEnterpriseId(), info);
        }
        json.setResult(td.size());
        return json;
    }

    @RequestMapping("infoCountBySc")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse infoCountBySc(Date startDate, Date endDate, String info,
                                      @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        JsonResponse json = new JsonResponse();
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        List<Visitor> td = new ArrayList<>();
        List<PrebookVisit> pd = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                td = visitorService.selectAllForMS(startDate, endDate, slaveEnterpriseIds, null);
                pd = prebookVisitService.selectAllForMS(startDate, endDate, slaveEnterpriseIds, 1);
            } else {
                td = visitorService.selectAll(startDate, endDate, employ.getEnterpriseId(), null);
                pd = prebookVisitService.selectAll(startDate, endDate, employ.getEnterpriseId(), 1);
            }
        } else {
            td = visitorService.selectAll(startDate, endDate, employ.getEnterpriseId(), null);
            pd = prebookVisitService.selectAll(startDate, endDate, employ.getEnterpriseId(), 1);
        }

        int b0To16 = 0;
        int tdCount = 0;
        int wbCount = 0;
        int jbCount = 0;
        int lrCount = 0;
        int etCount = 0;
        int zjCount = 0;
        for (Visitor visitor : td) {
            if (null != visitor.getIdentification()) {
                if (visitor.getIdentification().length() == 18 && !"".equals(visitor.getIdentification())
                        && !"888888888888888888".equals(visitor.getIdentification())) {
                    int age = StringUtils.IdNOToAge(visitor.getIdentification());
                    if (age >= 0 && age <= 16) {
                        b0To16++;
                    }
                    if (("浙江").equals(StringUtils.getProvinceByIdCard(visitor.getIdentification()))) {
                        zjCount++;
                    }
                } else if ("44444444".equals(visitor.getIdentification())) {
                    tdCount++;
                } else if ("55555555".equals(visitor.getIdentification())) {
                    wbCount++;
                } else if ("66666666".equals(visitor.getIdentification())) {
                    jbCount++;
                } else if ("77777777".equals(visitor.getIdentification())) {
                    lrCount++;
                } else if ("88888888".equals(visitor.getIdentification())) {
                    etCount++;
                }
            }
        }
        int xxCount = 0;
        for (PrebookVisit prebookVisit : pd) {
            if (prebookVisit.getPreType() == 1) {
                xxCount += prebookVisit.getQuantity();
            }
        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("wcnCount", xxCount + etCount + b0To16);
        returnMap.put("tdCount", tdCount);
        returnMap.put("wbCount", wbCount);
        returnMap.put("jbCount", jbCount);
        returnMap.put("lrCount", lrCount);
        returnMap.put("etCount", etCount);
        returnMap.put("zrsCount", td.size());
        returnMap.put("zjCount", zjCount + wbCount + jbCount + lrCount + etCount);
        json.setReturnMap(returnMap);
        return json;
    }

    @RequestMapping("getPrebookTypeCount")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getPrebookTypeCount(Date startDate, Date endDate,
                                            @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        JsonResponse json = new JsonResponse();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToDate(LocalDate.now());
            endDate = startDate;
        }
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        List<PrebookVisit> pd = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                pd = prebookVisitService.selectAllForMS(startDate, endDate, slaveEnterpriseIds, 2);
            } else {
                pd = prebookVisitService.selectAll(startDate, endDate, employ.getEnterpriseId(), 2);
            }
        } else {
            pd = prebookVisitService.selectAll(startDate, endDate, employ.getEnterpriseId(), 2);
        }

        int type1 = 0;
        int type2 = 0;
        int type3 = 0;
        for (PrebookVisit prebookVisit : pd) {
            if (prebookVisit.getPreType() == 1) {
                type1 += prebookVisit.getQuantity();
            } else if (prebookVisit.getPreType() == 2) {
                type2 += prebookVisit.getQuantity();
            } else if (prebookVisit.getPreType() == 3) {
                type3 += prebookVisit.getQuantity();
            }
        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("type1", type1);
        returnMap.put("type2", type2);
        returnMap.put("type3", type3);
        json.setReturnMap(returnMap);
        json.setSuccess(true);
        return json;
    }

    @RequestMapping("getaredcount")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getaredcount(@RequestParam(defaultValue = "0") int id, String startDate, String endDate,
                                     @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        JsonResponse json = new JsonResponse();
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        int betweenDay = getBetweenDay(DateUtil.strToDate(startDate, "yyyy-MM-dd"),
                DateUtil.strToDate(endDate, "yyyy-MM-dd"));
        List<Visitor> list = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                if (betweenDay < 90) {
                    list = visitorService.getByAreacountForMS(startDate, endDate, slaveEnterpriseIds, 1);
                } else {
                    list = visitorService.getByAreacountForMS(startDate, endDate, slaveEnterpriseIds, 2);
                }
            } else {
                if (betweenDay < 90) {
                    list = visitorService.getByAreacount(startDate, endDate, employ.getEnterpriseId(), 1);
                } else {
                    list = visitorService.getByAreacount(startDate, endDate, employ.getEnterpriseId(), 2);
                }
            }
        } else {
            if (betweenDay < 90) {
                list = visitorService.getByAreacount(startDate, endDate, slaveEnterpriseId, 1);
            } else {
                list = visitorService.getByAreacount(startDate, endDate, slaveEnterpriseId, 2);
            }
        }
        List<Visitor> tdlist = new ArrayList<>();
        int sumCount = 0;
        for (Visitor td : list) {
            if (td.getSigninQuantity() > 0) {
                sumCount += td.getSigninQuantity();
            }
        }
        for (Visitor td : list) {
            java.text.DecimalFormat df = new java.text.DecimalFormat("#0.0000");
            java.text.DecimalFormat dfd = new java.text.DecimalFormat("#0.00");
            BigDecimal a1 = new BigDecimal(df.format((double) td.getSigninQuantity() / (double) sumCount));
            BigDecimal b1 = new BigDecimal(100);
            td.setAllcount(dfd.format(a1.multiply(b1)));
            tdlist.add(td);
        }
        json.setList(tdlist);
        return json;
    }


    @RequestMapping("getAreaStatiscount")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getAreaStatiscount(@RequestParam(defaultValue = "0") int id, String startDate, String endDate,
                                           @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        JsonResponse json = new JsonResponse();
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        List<PrebookStatisArea> list = new ArrayList<PrebookStatisArea>();
        List<Integer> slaveEnterpriseIds = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
            } else {
                slaveEnterpriseIds.add(employ.getEnterpriseId());
            }
        } else {
            slaveEnterpriseIds.add(slaveEnterpriseId);
        }
        list = statisAreaService.listByEnterpriseIdAndDate(startDate, endDate, slaveEnterpriseIds, 1, 2);
        List<PrebookStatisArea> tdlist = new ArrayList<>();
        int sumCount = 0;
        for (PrebookStatisArea td : list) {
            if (td.getSigninFrequencyCount() > 0) {
                sumCount += td.getSigninFrequencyCount();
            }
        }
        for (PrebookStatisArea td : list) {
            if (td.getSigninFrequencyCount() > 0) {
                java.text.DecimalFormat df = new java.text.DecimalFormat("#0.0000");
                java.text.DecimalFormat dfd = new java.text.DecimalFormat("#0.00");
                BigDecimal a1 = new BigDecimal(df.format((double) td.getSigninFrequencyCount() / (double) sumCount));
                BigDecimal b1 = new BigDecimal(100);
                td.setAllcount(dfd.format(a1.multiply(b1)));
                tdlist.add(td);
            }
        }
        json.setList(tdlist);
        return json;
    }

    @RequestMapping("getAreaStatisPrecount")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getAreaStatisPrecount(@RequestParam(defaultValue = "0") int id, String startDate, String endDate,
                                              @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        JsonResponse json = new JsonResponse();
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        List<PrebookStatisArea> list = new ArrayList<PrebookStatisArea>();
        List<Integer> slaveEnterpriseIds = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
            } else {
                slaveEnterpriseIds.add(employ.getEnterpriseId());
            }
        } else {
            slaveEnterpriseIds.add(slaveEnterpriseId);
        }
        list = statisAreaService.listByEnterpriseIdAndDate(startDate, endDate, slaveEnterpriseIds, 1, 3);
        List<PrebookStatisArea> tdlist = new ArrayList<>();
        int sumCount = 0;
        for (PrebookStatisArea td : list) {
            if (td.getPrebookCount() > 0) {
                sumCount += td.getPrebookCount();
            }
        }
        for (PrebookStatisArea td : list) {
            if (td.getPrebookCount() > 0) {
                java.text.DecimalFormat df = new java.text.DecimalFormat("#0.0000");
                java.text.DecimalFormat dfd = new java.text.DecimalFormat("#0.00");
                BigDecimal a1 = new BigDecimal(df.format((double) td.getPrebookCount() / (double) sumCount));
                BigDecimal b1 = new BigDecimal(100);
                td.setAllcount(dfd.format(a1.multiply(b1)));
                tdlist.add(td);
            }
        }
        json.setList(tdlist);
        return json;
    }

    /**
     * 根据省份查下级
     *
     * @param id
     * @param startDate
     * @param endDate
     * @param slaveEnterpriseId
     * @return
     */
    @RequestMapping("getaredcountByProvince")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getaredcountByProvince(@RequestParam(defaultValue = "0") int id, String startDate, String endDate,
                                               @RequestParam(defaultValue = "0") int slaveEnterpriseId, String province) {
        JsonResponse json = new JsonResponse();
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        int betweenDay = getBetweenDay(DateUtil.strToDate(startDate, "yyyy-MM-dd"),
                DateUtil.strToDate(endDate, "yyyy-MM-dd"));
        province = StringUtils.getCodeByProvince(province);
        List<Visitor> list = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                if (betweenDay < 90) {
                    list = visitorService.getaredcountByProvinceForMS(startDate, endDate, slaveEnterpriseIds, 1, province);
                } else {
                    list = visitorService.getaredcountByProvinceForMS(startDate, endDate, slaveEnterpriseIds, 2, province);
                }
            } else {
                if (betweenDay < 90) {
                    list = visitorService.getaredcountByProvince(startDate, endDate, employ.getEnterpriseId(), 1, province);
                } else {
                    list = visitorService.getaredcountByProvince(startDate, endDate, employ.getEnterpriseId(), 2, province);
                }
            }
        } else {
            if (betweenDay < 90) {
                list = visitorService.getaredcountByProvince(startDate, endDate, slaveEnterpriseId, 1, province);
            } else {
                list = visitorService.getaredcountByProvince(startDate, endDate, slaveEnterpriseId, 2, province);
            }
        }
        List<Visitor> tdlist = new ArrayList<>();
        int sumCount = 0;
        for (Visitor td : list) {
            if (td.getSigninQuantity() > 0) {
                sumCount += td.getSigninQuantity();
            }
        }
        for (Visitor td : list) {
            java.text.DecimalFormat df = new java.text.DecimalFormat("#0.0000");
            java.text.DecimalFormat dfd = new java.text.DecimalFormat("#0.00");
            BigDecimal a1 = new BigDecimal(df.format((double) td.getSigninQuantity() / (double) sumCount));
            BigDecimal b1 = new BigDecimal(100);
            td.setAllcount(dfd.format(a1.multiply(b1)));
            tdlist.add(td);
        }
        json.setList(tdlist);
        return json;
    }

    @RequestMapping("getPrearedcount")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getPrearedcount(@RequestParam(defaultValue = "0") int id, String startDate, String endDate,
                                        @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        JsonResponse json = new JsonResponse();
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        int betweenDay = getBetweenDay(DateUtil.strToDate(startDate, "yyyy-MM-dd"),
                DateUtil.strToDate(endDate, "yyyy-MM-dd"));
        List<Visitor> list = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                if (betweenDay < 90) {
                    list = visitorService.getPreByAreacountForMS(startDate, endDate, slaveEnterpriseIds, 1);
                } else {
                    list = visitorService.getPreByAreacountForMS(startDate, endDate, slaveEnterpriseIds, 2);
                }
            } else {
                if (betweenDay < 90) {
                    list = visitorService.getPreByAreacount(startDate, endDate, employ.getEnterpriseId(), 1);
                } else {
                    list = visitorService.getPreByAreacount(startDate, endDate, employ.getEnterpriseId(), 2);
                }
            }
        } else {
            if (betweenDay < 90) {
                list = visitorService.getPreByAreacount(startDate, endDate, slaveEnterpriseId, 1);
            } else {
                list = visitorService.getPreByAreacount(startDate, endDate, slaveEnterpriseId, 2);
            }
        }
        List<Visitor> tdlist = new ArrayList<>();
        int sumCount = 0;
        for (Visitor td : list) {
            if (td.getCount() != null && !"".equals(td.getCount())) {
                sumCount += Integer.parseInt(td.getCount());
            }
        }
        for (Visitor td : list) {
            java.text.DecimalFormat df = new java.text.DecimalFormat("#0.0000");
            java.text.DecimalFormat dfd = new java.text.DecimalFormat("#0.00");
            BigDecimal a1 = new BigDecimal(df.format((double) Integer.parseInt(td.getCount()) / (double) sumCount));
            BigDecimal b1 = new BigDecimal(100);
            td.setAllcount(dfd.format(a1.multiply(b1)));
            tdlist.add(td);
        }
        json.setList(tdlist);
        return json;
    }


    @RequestMapping("getPrearedcountByProvince")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getPrearedcountByProvince(@RequestParam(defaultValue = "0") int id, String startDate, String endDate,
                                                  @RequestParam(defaultValue = "0") int slaveEnterpriseId, String province) {
        JsonResponse json = new JsonResponse();
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        int betweenDay = getBetweenDay(DateUtil.strToDate(startDate, "yyyy-MM-dd"),
                DateUtil.strToDate(endDate, "yyyy-MM-dd"));
        province = StringUtils.getCodeByProvince(province);
        List<Visitor> list = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                if (betweenDay < 90) {
                    list = visitorService.getPreByAreacountByProvinceForMS(startDate, endDate, slaveEnterpriseIds, 1, province);
                } else {
                    list = visitorService.getPreByAreacountByProvinceForMS(startDate, endDate, slaveEnterpriseIds, 2, province);
                }
            } else {
                if (betweenDay < 90) {
                    list = visitorService.getPreByAreacountByProvince(startDate, endDate, employ.getEnterpriseId(), 1, province);
                } else {
                    list = visitorService.getPreByAreacountByProvince(startDate, endDate, employ.getEnterpriseId(), 2, province);
                }
            }
        } else {
            if (betweenDay < 90) {
                list = visitorService.getPreByAreacountByProvince(startDate, endDate, slaveEnterpriseId, 1, province);
            } else {
                list = visitorService.getPreByAreacountByProvince(startDate, endDate, slaveEnterpriseId, 2, province);
            }
        }
        List<Visitor> tdlist = new ArrayList<>();
        int sumCount = 0;
        for (Visitor td : list) {
            if (td.getCount() != null && !"".equals(td.getCount())) {
                sumCount += Integer.parseInt(td.getCount());
            }
        }
        for (Visitor td : list) {
            java.text.DecimalFormat df = new java.text.DecimalFormat("#0.0000");
            java.text.DecimalFormat dfd = new java.text.DecimalFormat("#0.00");
            BigDecimal a1 = new BigDecimal(df.format((double) Integer.parseInt(td.getCount()) / (double) sumCount));
            BigDecimal b1 = new BigDecimal(100);
            td.setAllcount(dfd.format(a1.multiply(b1)));
            tdlist.add(td);
        }
        json.setList(tdlist);
        return json;
    }

    @RequestMapping("getagecount")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getagecount(@RequestParam(defaultValue = "0") int id, Date startDate, Date endDate,
                                    @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        JsonResponse json = new JsonResponse();
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToDate(LocalDate.now());
            endDate = startDate;
        }
        List<Visitor> list = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                list = visitorService.selectAllForMS(startDate, endDate, slaveEnterpriseIds, null);
            } else {
                list = visitorService.selectAll(startDate, endDate, employ.getEnterpriseId(), null);
            }
        } else {
            list = visitorService.selectAll(startDate, endDate, slaveEnterpriseId, null);
        }
        String[] strArr = {"0-16", "17-29", "30-39", "40-49", "50-59", "60-69", "70-79", "80-89", "90-99", "100及以上",
                "未知"};
        int b0To16 = 0;
        int b17To29 = 0;
        int b30To39 = 0;
        int b40To49 = 0;
        int b50To59 = 0;
        int b60To69 = 0;
        int b70To79 = 0;
        int b80To89 = 0;
        int b90To99 = 0;
        int b100 = 0;
        int other = 0;
        int total = 0;
        for (Visitor visitor : list) {
            if (visitor.getIdentification() != null && visitor.getIdentification().length() == 18
                    && !"".equals(visitor.getIdentification())
                    && !"888888888888888888".equals(visitor.getIdentification())) {
                int age = StringUtils.IdNOToAge(visitor.getIdentification());
                if (age >= 0 && age <= 16) {
                    b0To16 += visitor.getSigninQuantity();
                } else if (age >= 17 && age <= 29) {
                    b17To29 += visitor.getSigninQuantity();
                } else if (age >= 30 && age <= 39) {
                    b30To39 += visitor.getSigninQuantity();
                } else if (age >= 40 && age <= 49) {
                    b40To49 += visitor.getSigninQuantity();
                } else if (age >= 50 && age <= 59) {
                    b50To59 += visitor.getSigninQuantity();
                } else if (age >= 60 && age <= 69) {
                    b60To69 += visitor.getSigninQuantity();
                } else if (age >= 70 && age <= 79) {
                    b70To79 += visitor.getSigninQuantity();
                } else if (age >= 80 && age <= 89) {
                    b80To89 += visitor.getSigninQuantity();
                } else if (age >= 90 && age <= 99) {
                    b90To99 += visitor.getSigninQuantity();
                } else if (age >= 100) {
                    b100 += visitor.getSigninQuantity();
                } else {
                    other += visitor.getSigninQuantity();
                }
            } else {
                other += visitor.getSigninQuantity();
            }
            total += visitor.getSigninQuantity();
        }
        Integer[] intArr = {b0To16, b17To29, b30To39, b40To49, b50To59, b60To69, b70To79, b80To89, b90To99, b100,
                other};
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("xAlias", strArr);
        returnMap.put("yAlias", intArr);
        returnMap.put("total", total);
        json.setReturnMap(returnMap);
        json.setSuccess(true);
        return json;
    }


    @RequestMapping("get-age-statis-count")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getagestatiscount(@RequestParam(defaultValue = "0") int id, String startDate, String endDate,
                                          @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        JsonResponse json = new JsonResponse();
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (null == startDate || null == endDate) {
            Date date = CalfDateUtil.getDetachDate(CalfDateUtil.localDateToDate(LocalDate.now()), 1, 60 * 60 * 24);
            startDate = DateUtils.dateToStr(date, "yyyy-MM-dd");
            endDate = startDate;
        }
        List<PrebookStatisAge> list = new ArrayList<PrebookStatisAge>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                list = statisAgeService.listByEnterpriseIdAndDateForMS(startDate, endDate, slaveEnterpriseIds);
            } else {
                list = statisAgeService.listByEnterpriseIdAndDate(startDate, endDate, employ.getEnterpriseId());
            }
        } else {
            list = statisAgeService.listByEnterpriseIdAndDate(startDate, endDate, slaveEnterpriseId);
        }
        String[] strArr = {"0-16", "17-29", "30-39", "40-49", "50-59", "60-69", "70-79", "80-89", "90-99", "100及以上",
                "未知"};
        int b0To16 = 0;
        int b17To29 = 0;
        int b30To39 = 0;
        int b40To49 = 0;
        int b50To59 = 0;
        int b60To69 = 0;
        int b70To79 = 0;
        int b80To89 = 0;
        int b90To99 = 0;
        int b100 = 0;
        int other = 0;
        for (PrebookStatisAge visitor : list) {
            b0To16 += visitor.getA0to16();
            b17To29 += visitor.getA17to29();
            b30To39 += visitor.getA30to39();
            b40To49 += visitor.getA40to49();
            b50To59 += visitor.getA50to59();
            b60To69 += visitor.getA60to69();
            b70To79 += visitor.getA70to79();
            b80To89 += visitor.getA80to89();
            b90To99 += visitor.getA90to99();
            b100 += visitor.getA100();
            other += visitor.getUnknownAge();
        }
        Integer[] intArr = {b0To16, b17To29, b30To39, b40To49, b50To59, b60To69, b70To79, b80To89, b90To99, b100,
                other};
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("xAlias", strArr);
        returnMap.put("yAlias", intArr);
        json.setReturnMap(returnMap);
        json.setSuccess(true);
        return json;
    }

    @RequestMapping("getrealcount")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getrealcount(@RequestParam(defaultValue = "0") int id, Date startDate, Date endDate,
                                     @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        JsonResponse json = new JsonResponse();
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToDate(LocalDate.now());
            endDate = startDate;
        }
        List<Visitor> list = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                list = visitorService.getByDatePrebookCountForMS(startDate, endDate, slaveEnterpriseIds, 1);
            } else {
                list = visitorService.getByDatePrebookCount(startDate, endDate, employ.getEnterpriseId(), 2);
            }
        } else {
            list = visitorService.getByDatePrebookCount(startDate, endDate, slaveEnterpriseId, 2);
        }
        List<Visitor> visitors = visitorService.listAllByStatisPre(startDate, endDate, slaveEnterpriseId);
        int yyCount = 0;
        for (Visitor v : visitors) {
            yyCount += v.getPreCount();
        }
        int jgCount = 0; //进馆人数
        int rcCount = 0; //进馆人次
        for (Visitor visitor : list) {
            if (visitor.getStatus() == 2) {
                if (visitor.getCount() != null && !"".equals(visitor.getCount())) {
                    jgCount += Integer.parseInt(visitor.getCount());
                }
                rcCount += visitor.getSigninQuantity();
            }
        }

        Map<String, Object> returnMap = new HashMap<>();
        int outCount = visitorSignoutService.selectSignOutQuantity(startDate, endDate, slaveEnterpriseId); //出馆人次
        String[] strArr = {"预约人数", "进馆人数", "进馆人次", "出馆人次", "当前在馆人数"};
        //在馆人数
        int inCount = (jgCount - outCount) > 0 ? (jgCount - outCount) : 0;
        LocalDateTime nowTime = LocalDateTime.now();
        int hour = nowTime.getHour();
        if (hour > 18) {
            inCount = 0;
        }
        Integer[] intArr = {yyCount, jgCount, rcCount, outCount, inCount};
        returnMap.put("xAlias", strArr);
        returnMap.put("yAlias", intArr);
        json.setReturnMap(returnMap);
        json.setSuccess(true);
        return json;
    }

    @RequestMapping("getageone")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getageone(@RequestParam(defaultValue = "0") int id, Date startDate, Date endDate, String age,
                                  @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        JsonResponse json = new JsonResponse();
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToDate(LocalDate.now());
            endDate = startDate;
        }
        List<Visitor> list = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                list = visitorService.selectAllForMS(startDate, endDate, slaveEnterpriseIds, null);
            } else {
                list = visitorService.selectAll(startDate, endDate, employ.getEnterpriseId(), null);
            }
        } else {
            list = visitorService.selectAll(startDate, endDate, slaveEnterpriseId, null);
        }
        if ("其他".equals(age) || "100及以上".equals(age)) {
            return json;
        }
        String[] ageArr = age.split("-");
        int begin = Integer.parseInt(ageArr[0]);
        int end = Integer.parseInt(ageArr[1]);
        Map<String, Integer> map = new HashMap<String, Integer>();
        for (int i = begin; i <= end; i++) {
            map.put(i + "", 0);
        }
        int count = 0;
        for (Visitor visitor : list) {
            if (visitor.getIdentification() != null && !"88888888".equals(visitor.getIdentification())
                    && !"".equals(visitor.getIdentification())
                    && !"888888888888888888".equals(visitor.getIdentification())
                    && visitor.getIdentification().length() == 18) {
                // 杭州观众
                int age2 = StringUtils.IdNOToAge(visitor.getIdentification());
                if (begin <= age2 && age2 <= end) {
                    map.put(age2 + "", map.get(age2 + "") + visitor.getSigninQuantity());
                    count += visitor.getSigninQuantity();
                }
            }
            map.put("合计", count);
        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("yAlias", map);
        json.setReturnMap(returnMap);
        json.setSuccess(true);
        return json;
    }

    @RequestMapping("getBySexcount")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getBySexcount(@RequestParam(defaultValue = "0") int id, String startDate, String endDate,
                                      @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        JsonResponse json = new JsonResponse();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        int betweenDay = getBetweenDay(DateUtil.strToDate(startDate, "yyyy-MM-dd"),
                DateUtil.strToDate(endDate, "yyyy-MM-dd"));
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        List<Visitor> list = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                if (betweenDay < 90) {
                    list = visitorService.getBySexcountForMS(startDate, endDate, slaveEnterpriseIds, 1);
                } else {
                    list = visitorService.getBySexcountForMS(startDate, endDate, slaveEnterpriseIds, 2);
                }
            } else {
                if (betweenDay < 90) {
                    list = visitorService.getBySexcount(startDate, endDate, employ.getEnterpriseId(), 1);
                } else {
                    list = visitorService.getBySexcount(startDate, endDate, employ.getEnterpriseId(), 2);
                }
            }
        } else {
            if (betweenDay < 90) {
                list = visitorService.getBySexcount(startDate, endDate, slaveEnterpriseId, 1);
            } else {
                list = visitorService.getBySexcount(startDate, endDate, slaveEnterpriseId, 2);
            }
        }
        int womenCount = 0;
        int visitCount = 0;
        int wzCount = 0;
        for (Visitor visitor : list) {
            if (visitor.getSex() == 1) {
                visitCount = visitor.getSignCount();
            } else if (visitor.getSex() == 2) {
                womenCount = visitor.getSignCount();
            } else if (visitor.getSex() == 0) {
                wzCount = visitor.getSignCount();
            }
        }
        int allCount = 0;
        allCount += visitCount;
        allCount += womenCount;
        allCount += wzCount;
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("visitCount", visitCount);
        returnMap.put("womenCount", womenCount);
        returnMap.put("wzCount", wzCount);
        returnMap.put("allCount", allCount);
        json.setSuccess(true);
        json.setReturnMap(returnMap);
        json.setList(list);
        return json;
    }

    @RequestMapping("getBySrcTypeCount")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getBySrcTypeCount(@RequestParam(defaultValue = "0") int id, String startDate, String endDate,
                                          @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        JsonResponse json = new JsonResponse();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        int betweenDay = getBetweenDay(DateUtil.strToDate(startDate, "yyyy-MM-dd"),
                DateUtil.strToDate(endDate, "yyyy-MM-dd"));
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        List<Visitor> list = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                if (betweenDay < 90) {
                    list = visitorService.getBySrcTypeCountForMS(startDate, endDate, slaveEnterpriseIds, 1);
                } else {
                    list = visitorService.getBySrcTypeCountForMS(startDate, endDate, slaveEnterpriseIds, 2);
                }
            } else {
                if (betweenDay < 90) {
                    list = visitorService.getBySrcTypeCount(startDate, endDate, employ.getEnterpriseId(), 1);
                } else {
                    list = visitorService.getBySrcTypeCount(startDate, endDate, employ.getEnterpriseId(), 2);
                }
            }
        } else {
            if (betweenDay < 90) {
                list = visitorService.getBySrcTypeCount(startDate, endDate, slaveEnterpriseId, 1);
            } else {
                list = visitorService.getBySrcTypeCount(startDate, endDate, slaveEnterpriseId, 2);
            }
        }
        int sfzCount = 0;
        int hzCount = 0;
        int gaCount = 0;
        int tbCount = 0;
        int sbkCount = 0;
        for (Visitor visitor : list) {
            if (visitor.getSrcType() == 1) {
                sfzCount = visitor.getSignCount();
            } else if (visitor.getSrcType() == 5) {
                hzCount = visitor.getSignCount();
            } else if (visitor.getSrcType() == 7) {
                gaCount = visitor.getSignCount();
            } else if (visitor.getSrcType() == 6) {
                tbCount = visitor.getSignCount();
            } else if (visitor.getSrcType() == 9) {
                sbkCount = visitor.getSignCount();
            }
        }
        int allCount = sfzCount + hzCount + gaCount + tbCount;
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("sfzCount", sfzCount);
        returnMap.put("hzCount", hzCount);
        returnMap.put("gaCount", gaCount);
        returnMap.put("tbCount", tbCount);
        returnMap.put("sbkCount", sbkCount);
        returnMap.put("allCount", allCount);
        json.setSuccess(true);
        json.setReturnMap(returnMap);
        json.setList(list);
        return json;
    }

    @RequestMapping("getBycreateDatecount")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getBycreateDatecount(@RequestParam(defaultValue = "0") int id, String startDate, String endDate,
                                             @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        JsonResponse json = new JsonResponse();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        int betweenDay = getBetweenDay(DateUtil.strToDate(startDate, "yyyy-MM-dd"),
                DateUtil.strToDate(endDate, "yyyy-MM-dd"));
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        List<Visitor> list1 = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                if (betweenDay < 90) {
                    list1 = visitorService.getBycreateDatecountForMS(startDate, endDate, slaveEnterpriseIds, 1);
                } else {
                    list1 = visitorService.getBycreateDatecountForMS(startDate, endDate, slaveEnterpriseIds, 2);
                }
            } else {
                if (betweenDay < 90) {
                    list1 = visitorService.getBycreateDatecount(startDate, endDate, employ.getEnterpriseId(), 1);
                } else {
                    list1 = visitorService.getBycreateDatecount(startDate, endDate, employ.getEnterpriseId(), 2);
                }
            }
        } else {
            if (betweenDay < 90) {
                list1 = visitorService.getBycreateDatecount(startDate, endDate, slaveEnterpriseId, 1);
            } else {
                list1 = visitorService.getBycreateDatecount(startDate, endDate, slaveEnterpriseId, 2);
            }
        }
        json.setList(list1);
        return json;
    }

    @RequestMapping("getBycreatedayDatecount")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getBycreatedayDatecount(@RequestParam(defaultValue = "0") int id, String startDate,
                                                String endDate, @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        JsonResponse json = new JsonResponse();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        int betweenDay = getBetweenDay(DateUtil.strToDate(startDate, "yyyy-MM-dd"),
                DateUtil.strToDate(endDate, "yyyy-MM-dd"));
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        List<Visitor> list1 = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                if (betweenDay < 90) {
                    list1 = visitorService.getBycreateDatecountForMS(startDate, endDate, slaveEnterpriseIds, 1);
                } else {
                    list1 = visitorService.getBycreateDatecountForMS(startDate, endDate, slaveEnterpriseIds, 2);
                }
            } else {
                if (betweenDay < 90) {
                    list1 = visitorService.getBycreateDatecount(startDate, endDate, employ.getEnterpriseId(), 1);
                } else {
                    list1 = visitorService.getBycreateDatecount(startDate, endDate, employ.getEnterpriseId(), 2);
                }
            }
        } else {
            if (betweenDay < 90) {
                list1 = visitorService.getBycreateDatecount(startDate, endDate, slaveEnterpriseId, 1);
            } else {
                list1 = visitorService.getBycreateDatecount(startDate, endDate, slaveEnterpriseId, 2);
            }
        }
        List<Weather> wlist = new ArrayList<>();
        Date weatherStartDate = CalfDateUtil.strToDate(startDate);
        Date weatherEndDate = CalfDateUtil.strToDate(endDate);
        wlist = weatherService.getListByDay(weatherStartDate, weatherEndDate);
        List<Integer> countList = new ArrayList<>();
        List<String> listString = new ArrayList<>();
        // 排序
        // 必须是Comparator中的compare方法和Collections.sort方法配合使用才管用
        // MyComparator mc = new MyComparator();
        // Collections.sort(hourList, mc);
        // 时间list
        List<String> dateList = new ArrayList<String>();
        int max = 0;

        LocalDate startLocalDate = CalfDateUtil.strToLocalDate(startDate);
        LocalDate endLocalDate = CalfDateUtil.strToLocalDate(endDate);

        while (endLocalDate.compareTo(startLocalDate) > -1) {

            dateList.add(CalfDateUtil.localDateToStr(startLocalDate));
            startLocalDate = startLocalDate.plusDays(1);

        }

        for (String str : dateList) {
            Visitor o = null;

            for (Visitor v : list1) {
                if (DateUtils.dateToStr(v.getPreviewDate(), "yyyy-MM-dd").equals(str)) {
                    o = v;

                    break;
                }
            }
            int count = 0;
            if (o != null) {
                if (o.getCount() != null && !"".equals(o.getCount())) {
                    count = Integer.parseInt(o.getCount());
                }
            }
            if (max < count) {
                max = count;
            }
            countList.add(count);
            boolean tempFlag = false;
            for (Weather weather : wlist) {
                if (weather.getTime().equals(str)) {
                    tempFlag = true;
                    listString.add(weather.getTemperature());
                    break;
                }
            }
            if (!tempFlag) {
                listString.add("0");
            }
        }
        if (max < 10) {
            max = max + 2;
        } else if (max < 100) {
            max = max + 20;
        } else if (max < 1000) {
            max = max + 200;
        } else if (max < 10000) {
            max = max + 2000;
        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("listint", listString);
        // returnMap.put("listString", listString);
        returnMap.put("dateList", dateList);
        returnMap.put("countList", countList);
        json.setSuccess(true);
        json.setReturnMap(returnMap);
        json.setResult(max);
        json.setList(list1);
        return json;
    }

    @RequestMapping("getBycreatehourDatecount")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getBycreatehourDatecount(@RequestParam(defaultValue = "0") int id, Date startDate, Date endDate,
                                                 @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        JsonResponse json = new JsonResponse();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToDate(LocalDate.now());
            endDate = startDate;
        }
        int betweenDay = getBetweenDay(startDate, endDate);
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        List<Visitor> list1 = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                if (betweenDay < 90) {
                    list1 = visitorService.getByhourcountForMS(startDate, endDate, slaveEnterpriseIds, 1);
                } else {
                    list1 = visitorService.getByhourcountForMS(startDate, endDate, slaveEnterpriseIds, 2);
                }
            } else {
                if (betweenDay < 90) {
                    list1 = visitorService.getByhourcount(startDate, endDate, employ.getEnterpriseId(), 1);
                } else {
                    list1 = visitorService.getByhourcount(startDate, endDate, employ.getEnterpriseId(), 2);
                }
            }
        } else {
            if (betweenDay < 90) {
                list1 = visitorService.getByhourcount(startDate, endDate, slaveEnterpriseId, 1);
            } else {
                list1 = visitorService.getByhourcount(startDate, endDate, slaveEnterpriseId, 2);
            }
        }
        List<Weather> wlist = new ArrayList<>();
        String startDateStr = CalfDateUtil.dateToStr(startDate, CalfDateUtil.DATE_FORMAT);
        String endDateStr = CalfDateUtil.dateToStr(endDate, CalfDateUtil.DATE_FORMAT);
        wlist = weatherService.getListByHour(startDateStr, endDateStr);
        int max = 0;
        List<Weather> hourList = new ArrayList<Weather>();
        List<String> listString = new ArrayList<String>();
        for (Weather weather : wlist) {
            if (weather.getTime().indexOf('-') == -1) {
                hourList.add(weather);
                listString.add(weather.getTemperature());
            }
        }
        List<Integer> countList = new ArrayList<>();
        Map<Object, Visitor> map = com.taoart.common.utils.CollectionUtils.toMap(list1, "value");
        List<Visitor> retList = new ArrayList<Visitor>();
        int count = 0;
        for (int i = 0; i < 24; i++) {
            if (null != map.get("" + i + "")) {
                retList.add(map.get("" + i + ""));
                count = Integer.parseInt(map.get("" + i + "").getCount());
                countList.add(count);
                if (max < count) {
                    max = count;
                }
            } else {
                retList.add(new Visitor());
                countList.add(0);
            }
        }
        // 排序
        // 必须是Comparator中的compare方法和Collections.sort方法配合使用才管用
        MyComparator mc = new MyComparator();
        Collections.sort(hourList, mc);
        // 时间list
        List<String> dateList = new ArrayList<String>();
        for (Weather wea : hourList) {
            dateList.add(wea.getTime() + "时");
        }

        if (max < 10) {
            max = max + 2;
        } else if (max < 100) {
            max = max + 20;
        } else if (max < 1000) {
            max = max + 200;
        } else if (max < 10000) {
            max = max + 2000;
        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("listint", listString);
        returnMap.put("dateList", dateList);
        returnMap.put("countList", countList);
        json.setSuccess(true);
        json.setReturnMap(returnMap);
        json.setResult(max);
        json.setList(list1);
        return json;
    }

    @RequestMapping("getByhourcount")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getByhourcount(Date startDate, Date endDate,
                                       @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        JsonResponse json = new JsonResponse();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToDate(LocalDate.now());
            endDate = startDate;
        }
        int betweenDay = getBetweenDay(startDate, endDate);
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        List<Visitor> list2 = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                if (betweenDay < 90) {
                    list2 = visitorService.getByhourcountForMS(startDate, endDate, slaveEnterpriseIds, 1);
                } else {
                    list2 = visitorService.getByhourcountForMS(startDate, endDate, slaveEnterpriseIds, 2);
                }
            } else {
                if (betweenDay < 90) {
                    list2 = visitorService.getByhourcount(startDate, endDate, employ.getEnterpriseId(), 1);
                } else {
                    list2 = visitorService.getByhourcount(startDate, endDate, employ.getEnterpriseId(), 2);
                }
            }
        } else {
            if (betweenDay < 90) {
                list2 = visitorService.getByhourcount(startDate, endDate, slaveEnterpriseId, 1);
            } else {
                list2 = visitorService.getByhourcount(startDate, endDate, slaveEnterpriseId, 2);
            }
        }
        Map<Object, Visitor> map = com.taoart.common.utils.CollectionUtils.toMap(list2, "value");
        List<Visitor> retList = new ArrayList<Visitor>();
        for (int i = 0; i < 24; i++) {
            if (null != map.get("" + i + "")) {
                retList.add(map.get("" + i + ""));
            } else {
                retList.add(new Visitor());
            }
        }
        json.setList(retList);
        json.setSuccess(true);
        return json;
    }

    @RequestMapping("getByDatePrebookCount")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getByDatePrebookCount(Date startDate, Date endDate,
                                              @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToDate(LocalDate.now());
            endDate = startDate;
        }
        int betweenDay = getBetweenDay(startDate, endDate);
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        JsonResponse json = this.success();
        List<Visitor> list = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                if (betweenDay < 90) {
                    list = visitorService.getByDatePrebookCountForMS(startDate, endDate, slaveEnterpriseIds, 1);
                } else {
                    list = visitorService.getByDatePrebookCountForMS(startDate, endDate, slaveEnterpriseIds, 2);
                }
            } else {
                if (betweenDay < 90) {
                    list = visitorService.getByDatePrebookCount(startDate, endDate, employ.getEnterpriseId(), 1);
                } else {
                    list = visitorService.getByDatePrebookCount(startDate, endDate, employ.getEnterpriseId(), 2);
                }
            }
        } else {
            if (betweenDay < 90) {
                list = visitorService.getByDatePrebookCount(startDate, endDate, slaveEnterpriseId, 1);
            } else {
                list = visitorService.getByDatePrebookCount(startDate, endDate, slaveEnterpriseId, 2);
            }
        }
        List<String> dateList = new ArrayList<String>();
        for (Visitor visitor : list) {
            String viewDateStr = CalfDateUtil.dateToStr(visitor.getPreviewDate(), "yyyy-MM-dd");
            if (!dateList.contains(viewDateStr)) {
                dateList.add(viewDateStr);
            }
        }
        List<Integer> jg = new ArrayList<>();
        List<Integer> yy = new ArrayList<>();
        for (String dateStr : dateList) {
            int jgCount = 0;
            int yyCount = 0;
            for (Visitor visitor : list) {
                String viewDateStr = CalfDateUtil.dateToStr(visitor.getPreviewDate(), "yyyy-MM-dd");
                if (viewDateStr.equals(dateStr) && visitor.getStatus() != 3) {
                    if (visitor.getPeriodId() != 0) {
                        yyCount += Integer.parseInt(visitor.getCount());
                    }
                    if (visitor.getStatus() == 2) {
                        jgCount += visitor.getSigninQuantity();
                    }
                }
            }
            yy.add(yyCount);
            jg.add(jgCount);
        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("yyList", yy);
        returnMap.put("jgList", jg);
        returnMap.put("dateList", dateList);
        json.setReturnMap(returnMap);

        return json;
    }

    @RequestMapping("getByStatisVisitor")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getByStatisVisitor(String startDate, String endDate,
                                           @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        if (null == startDate || null == endDate) {
            Date date = CalfDateUtil.getDetachDate(CalfDateUtil.localDateToDate(LocalDate.now()), 1, 60 * 60 * 24);
            startDate = DateUtils.dateToStr(date, "yyyy-MM-dd");
            endDate = startDate;
        }
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        JsonResponse json = this.success();
        List<PrebookStatisVisitor> list = new ArrayList<PrebookStatisVisitor>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                list = statisVisitorService.listByEnterpriseIdAndDateForMS(startDate, endDate, slaveEnterpriseIds);
            } else {
                list = statisVisitorService.listByEnterpriseIdAndDate(startDate, endDate, employ.getEnterpriseId());
            }
        } else {
            list = statisVisitorService.listByEnterpriseIdAndDate(startDate, endDate, slaveEnterpriseId);
        }
        List<String> dateList = new ArrayList<String>();
        for (PrebookStatisVisitor visitor : list) {
            String viewDateStr = CalfDateUtil.dateToStr(visitor.getPreviewDate(), "yyyy-MM-dd");
            if (!dateList.contains(viewDateStr)) {
                dateList.add(viewDateStr);
            }
        }
        //进馆人数
        List<Integer> jg = new ArrayList<>();
        //进馆人次
        List<Integer> rc = new ArrayList<>();
        //预约数
        List<Integer> yy = new ArrayList<>();
        int manCount = 0;
        int womanCount = 0;
        int unCount = 0;
        int hzCount = 0;
        int tbCount = 0;
        int gangCount = 0;
        int sbCount = 0;
        int wgrCount = 0; //外国人永久居留身份证进馆人数

        int family_prebook_count = 0;
        int team_prebook_count = 0;
        int prebook_onsite_count = 0;
        int team_signin_visitor_count = 0;

        for (String dateStr : dateList) {
            int jgCount = 0;
            int yyCount = 0;
            int rcCount = 0;
            for (PrebookStatisVisitor visitor : list) {
                String viewDateStr = CalfDateUtil.dateToStr(visitor.getPreviewDate(), "yyyy-MM-dd");
                if (viewDateStr.equals(dateStr)) {
                    yyCount += visitor.getPrebookCount();
                    jgCount += visitor.getSigninVisitorCount();
                    rcCount += visitor.getSigninFrequencyCount();
                    manCount += visitor.getManCount();
                    womanCount += visitor.getWomanCount();
                    unCount += visitor.getUnknownSexCount();
                    hzCount += visitor.getIdcard5Count();
                    tbCount += visitor.getIdcard6Count();
                    gangCount += visitor.getIdcard7Count();
                    sbCount += visitor.getIdcard9Count();
                    wgrCount += visitor.getIdcard11Count();
                    family_prebook_count += visitor.getFamilyPrebookCount();
                    team_prebook_count += visitor.getTeamPrebookCount();
                    prebook_onsite_count += visitor.getPrebookOnsiteCount();
                    team_signin_visitor_count += visitor.getTeamSigninVisitorCount();
                }
            }
            yy.add(yyCount);
            jg.add(jgCount);
            rc.add(rcCount);
        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("yyList", yy);
        returnMap.put("jgList", jg);
        returnMap.put("rcList", rc);
        returnMap.put("manCount", manCount);
        returnMap.put("womanCount", womanCount);
        returnMap.put("unCount", unCount);
        returnMap.put("hzCount", hzCount);
        returnMap.put("tbCount", tbCount);
        returnMap.put("gangCount", gangCount);
        returnMap.put("sbCount", sbCount);
        returnMap.put("wgrCount", wgrCount);
        returnMap.put("family_prebook_count", family_prebook_count);
        returnMap.put("team_prebook_count", team_prebook_count);
        returnMap.put("prebook_onsite_count", prebook_onsite_count);
        returnMap.put("team_signin_visitor_count", team_signin_visitor_count);
        returnMap.put("dateList", dateList);
        json.setReturnMap(returnMap);

        return json;
    }

    @RequestMapping("getByHourSigninQuantity")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getByHourSigninQuantity(Date startDate, Date endDate,
                                                @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        JsonResponse json = this.success();
        List<Visitor> list = visitorService.getByhourcount(startDate, endDate, employ.getEnterpriseId(), 1);
        //Map<Object, Visitor> map = com.taoart.common.utils.CollectionUtils.toMap(list, "value");

        //小时
        List<String> hour = new ArrayList<>();
        //进馆人数
        List<String> jg = new ArrayList<>();
        //进馆人次
        List<Integer> rc = new ArrayList<>();
        list.forEach(v -> {
            hour.add(v.getValue());
            jg.add(v.getCount());
            rc.add(v.getSigninQuantity());
        });
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("hour", hour);
        returnMap.put("jg", jg);
        returnMap.put("rc", rc);
        json.setReturnMap(returnMap);
        return json;
    }

    @RequestMapping("getByDateSigninCount")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getByDateSigninCount(Date startDate, Date endDate,
                                             @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToDate(LocalDate.now());
            endDate = startDate;
        }
        int betweenDay = getBetweenDay(startDate, endDate);
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        JsonResponse json = this.success();
        List<Visitor> list = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                if (betweenDay < 90) {
                    list = visitorService.getByDateSigninCountForMS(startDate, endDate, slaveEnterpriseIds, 1);
                } else {
                    list = visitorService.getByDateSigninCountForMS(startDate, endDate, slaveEnterpriseIds, 2);
                }
            } else {
                if (betweenDay < 90) {
                    list = visitorService.getByDateSigninCount(startDate, endDate, employ.getEnterpriseId(), 1);
                } else {
                    list = visitorService.getByDateSigninCount(startDate, endDate, employ.getEnterpriseId(), 2);
                }
            }
        } else {
            if (betweenDay < 90) {
                list = visitorService.getByDateSigninCount(startDate, endDate, slaveEnterpriseId, 1);
            } else {
                list = visitorService.getByDateSigninCount(startDate, endDate, slaveEnterpriseId, 2);
            }
        }
        List<String> dateList = new ArrayList<String>();
        for (Visitor visitor : list) {
            String viewDateStr = CalfDateUtil.dateToStr(visitor.getPreviewDate(), "yyyy-MM-dd");
            if (!dateList.contains(viewDateStr)) {
                dateList.add(viewDateStr);
            }
        }
        List<Integer> jg = new ArrayList<>();//进馆人数
        for (String dateStr : dateList) {
            int jgCount = 0;
            for (Visitor visitor : list) {
                String viewDateStr = CalfDateUtil.dateToStr(visitor.getPreviewDate(), "yyyy-MM-dd");
                if (viewDateStr.equals(dateStr)) {
                    jgCount += visitor.getSignCount();
                }
            }
            jg.add(jgCount);
        }

        List<Integer> jgrc = new ArrayList<>();//进馆人次
        for (String dateStr : dateList) {
            int jgCount = 0;
            for (Visitor visitor : list) {
                String viewDateStr = CalfDateUtil.dateToStr(visitor.getPreviewDate(), "yyyy-MM-dd");
                if (viewDateStr.equals(dateStr)) {
                    jgCount += Integer.parseInt(visitor.getCount());
                }
            }
            jgrc.add(jgCount);
        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("jgList", jg);//进馆人数
        returnMap.put("jgrcList", jgrc);//进馆人次
        returnMap.put("dateList", dateList);
        json.setReturnMap(returnMap);

        return json;
    }

    @RequestMapping("getPrebookHourCount")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getPrebookHourCount(Date startDate, Date endDate,
                                            @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        JsonResponse json = new JsonResponse();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToDate(LocalDate.now());
            endDate = startDate;
        }
        int betweenDay = getBetweenDay(startDate, endDate);
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        List<Visitor> list2 = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                if (betweenDay < 90) {
                    list2 = visitorService.getByhourcountForMS(startDate, endDate, slaveEnterpriseIds, 1);
                } else {
                    list2 = visitorService.getByhourcountForMS(startDate, endDate, slaveEnterpriseIds, 2);
                }
            } else {
                if (betweenDay < 90) {
                    list2 = visitorService.getByhourcount(startDate, endDate, employ.getEnterpriseId(), 1);
                } else {
                    list2 = visitorService.getByhourcount(startDate, endDate, employ.getEnterpriseId(), 2);
                }
            }
        } else {
            if (betweenDay < 90) {
                list2 = visitorService.getByhourcount(startDate, endDate, slaveEnterpriseId, 1);
            } else {
                list2 = visitorService.getByhourcount(startDate, endDate, slaveEnterpriseId, 2);
            }
        }
        Map<Object, Visitor> map = com.taoart.common.utils.CollectionUtils.toMap(list2, "value");
        List<Visitor> retList = new ArrayList<Visitor>();
        int max = 0;

        for (int i = 0; i < 24; i++) {
            if (i < 7 || i == 23) {
                continue;
            }
            if (null != map.get("" + i + "")) {
                retList.add(map.get("" + i + ""));
                if (Integer.parseInt(map.get("" + i + "").getCount()) > max) {
                    max = Integer.parseInt(map.get("" + i + "").getCount());
                }
            } else {
                retList.add(new Visitor());
            }
        }

        if (max < 10) {
            max = max + 2;
        } else if (max < 100) {
            max = max + 20;
        } else if (max < 1000) {
            max = max + 200;
        } else if (max < 10000) {
            max = max + 2000;
        }
        json.setList(retList);
        json.setResult(max);
        json.setSuccess(true);
        return json;
    }

    private static int getBetweenDay(Date date1, Date date2) {
        Calendar d1 = new GregorianCalendar();
        d1.setTime(date1);
        Calendar d2 = new GregorianCalendar();
        d2.setTime(date2);
        int days = d2.get(Calendar.DAY_OF_YEAR) - d1.get(Calendar.DAY_OF_YEAR);
        int y2 = d2.get(Calendar.YEAR);
        if (d1.get(Calendar.YEAR) != y2) {
            do {
                days += d1.getActualMaximum(Calendar.DAY_OF_YEAR);
                d1.add(Calendar.YEAR, 1);
            } while (d1.get(Calendar.YEAR) != y2);
        }
        return days;
    }

    @RequestMapping("getByPeriod")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse getByPeriod(@RequestParam(defaultValue = "0") int id, Date startDate, Date endDate,
                                    @RequestParam(defaultValue = "0") int slaveEnterpriseId) {
        JsonResponse json = this.success();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToDate(LocalDate.now());
            endDate = startDate;
        }
        int betweenDay = getBetweenDay(startDate, endDate);
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        List<Visitor> list = new ArrayList<>();
        if (slaveEnterpriseId == 0) {
            List<Integer> slaveEnterpriseIds = new ArrayList<>();
            List<Enterprise> enterpriseList = enterpriseSaleServiceApi.selectByParentId(employ.getEnterpriseId());
            if (CollectionUtils.isNotEmpty(enterpriseList)) {
                for (Enterprise enterprise : enterpriseList) {
                    slaveEnterpriseIds.add(enterprise.getId());
                }
                // list = visitorService.getByPeriodForMS(startDate, endDate,
                // slaveEnterpriseIds);
                if (betweenDay < 90) {
                    list = visitorService.getByPeriodForMS(startDate, endDate, slaveEnterpriseIds, 1);
                } else {
                    list = visitorService.getByPeriodForMS(startDate, endDate, slaveEnterpriseIds, 2);
                }

                List<PrebookPeriod> periodList = prebookService.listAllForMS(slaveEnterpriseIds);
                List<String> pList = new ArrayList<String>();
                for (PrebookPeriod p : periodList) {
                    pList.add(p.getName());
                }
                HashMap<String, Visitor> map = new HashMap<>();
                HashMap<String, String> hasMap = new HashMap<>();
                for (Visitor visitor : list) {
                    String periodName = visitor.getName();
                    if (hasMap.get(periodName) != null) {
                        continue;
                    }
                    int count = 0;
                    for (Visitor visitor1 : list) {
                        if (periodName.equals(visitor1.getName())) {
                            count += Integer.parseInt(visitor1.getCount());
                        }
                    }
                    visitor.setCount(count + "");
                    map.put(periodName, visitor);
                    hasMap.put(periodName, "has");
                }
                ArrayList<Integer> yList = new ArrayList<Integer>();
                for (String pr : pList) {
                    if (map.get(pr) == null) {
                        yList.add(0);
                    } else {
                        yList.add(Integer.parseInt(map.get(pr).getCount()));
                    }
                }
                json.setResult(yList);
                json.setList(pList);
                return json;
            } else {
                if (betweenDay < 90) {
                    list = visitorService.getByPeriod(startDate, endDate, employ.getEnterpriseId(), 1);
                } else {
                    list = visitorService.getByPeriod(startDate, endDate, employ.getEnterpriseId(), 2);
                }

            }
        } else {
            if (betweenDay < 90) {
                list = visitorService.getByPeriod(startDate, endDate, slaveEnterpriseId, 1);
            } else {
                list = visitorService.getByPeriod(startDate, endDate, slaveEnterpriseId, 2);
            }
        }
        PrebookPeriod prebookPeriod = new PrebookPeriod();
        if (slaveEnterpriseId > 0) {
            prebookPeriod.setEnterpriseId(slaveEnterpriseId);
        } else {
            prebookPeriod.setEnterpriseId(employ.getEnterpriseId());
        }
        prebookPeriod.setStatus(1);
        List<PrebookPeriod> periodList = prebookService.listAll(prebookPeriod);
        List<String> pList = new ArrayList<String>();
        for (PrebookPeriod p : periodList) {
            pList.add(p.getName());
        }

        HashMap<String, Visitor> map = new HashMap<>();
        for (Visitor visitor : list) {
            map.put(visitor.getName(), visitor);
        }
        ArrayList<Integer> yList = new ArrayList<Integer>();
        for (String pr : pList) {
            if (map.get(pr) == null) {
                yList.add(0);
            } else {
                yList.add(Integer.parseInt(map.get(pr).getCount()));
            }
        }
        json.setResult(yList);
        json.setList(pList);
        return json;
    }

    @RequestMapping("/facedatalist")
    public ModelAndView facedata(@RequestParam(defaultValue = "0") int id, String startDate, String endDate,
                                 @RequestParam(defaultValue = "1") int pageNum) {
        ModelAndView mav = new ModelAndView("facedata_list");
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (null == startDate || null == endDate) {

            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("enterpriseId", employ.getEnterpriseId());
        PageBean<Visitor> list = visitorService.getPageFacelist(params, pageNum, Constant.PAGE_SIZE);
        mav.addObject("timeOff", "1");
        mav.addObject("pageBean", list);
        mav.addObject("startDate", startDate);
        mav.addObject("endDate", endDate);
        return mav;
    }

    @RequestMapping("load-verification-type")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse loadVerificationType(String startDate, String endDate,
                                             @RequestParam(defaultValue = "0") int informationId) {
        JsonResponse jp = this.success();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToStr(LocalDate.now());
            endDate = startDate;
        }
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
        }
        List<TicketOrderRelation> ticketOrderRelationList = this.ticketOrderRelationService
                .loadVerificationType(startDate, endDate, informationId, employ.getEnterpriseId(), 0);
        HashMap<String, TicketOrderRelation> map = new HashMap<>();
        for (TicketOrderRelation ticketOrderRelation : ticketOrderRelationList) {
            map.put(DateUtils.dateToStr(ticketOrderRelation.getUpdateTime(), "yyyy-MM-dd"), ticketOrderRelation);
        }
        ArrayList<Integer> yList = new ArrayList<Integer>();
        ArrayList<String> xList = new ArrayList<String>();
        for (TicketOrderRelation ticketOrderRelation : ticketOrderRelationList) {
            switch (ticketOrderRelation.getVerificationType()) {
                case 0:
                    xList.add("未检票");
                    break;
                case 1:
                    xList.add("电子检票");
                    break;
                case 2:
                    xList.add("纸质检票");
                    break;

                default:
                    break;
            }
            yList.add(ticketOrderRelation.getQuantity());
        }
        jp.setResult(yList);
        jp.setList(xList);
        return jp;
    }

    @RequestMapping("load-verification-hour")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse loadVerificationHour(Date startDate, Date endDate,
                                             @RequestParam(defaultValue = "0") int informationId) {
        JsonResponse jp = this.success();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToDate(LocalDate.now());
            endDate = startDate;
        }
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
        }
        List<TicketOrderRelation> ticketOrderRelationList = this.ticketOrderRelationService
                .loadVerificationHour(startDate, endDate, informationId, employ.getEnterpriseId(), 0);
        ArrayList<String> xList = new ArrayList<String>();
        ArrayList<Integer> yList = new ArrayList<Integer>();
        for (TicketOrderRelation ticketOrderRelation : ticketOrderRelationList) {
            xList.add(ticketOrderRelation.getHourDate());
            yList.add(ticketOrderRelation.getQuantity());
        }
        jp.setResult(yList);
        jp.setList(xList);
        return jp;
    }

    @RequestMapping("load-verification-user")
    @ResponseBody
    @IgnoreAuth(requireLogin = true)
    public JsonResponse loadVerificationUser(Date startDate, Date endDate,
                                             @RequestParam(defaultValue = "0") int informationId) {
        JsonResponse jp = this.success();
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToDate(LocalDate.now());
            endDate = startDate;
        }
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
        }
        List<TicketOrderRelation> ticketOrderRelationList = this.ticketOrderRelationService
                .loadVerificationUser(startDate, endDate, informationId, employ.getEnterpriseId(), 0);
        ArrayList<String> xList = new ArrayList<String>();
        ArrayList<Integer> yList = new ArrayList<Integer>();
        for (TicketOrderRelation ticketOrderRelation : ticketOrderRelationList) {
            xList.add(ticketOrderRelation.getUserName());
            yList.add(ticketOrderRelation.getQuantity());
        }
        jp.setResult(yList);
        jp.setList(xList);
        return jp;
    }

    /**
     * 核销列表
     * type 区分开 1-展览 2-活动 3-讲解
     */
    @RequestMapping("informationList")
    @ResponseBody
    public JsonResponse informationList(@RequestParam(defaultValue = "1") int enterpriseId,
                                        @RequestParam(defaultValue = "1") int informationType) {
        List<TicketInformation> infoList = this.ticketInformationService.listByEnterpriseId(enterpriseId, informationType);
        return this.success(infoList);
    }

    /**
     * 核销列表
     * type 区分开 1-展览
     */
    @RequestMapping("exhibition-verification-list")
    public ModelAndView exhibitionVerificationList(String orderSerial, @RequestParam(defaultValue = "1") int pageNum,
                                                   @RequestParam(defaultValue = "-1") int informationId, Date startDate, Date endDate,
                                                   @RequestParam(defaultValue = "0") int enterpriseId,String identification) {
        ModelAndView mav = new ModelAndView("admin/verification/exhibition_verification_list");
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
//        if (informationId > 0) {
//            TicketInformation information = ticketInformationService.selectById(informationId);
//            if (information == null || information.getEnterpriseId() != employ.getEnterpriseId()) {
//                return new ModelAndView("redirect:/auth/no-auth.htm");
//            }
//        }

        if (enterpriseId == 0) {
            enterpriseId = employ.getEnterpriseId();
        }
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("orderSerial", orderSerial);
        params.put("isVerification", 1);
        params.put("informationType", 1);
        List<TicketInformation> infoList = this.ticketInformationService.listAllBySort(enterpriseId, 1);
        if (informationId == -1) {
            if (infoList != null && infoList.size() > 0) {
                informationId = infoList.get(0).getId();
            } else {
                informationId = 0;
            }
        }

        //数据量过大导致列表加载很慢,给个预约时间默认值
        if (startDate == null) {
            String startDateStr = CalfDateUtil.localDateToStr(LocalDate.now().minusDays(6));
            params.put("startViewDate", startDateStr);
            mav.addObject("startDate", DateUtils.strToDate(startDateStr + " 00:00:00"));

        }
        if (endDate == null) {
            String endDateStr = CalfDateUtil.localDateToStr(LocalDate.now());
            params.put("endViewDate",endDateStr);
            mav.addObject("endDate",  DateUtils.strToDate(endDateStr + " 00:00:00"));
        }

        params.put("informationId", informationId);
        mav.addObject("orderSerial", orderSerial);
        if (startDate != null) {
            params.put("startViewDate", DateUtils.dateToStr(startDate, "yyy-MM-dd"));
            mav.addObject("startDate", startDate);
        }
        if (endDate != null) {
            params.put("endViewDate", DateUtils.dateToStr(endDate, "yyy-MM-dd"));
            mav.addObject("endDate", endDate);
        }
        mav.addObject("informationId", informationId);

        params.put("saleOrderSerial", identification);

        params.put("enterpriseId", enterpriseId);
        mav.addObject("enterpriseId", enterpriseId);
        PageBean<TicketOrderRelation> page = this.ticketOrderRelationService.listVerificationByPage(params, pageNum, Constant.PAGE_SIZE);
        List<Enterprise> enterpriseList = employEnterpriseAuthService.enterpriseListByEmployId(employ.getId());
        mav.addObject("enterpriseList", enterpriseList);
        mav.addObject("identification", identification);
        mav.addObject("pageBean", page);
        mav.addObject("infoList", infoList);
        mav.addObject("informationId", informationId);
        return mav;
    }

    /**
     * 核销列表
     * type 区分开 2-活动
     */
    @RequestMapping("activity-verification-list")
    public ModelAndView activityVerificationList(String orderSerial, @RequestParam(defaultValue = "1") int pageNum,
                                                 @RequestParam(defaultValue = "-1") int informationId, Date startDate, Date endDate,
                                                 @RequestParam(defaultValue = "1") int enterpriseId) {
        ModelAndView mav = new ModelAndView("admin/verification/activity_verification_list");
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
//        if (informationId > 0) {
//            TicketInformation information = ticketInformationService.selectById(informationId);
//            if (information == null || information.getEnterpriseId() != employ.getEnterpriseId()) {
//                return new ModelAndView("redirect:/auth/no-auth.htm");
//            }
//        }
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("orderSerial", orderSerial);
        params.put("isVerification", 1);
        params.put("informationType", 2);
        List<TicketInformation> infoList = this.ticketInformationService.listByEnterpriseId(enterpriseId, 2);
        if (informationId == -1) {
            if (infoList != null && infoList.size() > 0) {
                informationId = infoList.get(0).getId();
            } else {
                informationId = 0;
            }
        }
        //数据量过大导致列表加载很慢,给个预约时间默认值
        if (startDate == null) {
            String startDateStr = CalfDateUtil.localDateToStr(LocalDate.now().minusDays(6));
            params.put("startViewDate", startDateStr);
            mav.addObject("startDate", DateUtils.strToDate(startDateStr + " 00:00:00"));

        }
        if (endDate == null) {
            String endDateStr = CalfDateUtil.localDateToStr(LocalDate.now());
            params.put("endViewDate",endDateStr);
            mav.addObject("endDate",  DateUtils.strToDate(endDateStr + " 00:00:00"));
        }
        params.put("informationId", informationId);
        mav.addObject("orderSerial", orderSerial);
        if (startDate != null) {
            params.put("startViewDate", DateUtils.dateToStr(startDate, "yyy-MM-dd"));
            mav.addObject("startDate", startDate);
        }
        if (endDate != null) {
            params.put("endViewDate", DateUtils.dateToStr(endDate, "yyy-MM-dd"));
            mav.addObject("endDate", endDate);
        }
        mav.addObject("informationId", informationId);

        params.put("enterpriseId", enterpriseId);
        mav.addObject("enterpriseId", enterpriseId);
        PageBean<TicketOrderRelation> page = this.ticketOrderRelationService.listVerificationByPage(params, pageNum, Constant.PAGE_SIZE);
        List<Enterprise> enterpriseList = employEnterpriseAuthService.enterpriseListByEmployId(employ.getId());
        mav.addObject("enterpriseList", enterpriseList);
        mav.addObject("pageBean", page);
        mav.addObject("infoList", infoList);
        mav.addObject("informationId", informationId);
        return mav;
    }

    /**
     * 核销列表
     * type 区分开 3-讲解预约
     */
    @RequestMapping("explain-verification-list")
    public ModelAndView explainVerificationList(String orderSerial, @RequestParam(defaultValue = "1") int pageNum,
                                                @RequestParam(defaultValue = "-1") int informationId, Date startDate, Date endDate,
                                                @RequestParam(defaultValue = "1") int enterpriseId) {
        ModelAndView mav = new ModelAndView("admin/verification/explain_verification_list");
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
//        if (informationId > 0) {
//            TicketInformation information = ticketInformationService.selectById(informationId);
//            if (information == null || information.getEnterpriseId() != employ.getEnterpriseId()) {
//                return new ModelAndView("redirect:/auth/no-auth.htm");
//            }
//        }
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("orderSerial", orderSerial);
        params.put("isVerification", 1);
        params.put("informationType", 3);
        List<TicketInformation> infoList = this.ticketInformationService.listByEnterpriseId(enterpriseId, 3);
        if (informationId == -1) {
            if (infoList != null && infoList.size() > 0) {
                informationId = infoList.get(0).getId();
            } else {
                informationId = 0;
            }
        }
        //数据量过大导致列表加载很慢,给个预约时间默认值
        if (startDate == null) {
            String startDateStr = CalfDateUtil.localDateToStr(LocalDate.now().minusDays(6));
            params.put("startViewDate", startDateStr);
            mav.addObject("startDate", DateUtils.strToDate(startDateStr + " 00:00:00"));

        }
        if (endDate == null) {
            String endDateStr = CalfDateUtil.localDateToStr(LocalDate.now());
            params.put("endViewDate",endDateStr);
            mav.addObject("endDate",  DateUtils.strToDate(endDateStr + " 00:00:00"));
        }
        params.put("informationId", informationId);
        mav.addObject("orderSerial", orderSerial);
        if (startDate != null) {
            params.put("startViewDate", DateUtils.dateToStr(startDate, "yyy-MM-dd"));
            mav.addObject("startDate", startDate);
        }
        if (endDate != null) {
            params.put("endViewDate", DateUtils.dateToStr(endDate, "yyy-MM-dd"));
            mav.addObject("endDate", endDate);
        }
        mav.addObject("informationId", informationId);

        params.put("enterpriseId", enterpriseId);
        mav.addObject("enterpriseId", enterpriseId);
        PageBean<TicketOrderRelation> page = this.ticketOrderRelationService.listVerificationByPage(params, pageNum, Constant.PAGE_SIZE);
        List<Enterprise> enterpriseList = employEnterpriseAuthService.enterpriseListByEmployId(employ.getId());
        mav.addObject("enterpriseList", enterpriseList);
        mav.addObject("pageBean", page);
        mav.addObject("infoList", infoList);
        mav.addObject("informationId", informationId);
        return mav;
    }

    /**
     * 核销列表
     *
     * @Title: list-verification
     * @Description: TODO
     * @Param @return
     * @Return ModelAndView
     * @Throws
     */
    @RequestMapping("list-verification")
    public ModelAndView listOrder(String orderSerial, @RequestParam(defaultValue = "1") int pageNum,
                                  @RequestParam(defaultValue = "-1") int informationId, Date startDate, Date endDate) {
        ModelAndView mav = new ModelAndView("admin/verification_list");
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
            if (information == null || information.getEnterpriseId() != employ.getEnterpriseId()) {
                return new ModelAndView("redirect:/auth/no-auth.htm");
            }
        }
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("orderSerial", orderSerial);
        params.put("isVerification", 1);

        List<TicketInformation> infoList = this.ticketInformationService.listByEnterpriseId(employ.getEnterpriseId(),
                0);
        if (informationId == -1) {
            if (infoList != null && infoList.size() > 0) {
                informationId = infoList.get(0).getId();
            } else {
                informationId = 0;
            }
        }
        //数据量过大导致列表加载很慢,给个预约时间默认值
        if (startDate == null) {
            String startDateStr = CalfDateUtil.localDateToStr(LocalDate.now().minusDays(6));
            params.put("startViewDate", startDateStr);
            mav.addObject("startDate", DateUtils.strToDate(startDateStr + " 00:00:00"));

        }
        if (endDate == null) {
            String endDateStr = CalfDateUtil.localDateToStr(LocalDate.now());
            params.put("endViewDate",endDateStr);
            mav.addObject("endDate",  DateUtils.strToDate(endDateStr + " 00:00:00"));
        }
        params.put("informationId", informationId);
        mav.addObject("orderSerial", orderSerial);
        if (startDate != null) {
            params.put("startViewDate", DateUtils.dateToStr(startDate, "yyy-MM-dd"));
            mav.addObject("startDate", startDate);
        }
        if (endDate != null) {
            params.put("endViewDate", DateUtils.dateToStr(endDate, "yyy-MM-dd"));
            mav.addObject("endDate", endDate);
        }
        mav.addObject("informationId", informationId);

        params.put("enterpriseId", employ.getEnterpriseId());
        mav.addObject("enterpriseId", employ.getEnterpriseId());
        PageBean<TicketOrderRelation> page = this.ticketOrderRelationService.listVerificationByPage(params, pageNum,
                Constant.PAGE_SIZE);
        mav.addObject("pageBean", page);
        mav.addObject("infoList", infoList);
        mav.addObject("informationId", informationId);
        return mav;
    }

    @RequestMapping("ticket-verification-excel")
    @IgnoreAuth(requireLogin = true)
    public ResponseEntity<byte[]> ticketVerificationExcel(HttpServletResponse response, HttpServletRequest request,
                                                          String orderSerial, @RequestParam(defaultValue = "0") int informationId, Date startDate, Date endDate)
            throws IOException {

        ClassLoader classLoader = getClass().getClassLoader();
        URL url = classLoader.getResource("excel/excel-template/ticket_verification_template.xls");
        URL url2 = classLoader.getResource("excel/ticket_verification_temp.xls");
        String num = RandomUtils.generateRandomNumber(5);
        new File(url2.getPath().replace(".xls", num + ".xls"));
        InputStream in = new FileInputStream(url.getPath());
        FileOutputStream out = new FileOutputStream(url2.getPath().replace(".xls", num + ".xls"));
        ExcelBean excelBean = new ExcelBean();
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("orderSerial", orderSerial);
        params.put("informationId", informationId);
        params.put("isVerification", 1);

        //数据量过大导致列表加载很慢,给个预约时间默认值
        if (startDate == null) {
            String startDateStr = CalfDateUtil.localDateToStr(LocalDate.now().minusDays(6));
            params.put("startViewDate", startDateStr);
        }
        if (endDate == null) {
            String endDateStr = CalfDateUtil.localDateToStr(LocalDate.now());
            params.put("endViewDate",endDateStr);
        }

        if (startDate != null) {
            params.put("startViewDate", DateUtils.dateToStr(startDate, "yyy-MM-dd"));
        }
        if (endDate != null) {
            params.put("endViewDate", DateUtils.dateToStr(endDate, "yyy-MM-dd"));
        }

        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        params.put("enterpriseId", employ.getEnterpriseId());

        PageBean<TicketOrderRelation> page = this.ticketOrderRelationService.listVerificationByPage(params, 1,
                Integer.MAX_VALUE);
        List<TicketOrderRelation> list = page.getResult();
        for (TicketOrderRelation r : list) {
            r.setHourDate(DateUtils.dateToStr(r.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
            if (r.getIsVerification() == 1) {
                r.setIsVerificationStr("核销成功");
            }

            if (r.getVerificationType() == 1) {
                r.setVerificationTypeName("电子核销");
            } else if (r.getVerificationType() == 2) {
                r.setVerificationTypeName("门票核销");
            }
        }

        try {
            Context context = new Context();
            excelBean.setListData(list);
            context.putVar("excelBean", excelBean);
            JxlsHelper.getInstance().processTemplate(in, out, context);
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        File f = null;
        f = new File(url2.getPath().replace(".xls", num + ".xls")); // 将服务器上的文件下载下来
        HttpHeaders headers = new HttpHeaders();
        String fileName = new String("核销记录.xls".getBytes("UTF-8"), "iso-8859-1");
        headers.setContentType(MediaType.valueOf("application/x-excel;charset=UTF-8"));
        headers.add("Content-Disposition", "attachment; filename=" + fileName);
        headers.add("Content-Length", String.valueOf(f.length()));
        byte[] b = FileUtils.readFileToByteArray(f);
        if (f.exists())
            f.delete();
        return new ResponseEntity<byte[]>(b, headers, HttpStatus.OK);

    }

    /**
     * 核销列表
     *
     * @Title: verification-analysis
     * @Description: TODO
     * @Param @return
     * @Return ModelAndView
     * @Throws
     */
    @RequestMapping("verification-analysis")
    public ModelAndView verificationAnalysis(@RequestParam(defaultValue = "0") int informationId, Date startDate,
                                             Date endDate) {
        ModelAndView mav = new ModelAndView("admin/verification_analysis");
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();
        if (informationId > 0) {
            TicketInformation information = ticketInformationService.selectById(informationId);
            if (information == null || information.getEnterpriseId() != employ.getEnterpriseId()) {
                return new ModelAndView("redirect:/auth/no-auth.htm");
            }
        }
        List<TicketInformation> infoList = this.ticketInformationService.listByEnterpriseId(employ.getEnterpriseId(),
                1);
        if (null == startDate || null == endDate) {
            startDate = CalfDateUtil.localDateToDate(LocalDate.now());
            endDate = startDate;
        }
        mav.addObject("startDate", startDate);
        mav.addObject("endDate", endDate);
        mav.addObject("infoList", infoList);
        mav.addObject("informationId", informationId);
        return mav;
    }


    @RequestMapping("export-analysis-data")
    @IgnoreAuth(requireLogin = true)
    public ResponseEntity<byte[]> exportAnalysisData(HttpServletResponse response,
                                                     String startDate,
                                                     String endDate,
                                                     @RequestParam(defaultValue = "0") int informationId,
                                                     @RequestParam(defaultValue = "0") int informationType,
                                                     @RequestParam(defaultValue = "0") int slaveEnterpriseId) throws IOException {
        TicketEmploy employ = (TicketEmploy) SessionContextUtils.getLoginObject();

        // 创建Excel工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();

        // 创建sheet1 - 基础信息
        HSSFSheet sheet1 = workbook.createSheet("基础数据");

        // 设置列宽
        sheet1.setColumnWidth(0, 20 * 256);
        sheet1.setColumnWidth(1, 20 * 256);

        // 创建标题样式
        HSSFCellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        HSSFFont titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleStyle.setFont(titleFont);

        // 获取基础数据
        Map<String, Object> data = new HashMap<>();
        data.put("startDate", startDate);
        data.put("endDate", endDate);
        data.put("informationType", informationType);
        data.put("informationId", informationId);
        Integer enterpriseId = slaveEnterpriseId > 0 ? slaveEnterpriseId : employ.getEnterpriseId();
        data.put("slaveEnterpriseId", enterpriseId);

        // 获取订单统计数据
        Map<String, Object> orderStats = ticketOrderService.getOrderStatisticsData(startDate, endDate,
                enterpriseId, informationId, informationType);

        // 获取检票数据
        List<TicketOrderRelation> ticketOrderRelationList = ticketOrderRelationService
                .loadVerificationType(startDate, endDate, informationId, enterpriseId, 0);

        // 写入sheet1数据
        int rowNum = 0;


        // 写入日期范围
        HSSFRow sheet1DateRow = sheet1.createRow(rowNum++);
        sheet1DateRow.createCell(0).setCellValue("统计日期");
        sheet1DateRow.createCell(1).setCellValue(startDate + " 至 " + endDate);
        sheet1DateRow.getCell(0).setCellStyle(titleStyle);
        sheet1DateRow.getCell(1).setCellStyle(titleStyle);
        //空一行
        rowNum++;

        // 写入订单数据标题
        HSSFRow orderTitleRow = sheet1.createRow(rowNum++);
        orderTitleRow.createCell(0).setCellValue("订单数据");
        orderTitleRow.getCell(0).setCellStyle(titleStyle);

        String[][] baseData = {
                {"订单数", orderStats.get("orderCount") != null ? String.valueOf(orderStats.get("orderCount")) : "0"},
                {"境外购票量", orderStats.get("passportCount") != null ? String.valueOf(orderStats.get("passportCount")) : "0"},
                {"购票量", orderStats.get("ticketQuantity") != null ? String.valueOf(orderStats.get("ticketQuantity")) : "0"},
                {"取票数", orderStats.get("ssTicketQuantity") != null ? String.valueOf(orderStats.get("ssTicketQuantity")) : "0"},
                {"退票数", orderStats.get("refundTicketQuantity") != null ? String.valueOf(orderStats.get("refundTicketQuantity")) : "0"}
        };

        // 写入基础数据
        for (String[] rowData : baseData) {
            HSSFRow row = sheet1.createRow(rowNum++);
            row.createCell(0).setCellValue(rowData[0]);
            row.createCell(1).setCellValue(rowData[1]);
        }
        // 写入检票数据标题
        HSSFRow titleRow = sheet1.createRow(rowNum++);
        titleRow.createCell(0).setCellValue("检票数据");
        titleRow.getCell(0).setCellStyle(titleStyle);

        // 写入检票数据
        for (TicketOrderRelation relation : ticketOrderRelationList) {
            HSSFRow row = sheet1.createRow(rowNum++);
            String verificationType = "";
            Integer type = relation.getVerificationType();
            if (type != null) {
                switch (type) {
                    case 0:
                        verificationType = "未检票";
                        break;
                    case 1:
                        verificationType = "电子检票";
                        break;
                    case 2:
                        verificationType = "纸质检票";
                        break;
                    default:
                        verificationType = "未知";
                }
            } else {
                verificationType = "未知";
            }
            row.createCell(0).setCellValue(verificationType);
            row.createCell(1).setCellValue(relation.getQuantity() != 0 ? relation.getQuantity() : 0);
        }

        // 创建sheet2 - 订单统计
        HSSFSheet sheet2 = workbook.createSheet("订单统计");
        // 设置列宽
        sheet2.setColumnWidth(0, 20 * 256);
        sheet2.setColumnWidth(1, 20 * 256);
        sheet2.setColumnWidth(2, 20 * 256);
        sheet2.setColumnWidth(3, 20 * 256);
        sheet2.setColumnWidth(4, 20 * 256);  // 新增检票数量列宽

        // 获取订单统计和退款数据
        JsonResponse orderStatisticsResp = loadOrderStatistics(startDate, endDate, informationId, informationType, slaveEnterpriseId);
        List<Object[]> statisticsList = (List<Object[]>) orderStatisticsResp.getList();
        List<Object[]> refundlist = (List<Object[]>) orderStatisticsResp.getReturnMap().get("refundlist");

        // 将退款数据转换为Map便于查找
        Map<String, Object[]> refundMap = new HashMap<>();
        for (Object[] refundData : refundlist) {
            if (refundData[0] != null) {
                refundMap.put(refundData[0].toString(), refundData);
            }
        }
        // 获取每日检票数据
        JsonResponse verificationResp = loadVerificationDay(
                CalfDateUtil.strToDate(startDate),
                CalfDateUtil.strToDate(endDate),
                informationId,
                informationType,
                enterpriseId);
        List<String> dateList = (List<String>) verificationResp.getList();
        List<Integer> verificationCounts = (List<Integer>) verificationResp.getResult();

        // 将检票数据转换为Map便于查找
        Map<String, Integer> verificationMap = new HashMap<>();
        for (int i = 0; i < dateList.size(); i++) {
            verificationMap.put(dateList.get(i), verificationCounts.get(i));
        }

        // 写入sheet2标题行
        HSSFRow headerRow = sheet2.createRow(0);
        headerRow.createCell(0).setCellValue("日期");
        headerRow.createCell(1).setCellValue("订单数");
        headerRow.createCell(2).setCellValue("销售金额");
        headerRow.createCell(3).setCellValue("退款金额");
        headerRow.createCell(4).setCellValue("检票数量");
        headerRow.getCell(0).setCellStyle(titleStyle);
        headerRow.getCell(1).setCellStyle(titleStyle);
        headerRow.getCell(2).setCellStyle(titleStyle);
        headerRow.getCell(3).setCellStyle(titleStyle);
        headerRow.getCell(4).setCellStyle(titleStyle);

        // 写入sheet2数据
        int dataRowNum = 1;
        for (Object[] saleData : statisticsList) {
            HSSFRow row = sheet2.createRow(dataRowNum++);
            String date = saleData[0] != null ? saleData[0].toString() : "";
            row.createCell(0).setCellValue(date);
            // 获取该日期的详细数据
            Map<String, Object> detailData = ticketOrderService.getOrderStatisticsData(date, date,
                    slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), 
                    informationId, 
                    informationType);
            // 写入订单数
            row.createCell(1).setCellValue(detailData.get("orderCount") != null ? 
                    Integer.parseInt(detailData.get("orderCount").toString()) : 0);
            // 写入销售金额 - 使用statisticsList中的数据，因为这个是销售金额数据
            row.createCell(2).setCellValue(saleData[1] != null ? Double.parseDouble(saleData[1].toString()) : 0.0);
            // 写入退款金额
            Object[] refundData = refundMap.get(date);
            row.createCell(3).setCellValue(refundData != null && refundData[1] != null ? 
                    Double.parseDouble(refundData[1].toString()) : 0.0);
            // 写入检票数量
            row.createCell(4).setCellValue(verificationMap.getOrDefault(date, 0));
        }
        // 创建sheet3 - 票务分析
        HSSFSheet sheet3 = workbook.createSheet("票务分析");

        // 设置列宽
        sheet3.setColumnWidth(0, 20 * 256);
        sheet3.setColumnWidth(1, 20 * 256);

        // 写入统计日期
        int sheet3RowNum = 0;
        HSSFRow sheet3DateRow = sheet3.createRow(sheet3RowNum++);
        sheet3DateRow.createCell(0).setCellValue("统计日期");
        sheet3DateRow.createCell(1).setCellValue(startDate + " 至 " + endDate);
        sheet3DateRow.getCell(0).setCellStyle(titleStyle);
        sheet3DateRow.getCell(1).setCellStyle(titleStyle);

        // 空一行
        sheet3RowNum++;

        // 写入地域购票数据标题
        HSSFRow areaTitle = sheet3.createRow(sheet3RowNum++);
        areaTitle.createCell(0).setCellValue("地域购票统计");
        areaTitle.getCell(0).setCellStyle(titleStyle);

        // 写入地域购票数据表头
        HSSFRow areaHeader = sheet3.createRow(sheet3RowNum++);
        areaHeader.createCell(0).setCellValue("地区");
        areaHeader.createCell(1).setCellValue("购票数量");
        areaHeader.getCell(0).setCellStyle(titleStyle);
        areaHeader.getCell(1).setCellStyle(titleStyle);

        // 写入地域购票数据
        List<OrderArea> areaList = ticketOrderService.getOrderByAreaList(startDate, endDate, enterpriseId,
                informationId, informationType, 0);
        for (OrderArea areaData : areaList) {
            HSSFRow row = sheet3.createRow(sheet3RowNum++);
            row.createCell(0).setCellValue(areaData.getUserArea());
            row.createCell(1).setCellValue(areaData.getTicketQuantity());
        }

        // 空一行
        sheet3RowNum++;

        // 写入性别购票数据标题
        HSSFRow genderTitle = sheet3.createRow(sheet3RowNum++);
        genderTitle.createCell(0).setCellValue("性别购票统计");
        genderTitle.getCell(0).setCellStyle(titleStyle);

        // 写入性别购票数据表头
        HSSFRow genderHeader = sheet3.createRow(sheet3RowNum++);
        genderHeader.createCell(0).setCellValue("性别");
        genderHeader.createCell(1).setCellValue("购票数量");
        genderHeader.getCell(0).setCellStyle(titleStyle);
        genderHeader.getCell(1).setCellStyle(titleStyle);

        // 写入性别购票数据
        // 写入性别购票数据
        Map<String, Object> sexCount = ticketOrderService.getSexCount(startDate, endDate, enterpriseId,
                informationId, informationType);

        // 写入男性数据
        if (Integer.valueOf(sexCount.get("manCount").toString()) > 0) {
            HSSFRow row = sheet3.createRow(sheet3RowNum++);
            row.createCell(0).setCellValue("男");
            row.createCell(1).setCellValue(Integer.valueOf(sexCount.get("manCount").toString()));
        }

        // 写入女性数据
        if (Integer.valueOf(sexCount.get("womenCount").toString()) > 0) {
            HSSFRow row = sheet3.createRow(sheet3RowNum++);
            row.createCell(0).setCellValue("女");
            row.createCell(1).setCellValue(Integer.valueOf(sexCount.get("womenCount").toString()));
        }

        // 写入未知性别数据
        if (Integer.valueOf(sexCount.get("visitCount").toString()) > 0) {
            HSSFRow row = sheet3.createRow(sheet3RowNum++);
            row.createCell(0).setCellValue("未知");
            row.createCell(1).setCellValue(Integer.valueOf(sexCount.get("visitCount").toString()));
        }

        // 空一行
        sheet3RowNum++;

        // 写入证件类型购票数据标题
        HSSFRow cardTitle = sheet3.createRow(sheet3RowNum++);
        cardTitle.createCell(0).setCellValue("证件类型购票统计");
        cardTitle.getCell(0).setCellStyle(titleStyle);

        // 写入证件类型购票数据表头
        HSSFRow cardHeader = sheet3.createRow(sheet3RowNum++);
        cardHeader.createCell(0).setCellValue("证件类型");
        cardHeader.createCell(1).setCellValue("购票数量");
        cardHeader.getCell(0).setCellStyle(titleStyle);
        cardHeader.getCell(1).setCellStyle(titleStyle);

        // 写入证件类型购票数据
        List<TicketUserRelation> cardList = ticketOrderService.getTicketCardTypeData(startDate, endDate, enterpriseId,
                informationId, informationType);
        for (TicketUserRelation cardData : cardList) {
            HSSFRow row = sheet3.createRow(sheet3RowNum++);
            String cardType = "";
            switch (cardData.getCardType()) {
                case 0:
                    cardType = "无证件";
                    break;
                case 1:
                    cardType = "身份证";
                    break;
                case 5:
                    cardType = "护照";
                    break;
                case 6:
                    cardType = "台胞";
                    break;
                case 7:
                    cardType = "港澳";
                    break;
                case 9:
                    cardType = "社保卡";
                    break;
                default:
                    cardType = "其他";
            }
            row.createCell(0).setCellValue(cardType);
            row.createCell(1).setCellValue(cardData.getAge()); // 使用age字段存储数量
        }

        // 空一行
        sheet3RowNum++;

        // 写入票价分析数据标题
        HSSFRow priceTitle = sheet3.createRow(sheet3RowNum++);
        priceTitle.createCell(0).setCellValue("票价分析统计");
        priceTitle.getCell(0).setCellStyle(titleStyle);

        // 写入票价分析数据表头
        HSSFRow priceHeader = sheet3.createRow(sheet3RowNum++);
        priceHeader.createCell(0).setCellValue("票种");
        priceHeader.createCell(1).setCellValue("购票数量");
        priceHeader.getCell(0).setCellStyle(titleStyle);
        priceHeader.getCell(1).setCellStyle(titleStyle);

        // 获取票价分析数据
        List<TicketOrderRelation> priceList = ticketOrderRelationService.getOrderByFullAndDiscountList(
                CalfDateUtil.strToDate(startDate),
                CalfDateUtil.strToDate(endDate),
                enterpriseId,
                informationId,
                informationType);

        // 写入票价分析数据
        for (TicketOrderRelation priceData : priceList) {
            HSSFRow row = sheet3.createRow(sheet3RowNum++);
            String ticketType = priceData.getTicketType() == 1 ? 
                    "全价票(" + priceData.getTicketPrice() + "元)" : 
                    "优惠票(" + priceData.getTicketPrice() + "元)";
            row.createCell(0).setCellValue(ticketType);
            row.createCell(1).setCellValue(priceData.getCountValue());
        }

        // 空一行
        sheet3RowNum++;

        // 写入民族购票数据标题
        HSSFRow nationTitle = sheet3.createRow(sheet3RowNum++);
        nationTitle.createCell(0).setCellValue("民族购票统计");
        nationTitle.getCell(0).setCellStyle(titleStyle);

        // 写入民族购票数据表头
        HSSFRow nationHeader = sheet3.createRow(sheet3RowNum++);
        nationHeader.createCell(0).setCellValue("民族");
        nationHeader.createCell(1).setCellValue("购票数量");
        nationHeader.getCell(0).setCellStyle(titleStyle);
        nationHeader.getCell(1).setCellStyle(titleStyle);

        // 获取民族购票数据
        List<TicketUserData> nationList = ticketUserDataService.getNationData(
                enterpriseId,
                informationId,
                startDate,
                endDate,
                informationType);

        // 写入民族购票数据
        for (TicketUserData nationData : nationList) {
            HSSFRow row = sheet3.createRow(sheet3RowNum++);
            row.createCell(0).setCellValue(nationData.getNation() != null ? nationData.getNation() : "未知");
            row.createCell(1).setCellValue(nationData.getQuantity());
        }

        // 空一行
        sheet3RowNum++;

        // 写入年龄范围数据标题
        HSSFRow ageTitle = sheet3.createRow(sheet3RowNum++);
        ageTitle.createCell(0).setCellValue("年龄范围统计");
        ageTitle.getCell(0).setCellStyle(titleStyle);

        // 写入年龄范围数据表头
        HSSFRow ageHeader = sheet3.createRow(sheet3RowNum++);
        ageHeader.createCell(0).setCellValue("年龄段");
        ageHeader.createCell(1).setCellValue("购票数量");
        ageHeader.getCell(0).setCellStyle(titleStyle);
        ageHeader.getCell(1).setCellStyle(titleStyle);

        // 获取年龄范围数据
        JsonResponse ageResponse = loadOrderStatisticsAge(startDate, endDate, 1, informationId, informationType, enterpriseId);
        Map<String, Object> ageData = ageResponse.getReturnMap();
        String[] ageRanges = (String[]) ageData.get("xAlias");
        Integer[] ageCounts = (Integer[]) ageData.get("yAlias");

        // 写入年龄范围数据
        for (int i = 0; i < ageRanges.length; i++) {
            HSSFRow row = sheet3.createRow(sheet3RowNum++);
            row.createCell(0).setCellValue(ageRanges[i]);
            row.createCell(1).setCellValue(ageCounts[i]);
        }

        // 空一行
        sheet3RowNum++;

        // 写入购票时段数据标题
        HSSFRow timeTitle = sheet3.createRow(sheet3RowNum++);
        timeTitle.createCell(0).setCellValue("购票时段统计");
        timeTitle.getCell(0).setCellStyle(titleStyle);

        // 写入购票时段数据表头
        HSSFRow timeHeader = sheet3.createRow(sheet3RowNum++);
        timeHeader.createCell(0).setCellValue("时段");
        timeHeader.createCell(1).setCellValue("购票数量");
        timeHeader.getCell(0).setCellStyle(titleStyle);
        timeHeader.getCell(1).setCellStyle(titleStyle);

        // 获取购票时段数据
        JsonResponse timeResponse = loadChartData_sjd_gpl(startDate, endDate, informationId, informationType, enterpriseId);
        List<ChartBean> timeList = (List<ChartBean>) timeResponse.getList();

        // 将时段数据转换为Map便于查找
        Map<Integer, Integer> timeMap = new HashMap<>();
        for (ChartBean timeData : timeList) {
            timeMap.put(timeData.getxValue(), timeData.getyValue());
        }

        // 写入购票时段数据
        for (int hour = 0; hour < 24; hour++) {
            HSSFRow row = sheet3.createRow(sheet3RowNum++);
            String timeRange = hour + "-" + (hour + 1) + "点";
            row.createCell(0).setCellValue(timeRange);
            row.createCell(1).setCellValue(timeMap.getOrDefault(hour, 0));
        }

        // 在购票时段数据后添加空行
        sheet3RowNum++;

        // 写入每单张数数据标题
        HSSFRow ticketNumTitle = sheet3.createRow(sheet3RowNum++);
        ticketNumTitle.createCell(0).setCellValue("每单张数统计");
        ticketNumTitle.getCell(0).setCellStyle(titleStyle);

        // 写入每单张数数据表头
        HSSFRow ticketNumHeader = sheet3.createRow(sheet3RowNum++);
        ticketNumHeader.createCell(0).setCellValue("张数");
        ticketNumHeader.createCell(1).setCellValue("订单数量");
        ticketNumHeader.getCell(0).setCellStyle(titleStyle);
        ticketNumHeader.getCell(1).setCellStyle(titleStyle);

        // 获取每单张数数据
        List<ChartBean> gpsData = this.ticketOrderService.loadChartData_gps_ddl(startDate, endDate,
                slaveEnterpriseId != 0 ? slaveEnterpriseId : employ.getEnterpriseId(), informationId, informationType);
        Map<Object, ChartBean> gpsMap = com.taoart.common.utils.CollectionUtils.toMap(gpsData, "xValue");

        // 写入1-15张的数据
        for (int i = 1; i < 16; i++) {
            HSSFRow row = sheet3.createRow(sheet3RowNum++);
            row.createCell(0).setCellValue(i + "张");
            
            ChartBean bean = gpsMap.get(new Integer(i));
            int value = (bean != null) ? bean.getyValue() : 0;
            row.createCell(1).setCellValue(value);
        }

        // 计算并写入16张及以上的数据
        int sum16 = 0;
        for (ChartBean item : gpsData) {
            if (item.getxValue() > 15) {
                sum16 += item.getyValue();
            }
        }
        HSSFRow lastRow = sheet3.createRow(sheet3RowNum++);
        lastRow.createCell(0).setCellValue("16张及以上");
        lastRow.createCell(1).setCellValue(sum16);

        // 生成文件
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        workbook.write(baos);

        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        String fileName = new String("数据分析报表.xls".getBytes("UTF-8"), "iso-8859-1");
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", fileName);

        return new ResponseEntity<>(baos.toByteArray(), headers, HttpStatus.OK);
    }
}

class MyComparator implements Comparator {

    public int compare(Object o1, Object o2) {
        Weather e1 = (Weather) o1;
        Weather e2 = (Weather) o2;
        if (Integer.valueOf(e1.getTime().replace("时", "")) > Integer.valueOf(e2.getTime().replace("时", ""))) {
            return 1;
        } else if (Integer.valueOf(e1.getTime().replace("时", "")) == Integer.valueOf(e2.getTime().replace("时", ""))) {
            return 0;
        } else {
            return -1;
        }
    }
}
