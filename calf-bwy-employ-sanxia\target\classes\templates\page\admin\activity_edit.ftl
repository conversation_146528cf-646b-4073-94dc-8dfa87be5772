<#include "layout.ftl"/>
<#macro content>
    <#include "/head.ftl" />
    <div class="main">
        <!--弹出层-->
        <div id="mask"></div>
        <div class="maskCnt">
            <div class="mk_box">
                <a href="javascript:void(0)" class="mk_del"><i class="iconList"></i></a>
                <div class="mk_bx_cnt">
                </div>
            </div>
        </div>
        <!--#弹出层#-->
        <#include "left_menu.ftl" />
        <div class="right">
            <div class="pisitionDiv">
                <#if information??>
                    <#if readFlag == 1>
                        <span class="pn_tle">查看活动</span>
                    <#else>
                        <span class="pn_tle">更新活动</span>
                    </#if>
                <#else>
                    <span class="pn_tle">新建活动</span>
                </#if>

                <a href="${rootPath}/ticket/information/activity-list.htm" class="return pull-right" ><i class="smallIconList op_return_icon"></i>返回</a>
            </div>
            <form  method="post" enctype="multipart/form-data" id="thisForm">
                <table cellpadding="0" cellspacing="0" border="0" width="100%" class="edit_tbl" >
                    <tr>
                        <td  width="100" height="50"><label class="inputTle">活动名称：</label></td><td>
                            <#if readFlag != 1>
                                <input id="name" name="name" class="input_570_33 required" value="${information.name}"/>
                            <#else>
                                ${information.name}
                            </#if>
                        </td>
                    </tr>
                    <#if isvolunteer == 1>
                        <input type="hidden" id="enterpriseId" name="enterpriseId" value="1">
                    <#else>
                        <tr>
                            <td  width="150" height="70"><label class="inputTle">所属场馆：</label></td><td>
                                <select class="input_240_33 required"  <#if information?? >disabled="disabled" readonly="readonly"</#if> id="enterpriseId" name="enterpriseId" data-error-id="data-roleId" data-msg-required="不能为空"  >
                                    <option value="">请选择</option>
                                    <#list enterpriseList as item>
                                        <option  value="${item.id}" <#if item.id==information.enterpriseId>selected</#if>>${item.name}</option>
                                    </#list>
                                </select>
                                <span class="erro_tip" id="data-roleId"></span>
                                <input type="hidden" id="informationDate">
                            </td>
                        </tr>
                    </#if>


                    <#if isvolunteer == 1>

                    <tr>
                        <td height="60"><label class="inputTle">分类：</label></td>
                        <td>
                            <#if readFlag != 1>
                                <label class="radio_lbl"><input  id="feature" name="feature"  value="3" <#if information.feature ==3> checked="true" </#if>  type="radio" />活动</label>
                                <label class="radio_lbl"><input type="radio"   id="feature" name="feature" value="5" <#if information.feature !=3> checked="true" </#if> />讲解</label>
                            <#else>
                                <#if information.feature=3>活动<#else>讲解</#if>
                            </#if>
                        </td>
                    </tr >
                    <#else>
                        <input type="hidden" id="feature" name="feature"  value="1"/>
                    </#if>
                    <tr>
                        <td  width="100" height="70"><label class="inputTle">开始时间：</label></td><td>
                            <#if readFlag != 1>
                                <input  class="input_240_33 Wdate required" id="startTime" name="startTime"   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endTime\');}',readOnly:true})"  value="<#if information.startTime??>${information.startTime?string('yyyy-MM-dd')}</#if>"  />
                            <#else>
                                <#if information.startTime??>${information.startTime?string('yyyy-MM-dd')}</#if>
                            </#if>
                        </td>
                    </tr>
                    <tr>
                        <td  width="100" height="70"><label class="inputTle">结束时间：</label></td><td>
                            <#if readFlag != 1>
                                <input  class="input_240_33 Wdate required" id="endTime" name="endTime" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startTime\');}',readOnly:true})"  value="<#if information.endTime??>${information.endTime?string('yyyy-MM-dd')}</#if>"  />
                            <#else>
                                <#if information.endTime??>${information.endTime?string('yyyy-MM-dd')}</#if>
                            </#if>
                        </td>
                    </tr>
                    <tr>
                        <td  width="100" height="70"><label class="inputTle">招募时间：</label></td><td>
                            <#if readFlag != 1>
                                <input  class="input_240_33 Wdate required" id="startSaleTime" name="startSaleTime"   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',readOnly:true})"  value="<#if information.startSaleTime??>${information.startSaleTime?string('yyyy-MM-dd HH:mm:ss')}</#if>"  />
                            <#else>
                                <#if information.startSaleTime??>${information.startSaleTime?string('yyyy-MM-dd HH:mm:ss')}</#if>
                            </#if>
                        </td>
                    </tr>
                    <tr>
                        <td  width="100" height="70"><label class="inputTle">截止招募时间：</label></td><td>
                            <#if readFlag != 1>
                                <input  class="input_240_33 Wdate required" id="endSaleTime" name="endSaleTime" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',readOnly:true})"  value="<#if information.endSaleTime??>${information.endSaleTime?string('yyyy-MM-dd HH:mm:ss')}</#if>"  />
                            <#else>
                                <#if information.endSaleTime??>${information.endSaleTime?string('yyyy-MM-dd HH:mm:ss')}</#if>
                            </#if>
                        </td>
                    </tr>



                    <tr>
                        <td  width="100" height="70"><label class="inputTle">定时发布：</label></td><td>
                            <#if readFlag != 1>
                                <input  class="input_240_33 Wdate" id="timingTime" name="timingTime" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',readOnly:true})"  value="<#if information.timingTime??>${information.timingTime?string('yyyy-MM-dd HH:mm:ss')}</#if>"  />
                            <#else>
                                <#if information.timingTime??>${information.timingTime?string('yyyy-MM-dd HH:mm:ss')}</#if>
                            </#if>
                        </td>
                    </tr>


                    <tr>
                        <td height="70"><label class="inputTle">地<ins class="three-word"></ins>址：</label></td><td>
                            <#if readFlag != 1>
                                <input id="address" name="address" class="input_570_33 required" value="${information.address}"/>
                            <#else>
                                ${information.address}
                            </#if>
                        </td>
                    </tr>
                    <tr>
                        <td height="120" valign="top"><label class="inputTle lh18">app图片:<br/>(列表使用)<br /> <font size="1" color="#666;">600*345px</font></label></td>
                        <td valign="top">
                            <#if readFlag != 1>
                                <ul class="upload_img ruku_certificate" id="upload_app">
                                    <li class="upload_add" id="upload_app_add" <#if information.appPic> style="display:none" </#if>><a href="javascript:void(0)" onclick="openFileInput('fileImg1');"  >+</a><input type="file" class="hidden" id="fileImg1" onchange="app_upload()"  /></li>
                                    <#if information.appPic??>
                                        <li><a href="javascript:void(0)" ><div class="imgbox"><img id="appPic" src="${picPath}${information.appPic}" data-src='${information.appPic}' class="smallimg uploadApp" /></div><i class="icon delIcon"  onclick="removeApp(this)"></i></a></li>
                                    </#if>
                                </ul>
                            <#else>
                                <img class="pic" src="<@imgSize '${picPath}${information.appPic}' , '@178_178' />"/>
                            </#if>
                        </td>
                    </tr>
                    <tr>
                        <td height="120" valign="top"><label class="inputTle lh18">banner图片:<br/>(详情使用)<br /> <font size="1" color="#666;">1920*445px</font></label></td>
                        <td valign="top">
                            <#if readFlag != 1>
                                <ul class="upload_img ruku_certificate" id="upload_banner">
                                    <li class="upload_add" id="upload_banner_add" <#if information.bannerPic> style="display:none" </#if>><a href="javascript:void(0)" onclick="openFileInput('fileImg2');"  >+</a><input type="file" class="hidden" id="fileImg2" onchange="banner_upload()"  /></li>
                                    <#if information.bannerPic??>
                                        <li><a href="javascript:void(0)" ><div class="imgbox"><img id="bannerPic" src="${picPath}${information.bannerPic}" data-src='${information.bannerPic}' class="smallimg uploadBanner" /></div><i class="icon delIcon"  onclick="removeBanner(this)"></i></a></li>
                                    </#if>
                                </ul>
                            <#else>
                                <img class="pic" src="<@imgSize '${picPath}${information.bannerPic}' , '@178_178' />"/>
                            </#if>
                        </td>
                    </tr>
                    <tr>
                        <td height="130" valign="top"><label class="inputTle lh18">简<ins class="three-word"></ins>介：</label></td>
                        <td  valign="top">
                            <#if readFlag != 1>
                                <textarea id="descr" class="area_500_100">${information.descr}</textarea>
                            <#else>
                                <div id="informationdescr">
                                    ${information.descr}
                                </div>
                            </#if>
                        </td>
                    </tr>
                    <tr>
                        <td height="130" valign="top">强制弹窗设置：</td>
                        <td valign="top">
                            <#if readFlag != 1>
                                <textarea id="confirmInfo" class="area_500_100">${information.confirmInfo}</textarea>
                            <#else>
                                <div id="confirmInfo1">
                                    ${information.confirmInfo}
                                </div>
                            </#if>
                        </td>
                    </tr>

                    <tr>
                        <td height="60"><label class="inputTle">是否核销：</label></td>
                        <td>
                            <#if readFlag != 1>
                                <label class="radio_lbl"><input  id="isVerification" name="isVerification"  value="0" <#if information.isVerification ==0> checked="true" </#if>  type="radio" />不核销</label>
                                <label class="radio_lbl"><input type="radio"   id="isVerification" name="isVerification" value="1" <#if information.isVerification !=0> checked="true" </#if> />核销</label>
                            <#else>
                                <#if information.isVerification==0>不核销<#else>核销</#if>
                            </#if>
                        </td>
                    </tr >
                    <tr>
                        <td height="70"><label class="inputTle">预售天数：</label></td><td>
                            <#if readFlag != 1>
                                <input id="presaleDays" name="presaleDays" class="input_240_33 digits" value="<#if information.presaleDays??>${information.presaleDays}<#else>1</#if>"/>
                            <#else>
                                ${information.presaleDays }
                            </#if>
                        </td>
                    </tr>



<#--                    <tr>-->
<#--                        <td height="300" width="120"><label class="inputTle">账号名额限购：</label></td>-->
<#--                        <#if readFlag != 1>-->
<#--                            <td>-->
<#--                                <label class="radio_lbl"><input id="accountQuantityLimitRadio" name="accountQuantityLimitRadio"-->
<#--                                                                value="-1" <#if !information??||information.accountQuantityLimit == -1> checked="true" </#if>-->
<#--                                                                type="radio"/>不限制</label>-->
<#--                                <label class="radio_lbl"><input type="radio" id="accountQuantityLimitRadio" name="accountQuantityLimitRadio"-->
<#--                                                                value="1" <#if information??&&information.accountQuantityLimit!=-1> checked="true" </#if> />限制</label>-->
<#--                                <input id="accountQuantityLimit" name="accountQuantityLimit" class="input_240_33 digits" width="20" value="${information.accountQuantityLimit}"/>-->
<#--                            </td>-->
<#--                        <#else>-->
<#--                            <td>-->
<#--                                <#if information.accountQuantityLimit==-1>-->
<#--                                    不限制-->
<#--                                <#else>-->
<#--                                    ${information.accountQuantityLimit}-->
<#--                                </#if>-->
<#--                            </td>-->
<#--                        </#if>-->
<#--                    </tr>-->
                    <tr>
                        <td height="60"><label class="inputTle">是否打票：</label></td>
                        <td>
                            <#if readFlag != 1>
                                <label class="radio_lbl"><input  id="isPrint" name="isPrint"  value="0" <#if information.isPrint != 1> checked="true" </#if>  type="radio" />不打印</label>
                                <label class="radio_lbl"><input type="radio"   id="isPrint" name="isPrint" value="1" <#if information.isPrint==1> checked="true" </#if> />打印</label>
                            <#else>
                                <#if information.isPrint==0>不打印<#else>打印</#if>
                            </#if>
                        </td>
                    </tr >
                    <tr id="alm">
                        <td height="70"  width="120"><label class="inputTle">账号限购方式：</label></td>
                        <#if readFlag != 1>
                            <td>
                                <label class="radio_lbl"><input id="accountLimitMode" name="accountLimitMode" value="1" <#if information.accountLimitMode != 2 || information.accountLimitMode != 3>
                                        checked="true" </#if> type="radio"/>单次购买</label>
                                <label class="radio_lbl"><input type="radio" id="accountLimitMode" name="accountLimitMode"
                                                                value="2" <#if information.accountLimitMode==2> checked="true" </#if> />场次</label>
                                <label class="radio_lbl"><input type="radio" id="accountLimitMode" name="accountLimitMode"
                                                                value="3" <#if information.accountLimitMode==3> checked="true" </#if> />全天</label>
                            </td>
                        <#else>
                            <td>
                                <#if information.accountLimitMode==1>
                                    单次购买
                                <#elseif information.accountLimitMode==2>
                                    场次
                                <#elseif information.accountLimitMode==3>
                                    全天
                                </#if>
                            </td>
                        </#if>
                    </tr>
                    <tr <#if enterpriseId==20>style="display:none"</#if>>
                        <td height="300"><label class="inputTle">同一证件限购：</label></td>
                        <#if readFlag != 1>
                            <td>
                                <label class="radio_lbl"><input id="repeatBuyLimitRadio" name="repeatBuyLimitRadio"
                                                                value="-1" <#if !information??||information.repeatBuyLimit == -1> checked="true" </#if>
                                                                type="radio"/>不限制</label>
                                <label class="radio_lbl"><input type="radio" id="repeatBuyLimitRadio" name="repeatBuyLimitRadio"
                                                                value="1" <#if information??&&information.repeatBuyLimit!=-1> checked="true" </#if> />限制</label>
                                <input id="repeatBuyLimit" name="repeatBuyLimit" class="input_240_33 digits" width="20" value="${information.repeatBuyLimit}"/>
                            </td>
                        <#else>
                            <td>
                                <#if information.repeatBuyLimit==-1>
                                    不限制
                                <#else>
                                    ${information.repeatBuyLimit}
                                </#if>
                            </td>
                        </#if>
                    </tr>
                    <tr <#if enterpriseId==20>style="display:none"</#if>>
                        <td height="300"><label class="inputTle">年龄限购：</label></td>
                        <#if readFlag != 1>
                            <td>
                                <label class="radio_lbl"><input id="ageLimitRadio" name="ageLimitRadio"
                                                                value="-1" <#if !information??||information.ageLimit == -1> checked="true" </#if>
                                                                type="radio"/>不限制</label>
                                <label class="radio_lbl"><input type="radio" id="ageLimitRadio" name="ageLimitRadio"
                                                                value="1" <#if information??&&information.ageLimit!=-1> checked="true" </#if> />限制</label>
                                <input id="ageLimitStart" name="ageLimitStart" class="input_120_33" width="20" value="${information.ageLimitStart}" onkeyup="this.value=this.value.replace(/\D/g,'')"  onafterpaste="this.value=this.value.replace(/\D/g,'')"/>-
                                <input id="ageLimitEnd" name="ageLimitEnd" class="input_120_33" width="20" value="${information.ageLimitEnd}" onkeyup="this.value=this.value.replace(/\D/g,'')"  onafterpaste="this.value=this.value.replace(/\D/g,'')"/>
                            </td>
                        <#else>
                            <td>
                                <#if information.ageLimit==-1>
                                    不限制
                                <#else>
                                    ${information.ageLimit}
                                </#if>
                            </td>
                        </#if>
                    </tr>
                    <tr>
                        <td height="60"><label class="inputTle">核销规则：</label></td>
                        <td>
                            <#if readFlag != 1>
                                <label class="radio_lbl"><input  id="verificationRule" name="verificationRule"  value="1" <#if information.verificationRule ==1 > checked="true" </#if>  type="radio" />按展览时间（展览有效期内均可核销）</label></br></br>
                                <label class="radio_lbl"><input type="radio"   id="verificationRule" name="verificationRule" value="2" <#if information.verificationRule==2> checked="true" </#if> />按门票日期（只能按照门票对应日期核销）</label></br></br>
                                <label class="radio_lbl"><input type="radio"   id="verificationRule" name="verificationRule" value="3" <#if information.verificationRule != 1 && information.verificationRule != 2> checked="true" </#if> />按场次核销</label>
                            <#else>
                                <#if information.verificationRule==1>按展览时间<#elseif information.verificationRule==2>按门票日期<#else>按场次时间</#if>
                            </#if>
                        </td>
                    </tr >

                    <#if readFlag != 1>
                        <tr>
                            <td></td><td  height="70">
                                <input type="hidden" id="type" value="${type}" />
                                <#if information??>
                                    <input type="hidden" id="id" value="${information.id}" />
                                    <input type="button" class="btnBg btnBg200" value="更新活动"  onclick="doSave()"/>
                                <#else>
                                    <input type="button" class="btnBg btnBg200" value="创建活动"  onclick="doSave()"/>
                                    <input type="button" class="btnBg btnBg200" value="保存草稿"  onclick="draftSave()"/>
                                </#if>
                                <input type="hidden" id="isdraft" value="0" />
                            </td>
                        </tr>
                    </#if>
                </table>
            </form>
        </div>
    </div>


</#macro>
<#macro script>
    <style>
        .erro_tip , .textarea_erro_tip{color: red;}
    </style>
    <link rel="stylesheet" href="${domainResource}/js/kindeditor-4.1.11/themes/default/default.css" />
    <link rel="stylesheet" href="${domainResource}/js/kindeditor-4.1.11/plugins/uploadimage/uploadimage.css" />
    <script  src="${domainResource}/js/kindeditor-4.1.11/kindeditor-all.js" type="text/javascript"></script>
    <script  src="${domainResource}/js/kindeditor-4.1.11/lang/zh_CN.js"  type="text/javascript"></script>
    <script type="text/javascript">
        var imgUrls=new Array();
        var infoId = '${information.id}';

        $(function(){
            formValidate($("#thisForm"));
        }) ;

        function app_upload(){
            var file = document.getElementById("fileImg1").files[0];  //file文件
            var fileName=document.getElementById("fileImg1").value;  //file的文件名
            var AllImgExt=".jpg|.png|.gif|.bmp|.jpeg|";
            var extName = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();//（把路径中的所有字母全部转换为小写）
            if(AllImgExt.indexOf(extName+"|")==-1)
            {
                ErrMsg="该文件类型不允许上传。请上传 "+AllImgExt+" 类型的文件，当前文件类型为"+extName;
                alert(ErrMsg);
                return false;
            }

            var formData = new FormData();
            formData.append("file", file);
            $.ajax({
                url: swfUploadPath+"?bwy="+getCookie("sxsession"),
                type: "POST",
                data: formData,
                /**
                 *必须false才会自动加上正确的Content-Type
                 */
                contentType: false,
                /**
                 * 必须false才会避开jQuery对 formdata 的默认处理
                 * XMLHttpRequest会对 formdata 进行正确的处理
                 */
                processData: false,
                success: function (data) {
                    var img_obj = new Function("return" + data)();//转换后的JSON对象
                    var html='<li ><a href="javascript:void(0)" ><div class="imgbox"><img id="appPic" src="${picPath}/'+img_obj.path+'" data-src="'+img_obj.path+'" data-id="-1" class="smallimg uploadApp"/></div><i class="icon delIcon" onclick="removeApp(this)" ></i></a></li>';
                    $("#upload_app").append(html);
                    $("#upload_app_add").hide();
                },
                error: function () {
                    alert("上传失败！");
                }
            });
        }
        function banner_upload(){
            var file = document.getElementById("fileImg2").files[0];  //file文件
            var fileName=document.getElementById("fileImg2").value;  //file的文件名
            var AllImgExt=".jpg|.png|.gif|.bmp|.jpeg|";
            var extName = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();//（把路径中的所有字母全部转换为小写）
            if(AllImgExt.indexOf(extName+"|")==-1)
            {
                ErrMsg="该文件类型不允许上传。请上传 "+AllImgExt+" 类型的文件，当前文件类型为"+extName;
                alert(ErrMsg);
                return false;
            }

            var formData = new FormData();
            formData.append("file", file);
            $.ajax({
                url: swfUploadPath+"?bwy="+getCookie("sxsession"),
                type: "POST",
                data: formData,
                /**
                 *必须false才会自动加上正确的Content-Type
                 */
                contentType: false,
                /**
                 * 必须false才会避开jQuery对 formdata 的默认处理
                 * XMLHttpRequest会对 formdata 进行正确的处理
                 */
                processData: false,
                success: function (data) {
                    var img_obj = new Function("return" + data)();//转换后的JSON对象
                    var html='<li ><a href="javascript:void(0)" ><div class="imgbox"><img id="bannerPic" src="${picPath}/'+img_obj.path+'" data-src="'+img_obj.path+'" data-id="-1" class="smallimg uploadBanner"/></div><i class="icon delIcon" onclick="removeBanner(this)" ></i></a></li>';
                    $("#upload_banner").append(html);
                    $("#upload_banner_add").hide();
                },
                error: function () {
                    alert("上传失败！");
                }
            });
        }

        function openFileInput(fileId){
            document.getElementById(fileId).click();
        }

        function removeApp(obj){
            if(infoId && infoId > 0){ //修改时先记录删除图片标志
                if ($(obj).parents("li").find("img").attr("data-id") == "-1"){
                    delFile($(obj).parents("li").find("img").attr("data-src"));
                } else {
                    imgUrls.push($(obj).parents("li").find("img").attr("data-src"));
                }
            }else{
                delFile($(obj).parents("li").find("img").attr("data-src"));
            }
            $(obj).parents("li").remove();
            $("#upload_app_add").show();
            $("#upload_app_add").find("input").val("");
        }
        function removeBanner(obj){
            if(infoId && infoId > 0){ //修改时先记录删除图片标志
                if ($(obj).parents("li").find("img").attr("data-id") == "-1"){
                    delFile($(obj).parents("li").find("img").attr("data-src"));
                } else {
                    imgUrls.push($(obj).parents("li").find("img").attr("data-src"));
                }
            }else{
                delFile($(obj).parents("li").find("img").attr("data-src"));
            }
            $(obj).parents("li").remove();
            $("#upload_banner_add").show();
            $("#upload_banner_add").find("input").val("");
        }

        //删除文件夹里的文件
        function delFile(src){
            $.ajax({
                dataType:'json',
                type:"post",
                url:'${rootPath}/bwy/fileOperate/delFile.htm?path='+ src ,
                success:function(retData) {

                }
            })
        }

        function draftSave(){
            $("#isdraft").val("1");
            doSave();
        }
        function doSave() {
            var flag = $("#thisForm").valid();
            if(!flag) {
                return
            }
            var data = {} ;
            if($("#id").val()) {
                data.id = $("#id").val() ;
            }
            data.type = $("#type").val() ;
            data.name = $("#name").val() ;
            data.address = $("#address").val() ;
            data.startTime = $("#startTime").val() ;
            data.timingTime = $("#timingTime").val() ;
            data.endTime = $("#endTime").val() ;
            data.presaleDays = $("#presaleDays").val() ;
            data.startSaleTime = $("#startSaleTime").val() ;
            data.endSaleTime = $("#endSaleTime").val() ;
            //data.status = $("input[name='status']:checked").val() ;
            data.isPrint = $("input[name='isPrint']:checked").val() ;
            data.isVerification = $("input[name='isVerification']:checked").val() ;
            data.scope = 2 ;
            <#if isvolunteer == 1>
            data.feature = $("input[name='feature']:checked").val() ;
            <#else >
                data.feature = $("#feature").val() ;
            </#if>
            data.appPic = $("#appPic").attr("data-src") ;
            data.bannerPic = $("#bannerPic").attr("data-src") ;
            if($("#appPic").attr("data-src") == null || $("#bannerPic").attr("data-src") == null){
                alert("请上传图片");
                return false;
            }
            data.descr = rich.html() ;
            data.confirmInfo = rich2.html() ;

            if ($("#accountQuantityLimit").val() != "") {
                data.accountLimitMode = $("input[name='accountLimitMode']:checked").val();
            }else {
                data.accountLimitMode = 1;
            }
            if ($("#accountQuantityLimit").val() == ""){
                data.accountQuantityLimit = -1;
            } else {
                data.accountQuantityLimit = $("#accountQuantityLimit").val();
            }
            if ($("#repeatBuyLimit").val() == ""){
                data.repeatBuyLimit = -1;
            } else {
                data.repeatBuyLimit = $("#repeatBuyLimit").val();
            }
            if ($("input[name='ageLimitRadio']:checked").val() == "-1"){
                data.ageLimit = -1;
            } else {
                if (isNaN($("#ageLimitStart").val()) || isNaN($("#ageLimitEnd").val())){
                    alert("请填写正确的年龄") ;
                    return;
                }
                if (parseInt($("#ageLimitStart").val()) > parseInt($("#ageLimitEnd").val())){
                    alert("请填写正确的年龄区间") ;
                    return;
                }
                data.ageLimit = $("#ageLimitStart").val() + "-" + $("#ageLimitEnd").val();
            }
            if(data.presaleDays<=0){
                alert("预售天数不能小于0") ;
                return;
            }
            data.accountQuantityLimit=-1;
            // if(data.accountQuantityLimit<=0&&$("input[name='accountQuantityLimitRadio']:checked").val()==1){
            //     alert("名额限购不能小于0") ;
            //     return;
            // }

            if(data.repeatBuyLimit<=0&&$("input[name='repeatBuyLimitRadio']:checked").val()==1){
                alert("证件限购不能小于0") ;
                return;
            }
            data.verificationRule = $("input[name='verificationRule']:checked").val() ;

            if($("#isdraft").val()=="1"){
                data.status=2;
            }
            data.enterpriseId=$("#enterpriseId").val();
            $.ajax({
                dataType:'json',
                type:'post',
                data: data,
                url : "${rootPath}/ticket/information/do-save-information.htm",
                success:function(retData) {
                    if(retData.success) {
                        if(imgUrls.length > 0){
                            for(var i=0;i<imgUrls.length;i++){
                                delFile(imgUrls[i]);
                            }
                        }
                        <#if type==2>

                        if(data.status==2){
                            if(data.feature==3||data.feature==5){
                                window.location.href="${rootPath}/ticket/information/volunteer-activity-list.htm" ;
                            }else{
                                window.location.href="${rootPath}/ticket/information/activity-list.htm" ;
                            }
                        }else{
                            <#if information??>
                            if(data.feature==3||data.feature==5){
                                window.location.href="${rootPath}/ticket/information/volunteer-activity-list.htm" ;
                            }else{
                                window.location.href="${rootPath}/ticket/information/activity-list.htm" ;
                            }
                            <#else>
                            if(data.feature==3||data.feature==5){
                                myDialog(4,"创建成功",function(){window.location.href="${rootPath}/ticket/edit-volunteer-ticket-time.htm" ;},function(){window.location.href="${rootPath}/ticket/information/activity-list.htm" ;},"创建场次","返回列表")
                            }else{
                                myDialog(4,"创建成功",function(){window.location.href="${rootPath}/ticket/edit-activity-ticket-time.htm" ;},function(){window.location.href="${rootPath}/ticket/information/activity-list.htm" ;},"创建场次","返回列表")
                            }

                            </#if>
                        }

                        <#elseif type==3>
                        window.location.href="${rootPath}/ticket/information/explain-list.htm" ;
                        <#elseif type==4>
                        window.location.href="${rootPath}/ticket/information/movie-list.htm" ;
                        <#else>
                        window.location.href="${rootPath}/ticket/information/list-information.htm" ;
                        </#if>
                    }
                    else {
                        alert(retData.errorMessage) ;
                    }
                }
            })
        }

        var rich ;
        $(function(){
            rich =  KindEditor.create('textarea[id="descr"]', {
                uploadJson : swfUploadPath+"?bwy="+getCookie("sxsession")+"&crossOrigin="+crossOrigin,
                allowPreviewEmoticons : false,
                allowImageUpload : false,
                imageSizeLimit :"5MB",
                imageUploadLimit : "5",
                filterMode :false,
                height:'400px',
                newlineTag :'br' ,
                /*items : ['source', '|', 'undo', 'redo', '|', 'preview',  'template', 'code', 'cut', 'copy', 'paste',
                        'plainpaste', 'wordpaste', '|', 'justifyleft', 'justifycenter', 'justifyright',
                        'justifyfull', 'insertorderedlist', 'insertunorderedlist', 'indent', 'outdent', 'subscript',
                        'superscript', 'clearhtml', 'quickformat', 'selectall', '|', 'fullscreen', '/',
                        'formatblock', 'fontname', 'fontsize', '|', 'forecolor', 'hilitecolor', 'bold',
                        'italic', 'underline', 'strikethrough', 'lineheight', 'removeformat', '|', 'image', 'multiimage',
                        'flash', 'media', 'insertfile', 'table', 'hr', 'emoticons', 'baidumap', 'pagebreak',
                        'anchor', 'link', 'unlink', '|','custTitle','personMsg','custFormat']*/
            });
            rich2 =  KindEditor.create('textarea[id="confirmInfo"]', {
                uploadJson : swfUploadPath+"?bwy="+getCookie("sxsession")+"&crossOrigin="+crossOrigin,
                allowPreviewEmoticons : false,
                allowImageUpload : false,
                imageSizeLimit :"5MB",
                imageUploadLimit : "5",
                filterMode :false,
                height:'400px',
                newlineTag :'br' ,
                /*items : ['source', '|', 'undo', 'redo', '|', 'preview',  'template', 'code', 'cut', 'copy', 'paste',
                        'plainpaste', 'wordpaste', '|', 'justifyleft', 'justifycenter', 'justifyright',
                        'justifyfull', 'insertorderedlist', 'insertunorderedlist', 'indent', 'outdent', 'subscript',
                        'superscript', 'clearhtml', 'quickformat', 'selectall', '|', 'fullscreen', '/',
                        'formatblock', 'fontname', 'fontsize', '|', 'forecolor', 'hilitecolor', 'bold',
                        'italic', 'underline', 'strikethrough', 'lineheight', 'removeformat', '|', 'image', 'multiimage',
                        'flash', 'media', 'insertfile', 'table', 'hr', 'emoticons', 'baidumap', 'pagebreak',
                        'anchor', 'link', 'unlink', '|','custTitle','personMsg','custFormat']*/
            });
        })


        $(document).ready(function() {
            $("#informationdescr").find("img").each(function(){
                setDetailImg($(this));
            });
            //作品详细超出的图片处理
            function setDetailImg(imgObj){
                var image=new Image();
                image.src=$(imgObj).attr("src");
                $(image).load(function() {
                    $(imgObj).attr("width","100%");
                    $(imgObj).attr("height","");
                });
            }
        });

        $(document).ready(function() {
            $('input[type=radio][name=isFeature]').change(function() {
                if (this.value == '2'){
                    <#if information==null>
                    $("#endTime").val("2099-12-31") ;
                    $("#endSaleTime").val("2099-12-31 23:59:59") ;
                    $("#endTime").attr("readOnly",true);
                    $("#endSaleTime").attr("readOnly",true);
                    </#if>
                }else {
                    <#if information==null>
                    $("#endTime").val("") ;
                    $("#endSaleTime").val("") ;
                    $("#endTime").attr("readOnly",false);
                    $("#endSaleTime").attr("readOnly",false);
                    </#if>
                }

            });

            <#if !information??>
            $("#accountQuantityLimit").attr("readOnly",true);
            $("#accountQuantityLimit").val("") ;
            $("#repeatBuyLimit").attr("readOnly",true);
            $("#repeatBuyLimit").val("") ;
            $("#ageLimitStart").attr("readOnly",true);
            $("#ageLimitStart").val("") ;
            $("#ageLimitEnd").attr("readOnly",true);
            $("#ageLimitEnd").val("") ;
            $("#alm").hide();
            <#else >
            <#if information??&&information.accountQuantityLimit == -1>
            $("#accountQuantityLimit").attr("readOnly",true);
            $("#accountQuantityLimit").val("") ;
            $("#alm").hide();
            <#else>
            $("#accountQuantityLimit").attr("required",true);
            $("#alm").show();
            </#if>

            <#if information??&&information.repeatBuyLimit == -1>
            $("#repeatBuyLimit").attr("readOnly",true);
            $("#repeatBuyLimit").val("") ;
            <#else>
            $("#repeatBuyLimit").attr("required",true);
            </#if>
            <#if information??&&information.ageLimit == -1>
            $("#ageLimitStart").attr("readOnly",true);
            $("#ageLimitStart").val("") ;
            $("#ageLimitEnd").attr("readOnly",true);
            $("#ageLimitEnd").val("") ;
            <#else>
            $("#ageLimitStart").attr("required",true);
            $("#ageLimitEnd").attr("required",true);
            </#if>
            </#if>
            <#--$('input[type=radio][name=accountQuantityLimitRadio]').change(function() {-->
            <#--    if (this.value == '-1'){-->
            <#--        $("#accountQuantityLimit").val("") ;-->
            <#--        $("#accountQuantityLimit").attr("readOnly",true);-->
            <#--        $("#accountQuantityLimit").attr("required",false);-->
            <#--        $("#alm").hide();-->
            <#--    }else {-->
            <#--        $("#accountQuantityLimit").attr("readOnly",false);-->
            <#--        $("#accountQuantityLimit").attr("required",true);-->
            <#--        $("#alm").show();-->
            <#--        <#if information??&&information.accountQuantityLimit != -1>-->
            <#--        $("#accountQuantityLimit").val("${information.accountQuantityLimit}") ;-->
            <#--        </#if>-->
            <#--    }-->
            <#--});-->

            $('input[type=radio][name=repeatBuyLimitRadio]').change(function() {
                if (this.value == '-1'){
                    $("#repeatBuyLimit").val("") ;
                    $("#repeatBuyLimit").attr("readOnly",true);
                    $("#repeatBuyLimit").attr("required",false);
                }else {
                    $("#repeatBuyLimit").attr("readOnly",false);
                    $("#repeatBuyLimit").attr("required",true);
                    <#if information??&&information.repeatBuyLimit != -1>
                    $("#repeatBuyLimit").val("${information.repeatBuyLimit}") ;
                    </#if>
                }
            });

            $('input[type=radio][name=ageLimitRadio]').change(function() {
                if (this.value == '-1'){
                    $("#ageLimitStart").val("") ;
                    $("#ageLimitEnd").val("") ;
                    $("#ageLimitStart").attr("readOnly",true);
                    $("#ageLimitStart").attr("required",false);
                    $("#ageLimitEnd").attr("readOnly",true);
                    $("#ageLimitEnd").attr("required",false);
                }else {
                    $("#ageLimitStart").attr("readOnly",false);
                    $("#ageLimitStart").attr("required",true);
                    $("#ageLimitEnd").attr("readOnly",false);
                    $("#ageLimitEnd").attr("required",true);
                    <#if information??&&information.ageLimit != -1>
                    $("#ageLimitStart").val("${information.ageLimitStart}") ;
                    $("#ageLimitEnd").val("${information.ageLimitEnd}") ;
                    </#if>
                }
            });
        });

    </script>
</#macro>