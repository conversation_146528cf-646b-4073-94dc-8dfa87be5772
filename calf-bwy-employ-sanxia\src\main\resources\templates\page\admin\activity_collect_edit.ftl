<#include "layout.ftl"/>
<#macro content>
    <#include "/head.ftl" />
    <div class="main">
        <!--弹出层-->
        <div id="mask"></div>
        <div class="maskCnt">
            <div class="mk_box">
                <a href="javascript:void(0)" class="mk_del"><i class="iconList"></i></a>
                <div class="mk_bx_cnt">
                </div>
            </div>
        </div>
        <#include "left_menu.ftl" />
        <div class="right">
            <div class="pisitionDiv">
                <span class="pn_tle">机构活动申请</span>
                <a href="/activity/collect/loadListPage.htm" class="return pull-right"><i
                            class="smallIconList op_return_icon"></i>返回</a>
            </div>

            <form method="post" enctype="multipart/form-data" id="thisForm">
                <div class="edit_section">
                    <table cellpadding="0" cellspacing="0" border="0" width="100%" class="edit_tbl">
                        <tr>
                            <td width="160"><label class="inputTle"><span class="required">*</span>机构活动申请名称：</label>
                            </td>
                            <td>
                                <input id="name" name="name" class="input_240_33"
                                       value="${(activityCollect.name)!}" placeholder="输入申请名称"/>
                                <input type="hidden" id="id" value="${activityCollect.id!}"/>
                            </td>
                        </tr>
                        <tr>
                            <td width="100" height="70"><label class="inputTle"><span class="required">*</span>所属场馆：</label></td>
                            <td>
                                <select class="input_240_33 " id="enterpriseId" name="enterpriseId" data-msg-required="不能为空">
                                    <option value="">请选择</option>
                                    <option value="1" <#if (activityCollect.enterpriseId)?? && activityCollect.enterpriseId == "1">selected</#if>>重庆中国三峡博物馆</option>
                                    <option value="2" <#if (activityCollect.enterpriseId)?? && activityCollect.enterpriseId == "2">selected</#if>>重庆宋庆龄纪念馆</option>
                                    <option value="3" <#if (activityCollect.enterpriseId)?? && activityCollect.enterpriseId == "3">selected</#if>>三峡文物科技保护基地</option>
                                    <option value="4" <#if (activityCollect.enterpriseId)?? && activityCollect.enterpriseId == "4">selected</#if>>重庆白鹤梁水下博物馆</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td width="100" height="70"><label class="inputTle"><span class="required">*</span>机构活动类型：</label></td>
                            <td>
                                <select class="input_240_33 " id="type" name="type" data-msg-required="不能为空">
                                    <option value="">请选择</option>
                                    <option value="1" <#if (activityCollect.type)?? && activityCollect.type == "1">selected</#if>>巡展</option>
                                    <option value="2" <#if (activityCollect.type)?? && activityCollect.type == "2">selected</#if>>教育活动</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td height="70"><label class="inputTle"><span class="required">*</span>活动截至日期：</label></td>
                            <td>
                                <input class="input_240_33 Wdate" id="deadlineDate" name="deadlineDate"
                                       value="${(activityCollect.deadlineDate?string('yyyy-MM-dd'))!}"
                                       onFocus="WdatePicker({dateFmt:'yyyy-MM-dd', minDate:'%y-%M-%d'})"/>
                            </td>
                        </tr>
                        <tr>
                            <td height="120" width="125" valign="top">
                                <label class="inputTle lh18">
                                    <span class="required">*</span>图片：
                                </label>
                            </td>
                            <td valign="top">
                                <ul class="upload_img ruku_certificate" id="upload_app">
                                    <li class="upload_add" id="upload_app_add"
                                        <#if activityCollect.pic?? && activityCollect.pic != "">style="display:none"</#if>>
                                        <a href="javascript:void(0)" onclick="openFileInput('fileImg1');">+</a>
                                        <input type="file" class="hidden" id="fileImg1" onchange="uploadApp()"/>
                                    </li>
                                    <#if activityCollect.pic?? && activityCollect.pic != "">
                                        <li><a href="javascript:void(0)">
                                                <div class="imgbox"><img id="picture"
                                                                         src="${picPath}${activityCollect.pic}"
                                                                         data-src='${activityCollect.pic}'
                                                                         class="smallimg uploadApp"/></div>
                                                <i class="icon delIcon" onclick="removeApp(this)"></i></a></li>
                                    </#if>
                                    <span style="color: #666; margin-left: 10px;line-height: 100px;">图片尺寸：600*345px</span>
                                </ul>

                            </td>
                        </tr>
                        <tr>
                            <td>
                                <label class="inputTle lh18"><span class="required">*</span>简介：</label>
                            </td>
                            <td valign="top">
                                <#if readFlag != 1>
                                    <textarea id="content" class="area_500_100">${activityCollect.content}</textarea>
                                <#else>
                                    <div id="content">
                                        ${activityCollect.content}
                                    </div>
                                </#if>
                            </td>
                        </tr>
                        <tr>
                            <td></td>
                            <td height="70">
                                <#if activityCollect??&&activityCollect.id gt 0>
                                    <input type="button" class="btnBg btnBg200" value="更新" onclick="doSave()"/>
                                <#else>
                                    <input type="button" class="btnBg btnBg200" value="新建" onclick="doSave()"/>
                                </#if>
                            </td>
                        </tr>
                    </table>
                </div>
            </form>
        </div>
    </div>
</#macro>
<link rel="stylesheet" href="${domainResource}/js/kindeditor-4.1.11/themes/default/default.css"/>
<link rel="stylesheet" href="${domainResource}/js/kindeditor-4.1.11/plugins/uploadimage/uploadimage.css"/>
<script src="${domainResource}/js/kindeditor-4.1.11/kindeditor-all.js" type="text/javascript"></script>
<script src="${domainResource}/js/kindeditor-4.1.11/lang/zh_CN.js" type="text/javascript"></script>
<#macro script>
    <script type="text/javascript">
        function openFileInput(fileId) {
            document.getElementById(fileId).click();
        }

        var rich;
        $(function () {
            rich = KindEditor.create('textarea[id="content"]', {
                uploadJson: swfUploadPath+"?bwy="+getCookie("sxsession") + "&crossOrigin=" + crossOrigin,
                allowPreviewEmoticons: false,
                allowImageUpload: false,
                imageSizeLimit: "5MB",
                imageUploadLimit: "5",
                filterMode: false,
                height: '400px',
                newlineTag: 'br',
                /*items : ['source', '|', 'undo', 'redo', '|', 'preview',  'template', 'code', 'cut', 'copy', 'paste',
                        'plainpaste', 'wordpaste', '|', 'justifyleft', 'justifycenter', 'justifyright',
                        'justifyfull', 'insertorderedlist', 'insertunorderedlist', 'indent', 'outdent', 'subscript',
                        'superscript', 'clearhtml', 'quickformat', 'selectall', '|', 'fullscreen', '/',
                        'formatblock', 'fontname', 'fontsize', '|', 'forecolor', 'hilitecolor', 'bold',
                        'italic', 'underline', 'strikethrough', 'lineheight', 'removeformat', '|', 'image', 'multiimage',
                        'flash', 'media', 'insertfile', 'table', 'hr', 'emoticons', 'baidumap', 'pagebreak',
                        'anchor', 'link', 'unlink', '|','custTitle','personMsg','custFormat']*/
            });
        })
        function uploadApp() {
            var file = document.getElementById("fileImg1").files[0];  //file文件
            var fileName = document.getElementById("fileImg1").value;  //file的文件名
            var AllImgExt = ".jpg|.png|.gif|.bmp|.jpeg|";
            var extName = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();//（把路径中的所有字母全部转换为小写）
            if (AllImgExt.indexOf(extName + "|") == -1) {
                ErrMsg = "该文件类型不允许上传。请上传 " + AllImgExt + " 类型的文件，当前文件类型为" + extName;
                alert(ErrMsg);
                return false;
            }
            var formData = new FormData();
            formData.append("file", file);
            $.ajax({
                url: swfUploadPath+"?bwy="+getCookie("sxsession"),
                type: "POST",
                data: formData,
                contentType: false,
                processData: false,
                success: function (data) {
                    var img_obj = new Function("return" + data)();//转换后的JSON对象
                    var html = '<li ><a href="javascript:void(0)" ><div class="imgbox"><img id="appPic" src="${picPath}/' + img_obj.path + '" data-src="' + img_obj.path + '" data-id="-1" class="smallimg uploadApp"/></div><i class="icon delIcon" onclick="removeApp(this)" ></i></a></li>';
                    $("#upload_app").append(html);
                    $("#upload_app_add").hide();
                },
                error: function () {
                    alert("上传失败！");
                }
            });
        }

        function removeApp(obj) {
            var $li = $(obj).closest('li');
            var imageSrc = $li.find('img').attr('data-src');
            // 删除服务器上的文件
            $.ajax({
                url: '${rootPath}/bwy/fileOperate/delFile.htm',
                type: 'POST',
                data: {path: imageSrc},
                dataType: 'json',
                success: function (response) {
                    if (response.success) {
                        // 从 DOM 中移除图片元素
                        $li.remove();

                        // 清除隐藏的 input
                        $('input[name="images"]').remove();

                        // 显示上传按钮
                        $('#upload_app_add').show();

                        // 重置文件输入
                        $('#fileImg1').val('');

                        // 如果是在编辑模式下
                        if (infoId && infoId > 0) {
                            // 将删除的图片URL添加到待删除列表中(如果需要的话)
                            if (typeof imgUrls !== 'undefined') {
                                imgUrls.push(imageSrc);
                            }
                        }
                    } else {
                        alert('删除图片失败: ' + (response.message || '未知错误'));
                    }
                },
                error: function () {
                    alert('删除图片时发生错误,请稍后再试');
                }
            });
        }

        function doSave() {
            var flag = $("#thisForm").valid();
            if (!flag) {
                return;
            }

            if (!$("#name").val()) {
                myDialog(1, "请输入名称");
                return;
            }
            if (!$("#enterpriseId").val()) {
                myDialog(1, "请选择所属场馆");
                return;
            }
            if (!$("#type").val()) {
                myDialog(1, "请选择机构活动类型");
                return;
            }
            if (!$(".uploadApp").attr("data-src")) {
                myDialog(1, "请上传图片");
                return;
            }

            if (!$("#deadlineDate").val()) {
                myDialog(1, "请选择活动截至日期");
                return;
            }
            if (!rich.html()) {
                myDialog(1, "请选择活动截至日期");
                return;
            }

            var data = {};
            data.id = $("#id").val();
            data.name = $("#name").val();
            data.enterpriseId = $("#enterpriseId").val();
            data.type = $("#type").val();
            data.deadlineDate= $("#deadlineDate").val();
            data.pic = $(".uploadApp").attr("data-src");
            data.content = rich.html();
            $.ajax({
                dataType: 'json',
                type: 'post',
                data: data,
                url: "doSave.htm",
                success: function (retData) {
                    if (retData.success) {
                        window.location.href = "/activity/collect/loadListPage.htm";
                    } else {
                        myDialog(1, retData.errorMessage);
                    }
                }
            });
        }

    </script>
    <style>
        .required {
            color: red;
        }

        .upload_img {
            list-style: none;
            padding: 0;
            display: inline-block;
        }

        .upload_img li, .imgbox {
            width: 100px;
            height: 100px;
            overflow: hidden;
        }

        .upload_add {
            width: 100px;
            height: 100px;
            border: 1px dashed #ccc;
            text-align: center;
            line-height: 100px;
            cursor: pointer;
        }

        .upload_add a {
            font-size: 24px;
            color: #999;
            text-decoration: none;
        }


        .smallimg {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .delIcon {
            position: absolute;
            top: -5px;
            right: -5px;
            cursor: pointer;
            background: #fff;
            border-radius: 50%;
            padding: 2px;
        }

        .ruku_certificate li {
            padding-bottom: 0px;
        }

        .input_240_33 {
            width: 230px;
            height: 28px;
            font-size: 14px;
            line-height: 31px;
            border: 1px #aaaeb3 solid;
            background: #fff;
            border-radius: 5px;
            -webkit-border-radius: 5px;
            -moz-border-radius: 5px;
            -o-border-radius: 5px;
        }
    </style>
</#macro>