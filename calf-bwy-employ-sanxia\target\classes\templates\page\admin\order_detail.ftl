<#include "layout.ftl"/>
<#macro content>
<#include "/head.ftl" />
<div class="main">
	<!--弹出层-->
    <div id="mask"></div>
    <div class="maskCnt">
    	<div class="mk_box">
    		<a href="javascript:void(0)" class="mk_del"><i class="iconList"></i></a>
            <div class="mk_bx_cnt">
           </div>
        </div>
    </div>
	<!--#弹出层#-->
   <#include "left_menu.ftl" />
	<div class="right">
		<div class="pisitionDiv">
        	<div class="pn_tle">订单详情</div>
            <a href="${rootPath}/ticket/order/list.htm" class="return pull-right"><i class="smallIconList op_return_icon"></i>返回</a>
        </div>
        
         <div class="detailBg">
        	<div class="blank_20"></div>
            <table cellpadding="0" cellspacing="0" width="100%" align="center" class="orderDetailTbl" >
                <tr><td>展览名称：</td><td ><strong>${order.informationName}</strong></td>
                <td >订单编号：</td><td ><strong>${order.orderSerial}</strong>
                </tr>
                <tr>
                		<td  width="70">门票时间：</td><td  width="35%"><strong>
                		<#if order.viewDate>
                		 ${order.viewDate?string("yyyy-MM-dd")} (${time.name})
                		 </#if>
                		 </strong></td>
                		<td width="70" >下单时间：</td><td ><strong>${order.buyTime?string('yyyy-MM-dd HH:mm:ss')}</strong></td>	
                </tr>
                 <tr>
                    <td >付款方式：</td><td><strong><#if order.payWay==1>
                        		微信
                        	</#if>
                        	<#if order.payWay==2>
                        		支付宝
                        	</#if>
                        	<#if order.payWay==3>
                        		现金
                        	</#if>
                        	
                        	 <#if order.payWay==4>余额</#if>
	                   		<#if order.payWay==5>银联</#if>
	                   		<#if order.payWay==6>银联刷卡</#if>
	                   		<#if order.payWay==7>体验劵</#if>
	                   		<#if order.payWay==8>携程</#if>

                        	</strong></td>
                </tr>
                <tr>
                	<td >订单状态：</td>
                	<td>
                		<#if order.status==1>待付款</#if>
                		<#if order.status==2>已付款</#if>
                		<#if order.status==3>交易关闭</#if>
                		
                		<#if order.closeReson??  && order.closeReson != ""> &nbsp;(${order.closeReson})</#if>
                	</td>
                	<td >手机：</td><td ><strong>${order.mobile}</strong></td>
               	</tr>
               	 <tr>
                    <td >门票数量：</td><td ><strong>${order.quantity}张</strong></td>
                    <td>实付金额：</td><td><strong>￥${order.payPrice?string('0.00')}</strong></td>
                </tr>
                <tr>
                	<td  valign="top">取票码：</td><td valign="top">${order.takeTicketCode}</td>
                	<td >
                		 <span   id="ewm" ></span>
                		 <input type="hidden" id="sqCode" value="${order.takeTicketCode}" />
                	</td>
                </tr>
				<tr>
					<td  valign="top">订单备注：</td><td valign="top">${order.remark}</td>
					<td >
						<span   id="ewm" ></span>
						<input type="hidden" id="sqCode" value="${order.remark}" />
					</td>
				</tr>
				<tr><td>售票员：</td><td>${order.operName}</td></tr>
            </table>
            <div class="blank_30"></div>
            
            
            <div class="rt_position2" >用户信息</div>
            <div class="blank_10"></div>
            
            <ul class="re_tab">
            <#list list as item>
            	<li>  
	             <table cellpadding="0" cellspacing="0" width="100%" align="center" class="orderDetailTbl" >
	                <tr>
	                	<td width="60" data-id="${item.id}">姓名：</td>
	                	<td width="100"><strong>${item.name}</strong></td>
	                	<td width="70">证件号码：</td>
	                	<td width="230"><strong>${item.idcard}</strong></td> 
	                    <td width="70">证件类型：</td>
	                    <td width="230">
	                    <strong>
		                    <#if item.cardType == 1>
		                    	身份证
		                    <#elseif item.cardType == 5>
		                    	护照
		                    <#elseif item.cardType == 6>
		                    	台胞证
		                    <#elseif item.cardType == 7>
		                    	港澳通行证
		                    <#elseif item.cardType == 9>
		                    	社保卡
							<#elseif item.cardType == 11>
								外国人永久居留证
		                    </#if>
	                    </strong>
	                    </td>
	                    <td width="60">取票状态:</td>
	                    <td width="80">
	                    	<strong>
	                    		<#if item.isTakeTicket==1>已取票</#if>
	                    		<#if item.isTakeTicket==2>未取票</#if>
	                    	</strong>
	                    </td>
	                    <td width="60">取票时间:</td>
	                    <td width="120"><strong><#if item.isTakeTicket==1>${item.updateTime?string('MM-dd HH:mm')}</#if></strong></td>
	                </tr>
	                <tr>
						<td width="60">年龄:</td>
						<td width="100"><strong>${item.age}</strong></td>
	                	<td width="60">核销状态:</td>
	                	<td width="100"><strong><#if item.isVerification == 2>未核销<#else><#if item.verificationType==1>电子核销</#if><#if item.verificationType==2>门票核销</#if></#if></strong></td>
	               		<td width="60">联系方式:</td><td width="100"><strong>${item.mobile}</strong></td>
	               		<!--<td width="60">赠票状态</td><td width="100"><strong><#if item.isGet == 1>已转赠<#else>未转赠</#if></strong></td>-->
	               		<td width="60">票价：</td><td><strong>
	                		 	<#if item.ticketPrice??>
	                		 	￥${item.ticketPrice?string('0.00')}
	                		 	</#if>
	                		 </strong></td>
	                	<#if order.status == 2><td width="60">检票码</td><td width="60"><strong><span style="display: inline-block; margin-right: 20px"  id="ewm_${item.verificationCode}" class=" verification"  onclick="maxVerificationCode('${item.verificationCode}')" data-code="${item.verificationCode}"></span></strong></td></#if>
	                	<#if item.refundStatus == 1><td width="60"></td><td width="60"><strong class="font_red">退款成功</strong></td></#if>

	                </tr>
	               <#if ticketList?size gt 0> 
	               	<tr><td width="60">座位号</td><td width="100"><strong>${ticketList[item_index].ticketSign}</strong></td></tr></#if>
	             </table>
	             </li>
            </#list>
            </ul>
            <div class="blank_30"></div>
        </div>
    </div>
</div>


</#macro>
<#macro script>
<script type="text/javascript">
<#if order.takeTicketCode??>
$(function(){
	var qrcode = new QRCode('ewm',{
	    width: 80,
	    height: 80
	});
	qrcode.makeCode($("#sqCode").val());
})
</#if>

$(function(){
	$(".verification").each(function(){
		var verificationCode = $(this).attr("data-code");
		if(verificationCode){
			var qrcode = new QRCode('ewm_'+verificationCode,{
			    width: 60,
			    height: 60
			});
			qrcode.makeCode(verificationCode);
		}
		
	})
})


function maxVerificationCode(id){
	var html="<strong><span style=\"display: inline-block; margin-right: 20px\"  id=\"ewm_max\" class=\" verification\"></span></strong>";
	myDialog(1,html);
	var qrcode = new QRCode('ewm_max',{
	    width: 210,
	    height: 210
	});
	qrcode.makeCode(id);
}
</script>
</#macro>