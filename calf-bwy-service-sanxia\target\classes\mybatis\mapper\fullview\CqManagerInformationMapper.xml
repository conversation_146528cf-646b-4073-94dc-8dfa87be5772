<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">  
<mapper namespace="com.taoart.calf.bwy.mapper.fullview.CqManagerInformationMapper">
  <resultMap type="cqManagerInformation" id="result-map-cqManagerInformation">
      <result column="id" javaType="int" jdbcType="INTEGER" property="id"/>
      <result column="enterprise_id" javaType="int" jdbcType="INTEGER" property="enterpriseId"/>
      <result column="name" javaType="String" jdbcType="VARCHAR" property="name"/>
      <result column="url" javaType="String" jdbcType="VARCHAR" property="url"/>
      <result column="cover_path" javaType="String" jdbcType="VARCHAR" property="coverPath"/>
      <result column="hot_count" javaType="int" jdbcType="INTEGER" property="hotCount"/>
      <result column="resource_path" javaType="String" jdbcType="VARCHAR" property="resourcePath"/>
      <result column="create_time" javaType="java.util.Date" jdbcType="TIMESTAMP" property="createTime"/>
      <result column="update_time" javaType="java.util.Date" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>
  
  <!-- ================================================================== -->
  <!--                             插入一条记录                                                                          -->
  <!-- ================================================================== -->
  <insert id="insert" parameterType="cqManagerInformation" useGeneratedKeys="true" keyProperty="id">
  <![CDATA[
  INSERT INTO cq_manager_information(
	  enterprise_id,
	  name,
      url,
      cover_path,
      resource_path,
	  create_time,
	  update_time
  )VALUES(
	  #{enterpriseId,jdbcType=INTEGER},
	  #{name,jdbcType=VARCHAR},
	  #{url,jdbcType=VARCHAR},
	  #{coverPath,jdbcType=VARCHAR},
	  #{resourcePath,jdbcType=VARCHAR},
      now(),
      now()
  )
  ]]>
  </insert>
  
  <!-- ================================================================== -->
  <!--                             按主键查询                                                                               -->
  <!-- ================================================================== -->
  <select id="selectById" parameterType="int" resultMap="result-map-cqManagerInformation">
  SELECT * FROM cq_manager_information WHERE ID = #{id, jdbcType=INTEGER}
  </select>
  
  <!-- ================================================================== -->
  <!--                                  删除                                                                                  -->
  <!-- ================================================================== -->
  <delete id="delete" parameterType="int">
  DELETE FROM cq_manager_information WHERE ID = #{id, jdbcType=INTEGER}
  </delete>
  
  <!-- ================================================================== -->
  <!--                                   更新                                                                               -->
  <!-- ================================================================== -->
  <update id="update" parameterType="cqManagerInformation">
  <![CDATA[
  UPDATE cq_manager_information SET
	  id=#{id,jdbcType=INTEGER},
	  enterprise_id=#{enterpriseId,jdbcType=INTEGER},
	  name=#{name,jdbcType=VARCHAR},
	  url=#{url,jdbcType=VARCHAR},
	  cover_path=#{coverPath,jdbcType=VARCHAR},
      resource_path=#{resourcePath,jdbcType=VARCHAR},
	  update_time=now()
  WHERE ID=#{id, jdbcType=INTEGER}
  ]]>
  </update>

  <!-- 通过条件查询 -->
  <select id="listAll"  parameterType="cqManagerInformation" resultMap="result-map-cqManagerInformation">
  	 select * from cq_manager_information
  	 where 1 = 1
      <if test="enterpriseId != null and enterpriseId > 0">
        and  enterprise_id = #{enterpriseId}
      </if>
  </select>
  
   <!--  分页查询 -->
   <select id="listByPage"  parameterType="pageBean" resultMap="result-map-cqManagerInformation">
       SELECT i.*,
       (SELECT COUNT(*)
       FROM cq_manager_hot h
       WHERE h.information_id = i.id) AS hot_count
       FROM cq_manager_information i
       WHERE 1=1
       <include refid="selParams"/>
       ORDER BY i.create_time DESC
       LIMIT #{pageSize} OFFSET #{startItem}
   </select>
  
  
  <select id="findByPageCount"  parameterType="pageBean" resultType="int">
  	 select count(i.id) from cq_manager_information i where 1=1   <include refid="selParams"/>
  </select>

    <sql id="selParams">
        <if test="params.enterpriseId != null and params.enterpriseId != '' and params.enterpriseId > 0">
            and i.enterprise_id = #{params.enterpriseId}
        </if>
        <if test="params.name != null and params.name != '' ">
            and i.name  LIKE concat('%',#{params.name},'%')
        </if>
    </sql>
  
</mapper> 